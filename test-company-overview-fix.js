// Simple test to verify the SQL parameter mapping in CompanyOverviewProcessorV2
const sql = `
  UPDATE companies SET
    -- Core Company Information
    industry = COALESCE($1, industry),
    company_type = COALESCE($2, company_type),
    business_model = COALESCE($3, business_model),
    founded_year = COALESCE($4, founded_year),
    
    -- Investment & Strategy
    investment_focus = COALESCE($5, investment_focus),
    investment_strategy_mission = COALESCE($6, investment_strategy_mission),
    investment_strategy_approach = COALESCE($7, investment_strategy_approach),
    
    -- Contact Information (AI-updated fields)
    main_phone = COALESCE($8, main_phone),
    secondary_phone = COALESCE($9, secondary_phone),
    main_email = COALESCE($10, main_email),
    secondary_email = COALESCE($11, secondary_email),
    company_linkedin = COALESCE($12, company_linkedin),
    twitter = COALESCE($13, twitter),
    facebook = COALESCE($14, facebook),
    instagram = COALESCE($15, instagram),
    youtube = COALESCE($16, youtube),
    
    -- Address Information (AI-updated headquarters fields)
    headquarters_address = COALESCE($17, headquarters_address),
    headquarters_city = COALESCE($18, headquarters_city),
    headquarters_state = COALESCE($19, headquarters_state),
    headquarters_zipcode = COALESCE($20, headquarters_zipcode),
    headquarters_country = COALESCE($21, headquarters_country),
    additional_address = COALESCE($22, additional_address),
    additional_city = COALESCE($23, additional_city),
    additional_state = COALESCE($24, additional_state),
    additional_zipcode = COALESCE($25, additional_zipcode),
    additional_country = COALESCE($26, additional_country),
    office_locations = COALESCE($27, office_locations),
    
    -- Company Metrics
    fund_size = COALESCE($28, fund_size),
    aum = COALESCE($29, aum),
    number_of_properties = COALESCE($30, number_of_properties),
    number_of_offices = COALESCE($31, number_of_offices),
    number_of_employees = COALESCE($32, number_of_employees),
    annual_revenue = COALESCE($33, annual_revenue),
    net_income = COALESCE($34, net_income),
    ebitda = COALESCE($35, ebitda),
    profit_margin = COALESCE($36, profit_margin),
    market_capitalization = COALESCE($37, market_capitalization),
    market_share_percentage = COALESCE($38, market_share_percentage),
    
    -- Financial Information
    balance_sheet_strength = COALESCE($39, balance_sheet_strength),
    funding_sources = COALESCE($40, funding_sources),
    recent_capital_raises = COALESCE($41, recent_capital_raises),
    typical_debt_to_equity_ratio = COALESCE($42, typical_debt_to_equity_ratio),
    development_fee_structure = COALESCE($43, development_fee_structure),
    credit_rating = COALESCE($44, credit_rating),
    dry_powder = COALESCE($45, dry_powder),
    annual_deployment_target = COALESCE($46, annual_deployment_target),
    
    -- Investment & Fund Information
    investment_vehicle_type = COALESCE($47, investment_vehicle_type),
    active_fund_name_series = COALESCE($48, active_fund_name_series),
    fund_size_active_fund = COALESCE($49, fund_size_active_fund),
    fundraising_status = COALESCE($50, fundraising_status),
    lender_type = COALESCE($51, lender_type),
    annual_loan_volume = COALESCE($52, annual_loan_volume),
    lending_origin = COALESCE($53, lending_origin),
    portfolio_health = COALESCE($54, portfolio_health),
    
    -- Partnership & Leadership
    partnerships = COALESCE($55, partnerships),
    key_equity_partners = COALESCE($56, key_equity_partners),
    key_debt_partners = COALESCE($57, key_debt_partners),
    board_of_directors = COALESCE($58, board_of_directors),
    key_executives = COALESCE($59, key_executives),
    founder_background = COALESCE($60, founder_background),
    
    -- Market Positioning & Strategy
    market_cycle_positioning = COALESCE($61, market_cycle_positioning),
    urban_vs_suburban_preference = COALESCE($62, urban_vs_suburban_preference),
    sustainability_esg_focus = COALESCE($63, sustainability_esg_focus),
    technology_proptech_adoption = COALESCE($64, technology_proptech_adoption),
    adaptive_reuse_experience = COALESCE($65, adaptive_reuse_experience),
    regulatory_zoning_expertise = COALESCE($66, regulatory_zoning_expertise),
    
    -- Corporate Structure
    corporate_structure = COALESCE($67, corporate_structure),
    parent_company = COALESCE($68, parent_company),
    subsidiaries = COALESCE($69, subsidiaries),
    stock_ticker_symbol = COALESCE($70, stock_ticker_symbol),
    stock_exchange = COALESCE($71, stock_exchange),
    
    -- Business Information
    products_services_description = COALESCE($72, products_services_description),
    target_customer_profile = COALESCE($73, target_customer_profile),
    major_competitors = COALESCE($74, major_competitors),
    unique_selling_proposition = COALESCE($75, unique_selling_proposition),
    industry_awards_recognitions = COALESCE($76, industry_awards_recognitions),
    company_history = COALESCE($77, company_history),
    
    -- Transaction & Portfolio Data
    transactions_completed_last_12m = COALESCE($78, transactions_completed_last_12m),
    total_transaction_volume_ytd = COALESCE($79, total_transaction_volume_ytd),
    deal_count_ytd = COALESCE($80, deal_count_ytd),
    average_deal_size = COALESCE($81, average_deal_size),
    portfolio_size_sqft = COALESCE($82, portfolio_size_sqft),
    portfolio_asset_count = COALESCE($83, portfolio_asset_count),
    role_in_previous_deal = COALESCE($84, role_in_previous_deal),
    
    -- Relationship & Pipeline Data
    internal_relationship_manager = COALESCE($85, internal_relationship_manager),
    last_contact_date = COALESCE($86, last_contact_date),
    pipeline_status = COALESCE($87, pipeline_status),
    recent_news_sentiment = COALESCE($88, recent_news_sentiment),
    
    -- Data Quality & Processing
    data_source = COALESCE($89, data_source),
    data_confidence_score = COALESCE($90, data_confidence_score),
    quarterly_earnings_link = COALESCE($91, quarterly_earnings_link),
    
    -- Processing metadata
    updated_at = NOW(),
    last_updated_timestamp = NOW(),
    llm_used = $92,
    llm_response = $93,
    llm_token_usage = $94
    
  WHERE company_id = $95
`;

// Count the parameters
const paramMatches = sql.match(/\$(\d+)/g);
const paramNumbers = paramMatches ? paramMatches.map(match => parseInt(match.slice(1))) : [];
const uniqueParams = [...new Set(paramNumbers)].sort((a, b) => a - b);

console.log('Parameter numbers found in SQL:', uniqueParams);
console.log('Total parameters:', uniqueParams.length);
console.log('Expected parameters: 95 (1-95)');

// Check for gaps
let hasGaps = false;
for (let i = 1; i <= 95; i++) {
  if (!uniqueParams.includes(i)) {
    console.log(`Missing parameter: $${i}`);
    hasGaps = true;
  }
}

if (!hasGaps) {
  console.log('✅ All parameters are present and correctly numbered');
} else {
  console.log('❌ There are gaps in parameter numbering');
}

// Check for duplicates
const duplicates = paramNumbers.filter((item, index) => paramNumbers.indexOf(item) !== index);
if (duplicates.length > 0) {
  console.log('❌ Duplicate parameters found:', [...new Set(duplicates)]);
} else {
  console.log('✅ No duplicate parameters found');
} 
