#!/usr/bin/env ts-node

import { pool } from '../src/lib/db'
import fs from 'fs'
import path from 'path'

/**
 * Run the duplicate detection system migration
 * This script creates normalized tables and populates them with existing data
 */
async function runDuplicateDetectionMigration() {
  const client = await pool.connect()
  
  try {
    console.log('🚀 Starting duplicate detection system migration...')
    
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '../sql-files/create_duplicate_detection_tables.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf-8')
    
    console.log('📄 Executing migration SQL...')
    
    // Execute the migration within a transaction
    await client.query('BEGIN')
    
    try {
      // Execute the migration SQL
      await client.query(migrationSQL)
      
      await client.query('COMMIT')
      
      console.log('✅ Migration completed successfully!')
      
      // Get statistics on the populated data
      const companyStats = await client.query('SELECT COUNT(*) as count FROM company_normalized_data')
      const contactStats = await client.query('SELECT COUNT(*) as count FROM contact_normalized_data')
      
      console.log(`📊 Migration Statistics:`)
      console.log(`   Companies normalized: ${companyStats.rows[0].count}`)
      console.log(`   Contacts normalized: ${contactStats.rows[0].count}`)
      
      // Check for immediate duplicates
      console.log('\n🔍 Running initial duplicate detection...')
      
      const companyDuplicates = await client.query(`
        SELECT match_type, COUNT(*) as count
        FROM company_duplicate_candidates
        GROUP BY match_type
        ORDER BY match_type
      `)
      
      const contactDuplicates = await client.query(`
        SELECT match_type, COUNT(*) as count
        FROM contact_duplicate_candidates
        GROUP BY match_type
        ORDER BY match_type
      `)
      
      console.log('\n📋 Initial Duplicate Detection Results:')
      console.log('Companies:')
      if (companyDuplicates.rows.length === 0) {
        console.log('   No company duplicates found')
      } else {
        companyDuplicates.rows.forEach(row => {
          console.log(`   ${row.match_type}: ${row.count} pairs`)
        })
      }
      
      console.log('Contacts:')
      if (contactDuplicates.rows.length === 0) {
        console.log('   No contact duplicates found')
      } else {
        contactDuplicates.rows.forEach(row => {
          console.log(`   ${row.match_type}: ${row.count} pairs`)
        })
      }
      
      console.log('\n🎉 Duplicate detection system is ready!')
      console.log('Next steps:')
      console.log('1. Test the duplicate detection APIs')
      console.log('2. Update the UI to show duplicate management features')
      console.log('3. Run full system scan for duplicates')
      
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error)
    process.exit(1)
  } finally {
    client.release()
    await pool.end()
  }
}

// Run the migration if called directly
if (require.main === module) {
  runDuplicateDetectionMigration()
    .then(() => {
      console.log('Migration completed successfully')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Migration failed:', error)
      process.exit(1)
    })
}
