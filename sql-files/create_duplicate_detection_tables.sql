-- Duplicate Detection System Database Schema
-- Creates normalized lookup tables and duplicate tracking for efficient duplicate detection

-- ===========================================
-- Company Normalized Data Table
-- ===========================================
CREATE TABLE IF NOT EXISTS company_normalized_data (
    id SERIAL PRIMARY KEY,
    company_id INTEGER NOT NULL REFERENCES companies(company_id) ON DELETE CASCADE,
    normalized_name VARCHAR(255) NOT NULL,     -- Lowercase, no legal suffixes
    normalized_domain VARCHAR(255),            -- Extracted domain from website
    name_tokens TEXT[],                        -- Array of searchable name parts
    industry_normalized VARCHAR(100),          -- Lowercase industry
    phone_normalized VARCHAR(20),              -- Numbers only
    website_normalized VARCHAR(255),           -- Cleaned website URL
    address_normalized TEXT,                   -- Standardized address
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(company_id)
);

-- ===========================================  
-- Contact Normalized Data Table
-- ===========================================
CREATE TABLE IF NOT EXISTS contact_normalized_data (
    id SERIAL PRIMARY KEY,
    contact_id INTEGER NOT NULL REFERENCES contacts(contact_id) ON DELETE CASCADE,
    full_name_normalized VARCHAR(255) NOT NULL, -- Lowercase full name
    first_name_normalized VARCHAR(100),          -- Lowercase first name
    last_name_normalized VARCHAR(100),           -- Lowercase last name
    email_normalized VARCHAR(255),               -- Lowercase primary email
    additional_email_normalized VARCHAR(255),    -- Lowercase secondary email
    email_domain VARCHAR(100),                   -- Primary email domain
    linkedin_handle VARCHAR(100),               -- LinkedIn username (not full URL)
    phone_normalized VARCHAR(20),               -- Numbers only
    name_tokens TEXT[],                         -- Array of searchable name parts
    company_name_normalized VARCHAR(255),       -- Lowercase company name
    title_normalized VARCHAR(255),              -- Lowercase job title
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(contact_id)
);

-- ===========================================
-- Duplicate Records Tracking Table  
-- ===========================================
CREATE TABLE IF NOT EXISTS duplicate_records (
    id SERIAL PRIMARY KEY,
    record_type VARCHAR(20) NOT NULL CHECK (record_type IN ('company', 'contact')),
    primary_record_id INTEGER NOT NULL,        -- The "master" record to keep
    duplicate_record_id INTEGER NOT NULL,      -- The duplicate to potentially merge/remove
    match_type VARCHAR(50) NOT NULL,           -- How the duplicate was detected
    confidence_score DECIMAL(3,2) DEFAULT 1.0 CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0),
    match_details JSONB,                       -- Additional match information
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'false_positive', 'merged')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    resolved_by VARCHAR(100) NULL,
    resolution_notes TEXT,
    -- Prevent duplicate entries for the same pair
    UNIQUE(record_type, primary_record_id, duplicate_record_id),
    -- Also prevent reverse duplicates (A->B and B->A)
    CONSTRAINT check_no_self_duplicate CHECK (primary_record_id != duplicate_record_id)
);

-- ===========================================
-- Performance Indexes
-- ===========================================

-- Company normalized data indexes
CREATE INDEX IF NOT EXISTS idx_company_normalized_name ON company_normalized_data(normalized_name);
CREATE INDEX IF NOT EXISTS idx_company_normalized_domain ON company_normalized_data(normalized_domain) WHERE normalized_domain IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_company_name_tokens ON company_normalized_data USING GIN(name_tokens);
CREATE INDEX IF NOT EXISTS idx_company_phone_normalized ON company_normalized_data(phone_normalized) WHERE phone_normalized IS NOT NULL AND phone_normalized != '';
CREATE INDEX IF NOT EXISTS idx_company_industry_normalized ON company_normalized_data(industry_normalized) WHERE industry_normalized IS NOT NULL AND industry_normalized != '';

-- Contact normalized data indexes  
CREATE INDEX IF NOT EXISTS idx_contact_normalized_email ON contact_normalized_data(email_normalized) WHERE email_normalized IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_contact_additional_email ON contact_normalized_data(additional_email_normalized) WHERE additional_email_normalized IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_contact_email_domain ON contact_normalized_data(email_domain) WHERE email_domain IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_contact_linkedin_handle ON contact_normalized_data(linkedin_handle) WHERE linkedin_handle IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_contact_name_tokens ON contact_normalized_data USING GIN(name_tokens);
CREATE INDEX IF NOT EXISTS idx_contact_phone_normalized ON contact_normalized_data(phone_normalized) WHERE phone_normalized IS NOT NULL AND phone_normalized != '';
CREATE INDEX IF NOT EXISTS idx_contact_full_name_normalized ON contact_normalized_data(full_name_normalized);
CREATE INDEX IF NOT EXISTS idx_contact_company_name_normalized ON contact_normalized_data(company_name_normalized) WHERE company_name_normalized IS NOT NULL;

-- Duplicate records indexes
CREATE INDEX IF NOT EXISTS idx_duplicate_records_type_status ON duplicate_records(record_type, status);
CREATE INDEX IF NOT EXISTS idx_duplicate_records_primary ON duplicate_records(record_type, primary_record_id);
CREATE INDEX IF NOT EXISTS idx_duplicate_records_duplicate ON duplicate_records(record_type, duplicate_record_id);
CREATE INDEX IF NOT EXISTS idx_duplicate_records_created_at ON duplicate_records(created_at);
CREATE INDEX IF NOT EXISTS idx_duplicate_records_confidence ON duplicate_records(confidence_score);

-- ===========================================
-- Normalization Functions
-- ===========================================

-- Function to extract domain from URL
CREATE OR REPLACE FUNCTION extract_domain(url TEXT)
RETURNS TEXT AS $$
BEGIN
    IF url IS NULL OR url = '' THEN
        RETURN NULL;
    END IF;
    
    -- Remove protocol and www
    url := LOWER(TRIM(url));
    url := REGEXP_REPLACE(url, '^https?://', '');
    url := REGEXP_REPLACE(url, '^www\.', '');
    
    -- Extract just the domain part (remove path)
    url := REGEXP_REPLACE(url, '/.*$', '');
    
    -- Remove port numbers
    url := REGEXP_REPLACE(url, ':[0-9]+$', '');
    
    RETURN NULLIF(url, '');
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to normalize company names (remove legal suffixes)
CREATE OR REPLACE FUNCTION normalize_company_name(name TEXT)
RETURNS TEXT AS $$
BEGIN
    IF name IS NULL OR name = '' THEN
        RETURN NULL;
    END IF;
    
    -- Convert to lowercase and trim
    name := LOWER(TRIM(name));
    
    -- Remove common legal suffixes
    name := REGEXP_REPLACE(name, '\s+(inc\.?|llc\.?|ltd\.?|corp\.?|corporation|company|co\.?|limited|l\.?l\.?c\.?|l\.?l\.?p\.?|lp|llp|pllc|p\.?c\.?|professional\s+corporation)$', '', 'gi');
    
    -- Remove extra whitespace
    name := REGEXP_REPLACE(name, '\s+', ' ', 'g');
    name := TRIM(name);
    
    RETURN NULLIF(name, '');
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to extract LinkedIn handle from URL
CREATE OR REPLACE FUNCTION extract_linkedin_handle(url TEXT)
RETURNS TEXT AS $$
BEGIN
    IF url IS NULL OR url = '' THEN
        RETURN NULL;
    END IF;
    
    -- Extract handle from LinkedIn URL
    url := REGEXP_REPLACE(url, '^https?://(www\.)?linkedin\.com/(in|pub)/([^/?]+).*$', '\3', 'i');
    
    -- Return NULL if no valid handle extracted
    IF url = '' OR url ~ '^https?://' THEN
        RETURN NULL;
    END IF;
    
    RETURN LOWER(TRIM(url));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to normalize phone numbers (keep digits only)
CREATE OR REPLACE FUNCTION normalize_phone(phone TEXT)
RETURNS TEXT AS $$
BEGIN
    IF phone IS NULL OR phone = '' THEN
        RETURN NULL;
    END IF;
    
    -- Keep only digits
    phone := REGEXP_REPLACE(phone, '[^0-9]', '', 'g');
    
    -- Return NULL if too short to be a valid phone number
    IF LENGTH(phone) < 7 THEN
        RETURN NULL;
    END IF;
    
    RETURN phone;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to create name tokens for search
CREATE OR REPLACE FUNCTION create_name_tokens(name TEXT)
RETURNS TEXT[] AS $$
BEGIN
    IF name IS NULL OR name = '' THEN
        RETURN ARRAY[]::TEXT[];
    END IF;
    
    -- Split by spaces and filter out short words
    RETURN ARRAY(
        SELECT LOWER(word)
        FROM UNNEST(STRING_TO_ARRAY(TRIM(name), ' ')) AS word
        WHERE LENGTH(word) >= 2
        AND word !~ '^(and|or|the|of|at|in|on|for|with|by)$'
    );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- ===========================================
-- Data Population Functions
-- ===========================================

-- Function to populate company normalized data
CREATE OR REPLACE FUNCTION populate_company_normalized_data()
RETURNS TRIGGER AS $$
DECLARE
    domain_part TEXT;
    clean_name TEXT;
    tokens TEXT[];
BEGIN
    -- Extract and normalize domain
    domain_part := extract_domain(NEW.company_website);
    
    -- Normalize company name
    clean_name := normalize_company_name(NEW.company_name);
    
    -- Create name tokens
    tokens := create_name_tokens(clean_name);
    
    -- Insert or update normalized data
    INSERT INTO company_normalized_data (
        company_id,
        normalized_name,
        normalized_domain,
        name_tokens,
        industry_normalized,
        phone_normalized,
        website_normalized,
        address_normalized
    ) VALUES (
        NEW.company_id,
        clean_name,
        domain_part,
        tokens,
        LOWER(TRIM(COALESCE(NEW.industry, ''))),
        normalize_phone(NEW.company_phone),
        LOWER(TRIM(COALESCE(NEW.company_website, ''))),
        LOWER(TRIM(COALESCE(NEW.company_address, '')))
    )
    ON CONFLICT (company_id) DO UPDATE SET
        normalized_name = EXCLUDED.normalized_name,
        normalized_domain = EXCLUDED.normalized_domain,
        name_tokens = EXCLUDED.name_tokens,
        industry_normalized = EXCLUDED.industry_normalized,
        phone_normalized = EXCLUDED.phone_normalized,
        website_normalized = EXCLUDED.website_normalized,
        address_normalized = EXCLUDED.address_normalized,
        updated_at = CURRENT_TIMESTAMP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to populate contact normalized data
CREATE OR REPLACE FUNCTION populate_contact_normalized_data()
RETURNS TRIGGER AS $$
DECLARE
    full_name_clean TEXT;
    first_name_clean TEXT;
    last_name_clean TEXT;
    email_clean TEXT;
    additional_email_clean TEXT;
    domain_part TEXT;
    linkedin_handle TEXT;
    tokens TEXT[];
    company_clean TEXT;
    title_clean TEXT;
BEGIN
    -- Normalize names
    first_name_clean := LOWER(TRIM(COALESCE(NEW.first_name, '')));
    last_name_clean := LOWER(TRIM(COALESCE(NEW.last_name, '')));
    full_name_clean := LOWER(TRIM(COALESCE(NEW.full_name, CONCAT(COALESCE(NEW.first_name, ''), ' ', COALESCE(NEW.last_name, '')))));
    
    -- Normalize emails
    email_clean := LOWER(TRIM(COALESCE(NEW.email, NEW.personal_email, '')));
    additional_email_clean := LOWER(TRIM(COALESCE(NEW.additional_email, '')));
    
    -- Extract email domain
    domain_part := CASE 
        WHEN email_clean != '' AND email_clean LIKE '%@%' THEN
            SPLIT_PART(email_clean, '@', 2)
        ELSE NULL
    END;
    
    -- Extract LinkedIn handle
    linkedin_handle := extract_linkedin_handle(COALESCE(NEW.linkedin_url, NEW.person_linkedin));
    
    -- Create name tokens
    tokens := create_name_tokens(full_name_clean);
    
    -- Normalize company and title
    company_clean := LOWER(TRIM(COALESCE(NEW.company_name, '')));
    title_clean := LOWER(TRIM(COALESCE(NEW.job_title, NEW.title, '')));
    
    -- Insert or update normalized data
    INSERT INTO contact_normalized_data (
        contact_id,
        full_name_normalized,
        first_name_normalized,
        last_name_normalized,
        email_normalized,
        additional_email_normalized,
        email_domain,
        linkedin_handle,
        phone_normalized,
        name_tokens,
        company_name_normalized,
        title_normalized
    ) VALUES (
        NEW.contact_id,
        NULLIF(full_name_clean, ''),
        NULLIF(first_name_clean, ''),
        NULLIF(last_name_clean, ''),
        NULLIF(email_clean, ''),
        NULLIF(additional_email_clean, ''),
        domain_part,
        linkedin_handle,
        normalize_phone(COALESCE(NEW.phone_number, NEW.phone_number_secondary)),
        tokens,
        NULLIF(company_clean, ''),
        NULLIF(title_clean, '')
    )
    ON CONFLICT (contact_id) DO UPDATE SET
        full_name_normalized = EXCLUDED.full_name_normalized,
        first_name_normalized = EXCLUDED.first_name_normalized,
        last_name_normalized = EXCLUDED.last_name_normalized,
        email_normalized = EXCLUDED.email_normalized,
        additional_email_normalized = EXCLUDED.additional_email_normalized,
        email_domain = EXCLUDED.email_domain,
        linkedin_handle = EXCLUDED.linkedin_handle,
        phone_normalized = EXCLUDED.phone_normalized,
        name_tokens = EXCLUDED.name_tokens,
        company_name_normalized = EXCLUDED.company_name_normalized,
        title_normalized = EXCLUDED.title_normalized,
        updated_at = CURRENT_TIMESTAMP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ===========================================
-- Triggers
-- ===========================================

-- Trigger for companies
DROP TRIGGER IF EXISTS trigger_populate_company_normalized_data ON companies;
CREATE TRIGGER trigger_populate_company_normalized_data
    AFTER INSERT OR UPDATE ON companies
    FOR EACH ROW
    EXECUTE FUNCTION populate_company_normalized_data();

-- Trigger for contacts
DROP TRIGGER IF EXISTS trigger_populate_contact_normalized_data ON contacts;
CREATE TRIGGER trigger_populate_contact_normalized_data
    AFTER INSERT OR UPDATE ON contacts
    FOR EACH ROW
    EXECUTE FUNCTION populate_contact_normalized_data();

-- ===========================================
-- Initial Data Population
-- ===========================================

-- Populate existing company data
INSERT INTO company_normalized_data (
    company_id,
    normalized_name,
    normalized_domain,
    name_tokens,
    industry_normalized,
    phone_normalized,
    website_normalized,
    address_normalized
)
SELECT 
    company_id,
    normalize_company_name(company_name),
    extract_domain(company_website),
    create_name_tokens(normalize_company_name(company_name)),
    LOWER(TRIM(COALESCE(industry, ''))),
    normalize_phone(company_phone),
    LOWER(TRIM(COALESCE(company_website, ''))),
    LOWER(TRIM(COALESCE(company_address, '')))
FROM companies 
WHERE company_id IS NOT NULL
ON CONFLICT (company_id) DO NOTHING;

-- Populate existing contact data
INSERT INTO contact_normalized_data (
    contact_id,
    full_name_normalized,
    first_name_normalized,
    last_name_normalized,
    email_normalized,
    additional_email_normalized,
    email_domain,
    linkedin_handle,
    phone_normalized,
    name_tokens,
    company_name_normalized,
    title_normalized
)
SELECT 
    contact_id,
    LOWER(TRIM(COALESCE(full_name, CONCAT(COALESCE(first_name, ''), ' ', COALESCE(last_name, ''))))),
    LOWER(TRIM(COALESCE(first_name, ''))),
    LOWER(TRIM(COALESCE(last_name, ''))),
    LOWER(TRIM(COALESCE(email, personal_email, ''))),
    LOWER(TRIM(COALESCE(additional_email, ''))),
    CASE 
        WHEN COALESCE(email, personal_email) IS NOT NULL AND COALESCE(email, personal_email) LIKE '%@%' THEN
            SPLIT_PART(LOWER(TRIM(COALESCE(email, personal_email))), '@', 2)
        ELSE NULL
    END,
    extract_linkedin_handle(COALESCE(linkedin_url, person_linkedin)),
    normalize_phone(COALESCE(phone_number, phone_number_secondary)),
    create_name_tokens(LOWER(TRIM(COALESCE(full_name, CONCAT(COALESCE(first_name, ''), ' ', COALESCE(last_name, '')))))),
    LOWER(TRIM(COALESCE(company_name, ''))),
    LOWER(TRIM(COALESCE(job_title, title, '')))
FROM contacts 
WHERE contact_id IS NOT NULL
ON CONFLICT (contact_id) DO NOTHING;

-- ===========================================
-- Helper Views for Duplicate Detection
-- ===========================================

-- View for company duplicate candidates
CREATE OR REPLACE VIEW company_duplicate_candidates AS
SELECT DISTINCT
    c1.company_id as primary_id,
    c2.company_id as duplicate_id,
    'exact_domain' as match_type,
    0.95 as confidence,
    jsonb_build_object(
        'domain', cn1.normalized_domain,
        'primary_name', cn1.normalized_name,
        'duplicate_name', cn2.normalized_name
    ) as match_details
FROM company_normalized_data cn1
JOIN company_normalized_data cn2 ON cn1.normalized_domain = cn2.normalized_domain 
    AND cn1.company_id < cn2.company_id 
    AND cn1.normalized_domain IS NOT NULL 
    AND cn1.normalized_domain != ''
JOIN companies c1 ON cn1.company_id = c1.company_id
JOIN companies c2 ON cn2.company_id = c2.company_id

UNION ALL

SELECT DISTINCT
    c1.company_id as primary_id,
    c2.company_id as duplicate_id,
    'exact_name' as match_type,
    0.90 as confidence,
    jsonb_build_object(
        'normalized_name', cn1.normalized_name,
        'primary_website', cn1.normalized_domain,
        'duplicate_website', cn2.normalized_domain
    ) as match_details
FROM company_normalized_data cn1
JOIN company_normalized_data cn2 ON cn1.normalized_name = cn2.normalized_name 
    AND cn1.company_id < cn2.company_id 
    AND cn1.normalized_name IS NOT NULL 
    AND cn1.normalized_name != ''
JOIN companies c1 ON cn1.company_id = c1.company_id
JOIN companies c2 ON cn2.company_id = c2.company_id

UNION ALL

SELECT DISTINCT
    c1.company_id as primary_id,
    c2.company_id as duplicate_id,
    'phone_match' as match_type,
    0.85 as confidence,
    jsonb_build_object(
        'phone', cn1.phone_normalized,
        'primary_name', cn1.normalized_name,
        'duplicate_name', cn2.normalized_name
    ) as match_details
FROM company_normalized_data cn1
JOIN company_normalized_data cn2 ON cn1.phone_normalized = cn2.phone_normalized 
    AND cn1.company_id < cn2.company_id 
    AND cn1.phone_normalized IS NOT NULL 
    AND cn1.phone_normalized != ''
    AND LENGTH(cn1.phone_normalized) >= 10
JOIN companies c1 ON cn1.company_id = c1.company_id
JOIN companies c2 ON cn2.company_id = c2.company_id;

-- View for contact duplicate candidates
CREATE OR REPLACE VIEW contact_duplicate_candidates AS
SELECT DISTINCT
    c1.contact_id as primary_id,
    c2.contact_id as duplicate_id,
    'exact_email' as match_type,
    0.98 as confidence,
    jsonb_build_object(
        'email', cn1.email_normalized,
        'primary_name', cn1.full_name_normalized,
        'duplicate_name', cn2.full_name_normalized
    ) as match_details
FROM contact_normalized_data cn1
JOIN contact_normalized_data cn2 ON cn1.email_normalized = cn2.email_normalized 
    AND cn1.contact_id < cn2.contact_id 
    AND cn1.email_normalized IS NOT NULL 
    AND cn1.email_normalized != ''
JOIN contacts c1 ON cn1.contact_id = c1.contact_id
JOIN contacts c2 ON cn2.contact_id = c2.contact_id

UNION ALL

SELECT DISTINCT
    c1.contact_id as primary_id,
    c2.contact_id as duplicate_id,
    'linkedin_match' as match_type,
    0.95 as confidence,
    jsonb_build_object(
        'linkedin_handle', cn1.linkedin_handle,
        'primary_name', cn1.full_name_normalized,
        'duplicate_name', cn2.full_name_normalized
    ) as match_details
FROM contact_normalized_data cn1
JOIN contact_normalized_data cn2 ON cn1.linkedin_handle = cn2.linkedin_handle 
    AND cn1.contact_id < cn2.contact_id 
    AND cn1.linkedin_handle IS NOT NULL 
    AND cn1.linkedin_handle != ''
JOIN contacts c1 ON cn1.contact_id = c1.contact_id
JOIN contacts c2 ON cn2.contact_id = c2.contact_id

UNION ALL

SELECT DISTINCT
    c1.contact_id as primary_id,
    c2.contact_id as duplicate_id,
    'phone_match' as match_type,
    0.75 as confidence,
    jsonb_build_object(
        'phone', cn1.phone_normalized,
        'primary_name', cn1.full_name_normalized,
        'duplicate_name', cn2.full_name_normalized
    ) as match_details
FROM contact_normalized_data cn1
JOIN contact_normalized_data cn2 ON cn1.phone_normalized = cn2.phone_normalized 
    AND cn1.contact_id < cn2.contact_id 
    AND cn1.phone_normalized IS NOT NULL 
    AND cn1.phone_normalized != ''
    AND LENGTH(cn1.phone_normalized) >= 10
JOIN contacts c1 ON cn1.contact_id = c1.contact_id
JOIN contacts c2 ON cn2.contact_id = c2.contact_id;

-- ===========================================
-- Grant Permissions (adjust as needed)
-- ===========================================
-- GRANT ALL ON company_normalized_data TO anax_user;
-- GRANT ALL ON contact_normalized_data TO anax_user;
-- GRANT ALL ON duplicate_records TO anax_user;
-- GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO anax_user;
