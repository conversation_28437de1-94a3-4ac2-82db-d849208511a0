-- Migration: Add unique constraint on article_url to prevent duplicate URLs
-- This ensures data integrity and prevents the same article from being scraped multiple times

-- Add unique constraint on article_url
ALTER TABLE article ADD CONSTRAINT unique_article_url UNIQUE (article_url);

-- Add index on article_url for better query performance
CREATE INDEX IF NOT EXISTS idx_article_url ON article(article_url);

-- Verify the constraint was added
SELECT conname, contype, pg_get_constraintdef(oid) as definition
FROM pg_constraint
WHERE conrelid = 'article'::regclass AND conname = 'unique_article_url';