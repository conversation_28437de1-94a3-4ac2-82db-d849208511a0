# Duplicate Detection System Implementation Plan

## Overview
This document outlines the implementation of a comprehensive duplicate detection system for contacts and companies in the Anax Dashboard application. The system will extend the existing conflict resolution framework to include proactive duplicate detection.

## Current State Analysis

### Existing Conflict System
- **Conflict Resolution Modal**: Individual conflict resolution UI
- **Conflict Dashboard**: List view with filtering and bulk operations
- **Database Storage**: Uses `conflicts` JSONB field in both `companies` and `contacts` tables
- **Conflict Types**: Currently handles field conflicts from CSV imports

### Current Limitations
1. **Reactive Approach**: Only detects conflicts during CSV imports
2. **No Proactive Scanning**: No system-wide duplicate detection
3. **Performance Issues**: No optimized indexing for similarity matching
4. **Limited Matching**: Basic exact matching only

## Solution Architecture

### 1. Database Schema Enhancement

#### 1.1 Normalized Lookup Tables
Create specialized tables for optimized duplicate detection:

```sql
-- Company normalized data for faster matching
CREATE TABLE company_normalized_data (
    id SERIAL PRIMARY KEY,
    company_id INTEGER REFERENCES companies(company_id),
    normalized_name VARCHAR(255) NOT NULL, -- Lowercase, no suffixes
    normalized_domain VARCHAR(255),        -- Extracted domain
    name_tokens TEXT[],                    -- Searchable name tokens
    industry_normalized VARCHAR(100),
    phone_normalized VARCHAR(20),          -- Numbers only
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Contact normalized data for faster matching  
CREATE TABLE contact_normalized_data (
    id SERIAL PRIMARY KEY,
    contact_id INTEGER REFERENCES contacts(contact_id),
    full_name_normalized VARCHAR(255) NOT NULL, -- Lowercase full name
    email_normalized VARCHAR(255),               -- Lowercase email
    linkedin_handle VARCHAR(100),               -- LinkedIn username
    phone_normalized VARCHAR(20),               -- Numbers only
    name_tokens TEXT[],                         -- Searchable name parts
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Duplicate tracking table
CREATE TABLE duplicate_records (
    id SERIAL PRIMARY KEY,
    record_type VARCHAR(20) NOT NULL, -- 'company' | 'contact'
    primary_record_id INTEGER NOT NULL,
    duplicate_record_id INTEGER NOT NULL,
    match_type VARCHAR(50) NOT NULL, -- 'exact_name' | 'domain' | 'email' | 'similarity'
    confidence_score DECIMAL(3,2) DEFAULT 1.0, -- 0.0 to 1.0
    match_details JSONB,
    status VARCHAR(20) DEFAULT 'pending', -- 'pending' | 'confirmed' | 'false_positive'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    resolved_by VARCHAR(100) NULL,
    UNIQUE(record_type, primary_record_id, duplicate_record_id)
);

-- Indexes for performance
CREATE INDEX idx_company_normalized_name ON company_normalized_data(normalized_name);
CREATE INDEX idx_company_normalized_domain ON company_normalized_data(normalized_domain);
CREATE INDEX idx_contact_normalized_email ON contact_normalized_data(email_normalized);
CREATE INDEX idx_contact_normalized_linkedin ON contact_normalized_data(linkedin_handle);
CREATE INDEX idx_contact_name_tokens ON contact_normalized_data USING GIN(name_tokens);
CREATE INDEX idx_duplicate_records_type_status ON duplicate_records(record_type, status);
```

#### 1.2 Triggers for Auto-Population
```sql
-- Function to normalize company data
CREATE OR REPLACE FUNCTION normalize_company_data()
RETURNS TRIGGER AS $$
DECLARE
    domain_part VARCHAR(255);
    clean_name VARCHAR(255);
BEGIN
    -- Extract domain from website
    domain_part := CASE 
        WHEN NEW.company_website IS NOT NULL THEN
            LOWER(REGEXP_REPLACE(
                REGEXP_REPLACE(NEW.company_website, '^https?://(www\.)?', ''),
                '/.*$', ''
            ))
        ELSE NULL
    END;
    
    -- Clean company name (lowercase, remove suffixes)
    clean_name := LOWER(TRIM(REGEXP_REPLACE(
        COALESCE(NEW.company_name, ''),
        '\s+(inc\.?|llc\.?|ltd\.?|corp\.?|corporation|company|co\.?)$',
        '',
        'gi'
    )));
    
    -- Insert or update normalized data
    INSERT INTO company_normalized_data (
        company_id, normalized_name, normalized_domain, name_tokens, industry_normalized, phone_normalized
    ) VALUES (
        NEW.company_id,
        clean_name,
        domain_part,
        STRING_TO_ARRAY(clean_name, ' '),
        LOWER(COALESCE(NEW.industry, '')),
        REGEXP_REPLACE(COALESCE(NEW.company_phone, ''), '[^0-9]', '', 'g')
    )
    ON CONFLICT (company_id) DO UPDATE SET
        normalized_name = EXCLUDED.normalized_name,
        normalized_domain = EXCLUDED.normalized_domain,
        name_tokens = EXCLUDED.name_tokens,
        industry_normalized = EXCLUDED.industry_normalized,
        phone_normalized = EXCLUDED.phone_normalized,
        updated_at = CURRENT_TIMESTAMP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for companies
CREATE TRIGGER trigger_normalize_company_data
    AFTER INSERT OR UPDATE ON companies
    FOR EACH ROW
    EXECUTE FUNCTION normalize_company_data();
```

### 2. Duplicate Detection Algorithms

#### 2.1 Company Matching Strategies

**Priority Order:**
1. **Exact Domain Match** (Confidence: 0.95)
2. **Exact Name Match** (Confidence: 0.90)
3. **Phone Number Match** (Confidence: 0.85)
4. **Fuzzy Name Similarity** (Confidence: 0.60-0.85)

```typescript
interface CompanyMatchCriteria {
  exactDomain: boolean;
  exactName: boolean;
  phoneMatch: boolean;
  nameSimilarity: number; // 0.0 to 1.0
  minimumConfidence: number;
}

interface CompanyDuplicateResult {
  primaryCompany: Company;
  duplicates: Array<{
    company: Company;
    matchType: 'exact_domain' | 'exact_name' | 'phone' | 'similarity';
    confidence: number;
    matchDetails: Record<string, any>;
  }>;
}
```

#### 2.2 Contact Matching Strategies

**Priority Order:**
1. **Exact Email Match** (Confidence: 0.98)
2. **LinkedIn URL Match** (Confidence: 0.95)
3. **Email Domain + Name Similarity** (Confidence: 0.80-0.90)
4. **Name + Company Match** (Confidence: 0.70-0.85)
5. **Phone Number Match** (Confidence: 0.75)

```typescript
interface ContactMatchCriteria {
  exactEmail: boolean;
  linkedinMatch: boolean;
  emailDomainNameSimilarity: boolean;
  nameCompanyMatch: boolean;
  phoneMatch: boolean;
  minimumConfidence: number;
}

interface ContactDuplicateResult {
  primaryContact: Contact;
  duplicates: Array<{
    contact: Contact;
    matchType: 'exact_email' | 'linkedin' | 'email_domain_name' | 'name_company' | 'phone';
    confidence: number;
    matchDetails: Record<string, any>;
  }>;
}
```

### 3. API Endpoints

#### 3.1 Duplicate Detection APIs

```typescript
// POST /api/duplicates/scan
interface ScanRequest {
  type: 'company' | 'contact' | 'both';
  recordIds?: number[]; // Optional: scan specific records
  criteria?: CompanyMatchCriteria | ContactMatchCriteria;
}

interface ScanResponse {
  success: boolean;
  duplicates: {
    companies: CompanyDuplicateResult[];
    contacts: ContactDuplicateResult[];
  };
  stats: {
    scanned: number;
    duplicatesFound: number;
    processingTime: number;
  };
}

// GET /api/duplicates
interface GetDuplicatesRequest {
  type?: 'company' | 'contact';
  status?: 'pending' | 'confirmed' | 'false_positive';
  page?: number;
  pageSize?: number;
}

// POST /api/duplicates/resolve
interface ResolveDuplicatesRequest {
  duplicateId: number;
  action: 'merge' | 'keep_separate' | 'mark_false_positive';
  mergeStrategy?: {
    primaryRecordId: number;
    fieldsToMerge: string[];
    customValues?: Record<string, any>;
  };
}
```

#### 3.2 Batch Processing APIs

```typescript
// POST /api/duplicates/batch-scan
interface BatchScanRequest {
  batchSize: number;
  criteria: CompanyMatchCriteria | ContactMatchCriteria;
}

// GET /api/duplicates/batch-status/{jobId}
interface BatchStatusResponse {
  jobId: string;
  status: 'running' | 'completed' | 'failed';
  progress: {
    processed: number;
    total: number;
    duplicatesFound: number;
  };
  estimatedTimeRemaining?: number;
}
```

### 4. UI Components Enhancement

#### 4.1 Enhanced Conflict Dashboard

**New Features:**
- **Duplicate Detection Tab**: Separate tab for duplicate management
- **Batch Scanning**: Start system-wide duplicate detection
- **Confidence Filtering**: Filter by confidence score
- **Match Type Filtering**: Filter by detection method
- **Bulk Actions**: Merge, separate, or mark false positives

**Component Structure:**
```tsx
interface DuplicateDashboardProps {
  initialType?: 'company' | 'contact';
}

// Enhanced ConflictDashboard.tsx components:
- <DuplicateDetectionTab />
- <ScanProgressIndicator />
- <DuplicateMatchCard />
- <BatchActionPanel />
- <ConfidenceScoreFilter />
```

#### 4.2 Improved Resolution Modal

**New Features:**
- **Merge Preview**: Show what merged record will look like
- **Field-by-field Comparison**: Side-by-side comparison
- **Auto-merge Suggestions**: AI-powered merge recommendations
- **Batch Resolution**: Resolve multiple similar duplicates

```tsx
interface DuplicateResolutionModalProps {
  duplicateGroup: DuplicateResult;
  onResolve: (action: ResolutionAction) => Promise<void>;
  onMerge: (mergeDetails: MergeDetails) => Promise<void>;
}
```

#### 4.3 Smart Merge Interface

**Features:**
- **Visual Diff**: Highlight differences between records
- **Auto-populate**: Suggest best values for each field
- **Conflict Resolution**: Handle conflicting values
- **Related Records**: Show impact on related records

### 5. Implementation Phases

#### Phase 1: Database Foundation (Week 1)
- [ ] Create normalized lookup tables
- [ ] Implement data population triggers that can be handled from UI
- [ ] Migrate existing data
- [ ] Add performance indexes

#### Phase 2: Core Detection Logic (Week 2)
- [ ] Implement company matching algorithms
- [ ] Implement contact matching algorithms
- [ ] Create duplicate detection service
- [ ] Add confidence scoring

#### Phase 3: API Development (Week 3)
- [ ] Build duplicate detection endpoints
- [ ] Implement batch processing
- [ ] Add duplicate resolution APIs
- [ ] Create merge functionality

#### Phase 4: UI Enhancement (Week 4)
- [ ] Enhance conflict dashboard
- [ ] Build duplicate resolution modal
- [ ] Create merge interface
- [ ] Add batch action controls


### 6. Performance Considerations

#### 6.1 Optimization Strategies
- **Batch Processing**: Process duplicates in background jobs
- **Smart Indexing**: Use GIN indexes for array searches
- **Caching**: Cache frequently accessed normalized data
- **Pagination**: Limit UI to manageable result sets

#### 6.2 Scalability
- **Queue System**: Use job queues for large scans
- **Progress Tracking**: Real-time progress updates
- **Resource Management**: Limit concurrent processing
- **Memory Management**: Stream large result sets

### 7. Monitoring & Analytics

#### 7.1 Metrics to Track
- Duplicate detection accuracy
- Processing performance
- User resolution patterns
- System resource usage

#### 7.2 Alerts
- High duplicate detection rates
- Performance degradation
- Failed batch jobs
- Unusual merge patterns
