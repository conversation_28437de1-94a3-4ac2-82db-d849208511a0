-- Main article table
CREATE TABLE IF NOT EXISTS article (
    article_id SERIAL PRIMARY KEY,
    publication_name TEXT,
    article_url TEXT,
    scraping_source_type TEXT,
    headline TEXT,
    publication_date TEXT, -- Still string per your spec
    author TEXT,
    summary TEXT,
    article_body_text TEXT,
    topic TEXT,
    market_trend_tags JSONB DEFAULT '[]'::jsonb,
    is_distressed BOOLEAN DEFAULT FALSE,
    sentiment TEXT,
    sentiment_summary TEXT,
    key_takeaways TEXT,
    llm_tags JSONB DEFAULT '[]'::jsonb,
    quotes_llm_tags JSONB DEFAULT '[]'::jsonb,
    source_confidence NUMERIC,
    extraction_notes TEXT,
    fetch_status TEXT DEFAULT 'pending',
    fetch_error JSONB,
    fetch_date TIMESTAMP WITH TIME ZONE,
    extraction_status TEXT DEFAULT 'pending',
    extraction_error JSONB,
    extraction_date TIMESTAMP WITH TIME ZONE,
    is_bad_url BOOLEAN DEFAULT FALSE,
    is_relevant BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Article properties table
CREATE TABLE IF NOT EXISTS article_properties (
    article_property_id SERIAL PRIMARY KEY,
    article_id INTEGER REFERENCES article(article_id) ON DELETE CASCADE,
    property_name TEXT,
    address TEXT,
    state TEXT,
    city TEXT,
    zipcode TEXT,
    region TEXT,
    country TEXT,
    square_footage BIGINT,
    unit_count INTEGER,
    construction_type TEXT,
    project_timeline TEXT,
    job_creation INTEGER,
    subsidy_info TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Article market metrics table
CREATE TABLE IF NOT EXISTS article_market_metrics (
    article_market_metric_id SERIAL PRIMARY KEY,
    article_id INTEGER REFERENCES article(article_id) ON DELETE CASCADE,
    market_city TEXT,
    market_state TEXT,
    market_country TEXT,
    time_period TEXT,
    vacancy_rate NUMERIC,
    rental_rate NUMERIC,
    absorption_rate NUMERIC,
    transaction_volume BIGINT,
    construction_pipeline BIGINT,
    new_deliveries_sf BIGINT,
    rental_rate_trend TEXT,
    cap_rate_avg NUMERIC,
    cap_rate_trend TEXT,
    demand_trend TEXT,
    commentary TEXT,
    remote_work_impact TEXT,
    distress_indicator BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Article transactions table
CREATE TABLE IF NOT EXISTS article_transactions (
    article_transaction_id SERIAL PRIMARY KEY,
    article_id INTEGER REFERENCES article(article_id) ON DELETE CASCADE,
    deal_type TEXT,
    deal_size TEXT,
    cap_rate TEXT,
    price_per_sf NUMERIC,
    loan_type TEXT,
    equity_type TEXT,
    financing_type JSONB DEFAULT '[]'::jsonb,
    capital_stack_notes JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Articles entities table
CREATE TABLE IF NOT EXISTS articles_entities (
    article_entity_id SERIAL PRIMARY KEY,
    article_id INTEGER REFERENCES article(article_id) ON DELETE CASCADE,
    entity_id INTEGER,
    entity_type TEXT,
    entity_name TEXT,
    entity_role TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add unique constraint on article_url to prevent duplicates
ALTER TABLE article ADD CONSTRAINT unique_article_url UNIQUE (article_url);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_article_publication_name ON article(publication_name);
CREATE INDEX IF NOT EXISTS idx_article_fetch_status ON article(fetch_status);
CREATE INDEX IF NOT EXISTS idx_article_extraction_status ON article(extraction_status);
CREATE INDEX IF NOT EXISTS idx_article_is_relevant ON article(is_relevant);
CREATE INDEX IF NOT EXISTS idx_article_is_bad_url ON article(is_bad_url);
CREATE INDEX IF NOT EXISTS idx_article_url ON article(article_url);
CREATE INDEX IF NOT EXISTS idx_article_properties_article_id ON article_properties(article_id);
CREATE INDEX IF NOT EXISTS idx_article_market_metrics_article_id ON article_market_metrics(article_id);
CREATE INDEX IF NOT EXISTS idx_article_transactions_article_id ON article_transactions(article_id);
CREATE INDEX IF NOT EXISTS idx_articles_entities_article_id ON articles_entities(article_id);
CREATE INDEX IF NOT EXISTS idx_articles_entities_entity_id ON articles_entities(entity_id);
CREATE INDEX IF NOT EXISTS idx_articles_entities_entity_type ON articles_entities(entity_type);
CREATE INDEX IF NOT EXISTS idx_articles_entities_entity_role ON articles_entities(entity_role);

-- Trigger function for auto-updating updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER update_article_updated_at BEFORE UPDATE ON article
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_article_properties_updated_at BEFORE UPDATE ON article_properties
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_article_market_metrics_updated_at BEFORE UPDATE ON article_market_metrics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_article_transactions_updated_at BEFORE UPDATE ON article_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_articles_entities_updated_at BEFORE UPDATE ON articles_entities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();