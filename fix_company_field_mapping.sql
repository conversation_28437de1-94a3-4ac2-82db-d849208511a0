-- Fix Company Field Mapping Issues
-- This script addresses field name mismatches between components and database

-- Step 1: Add missing company_zipcode column for consistency
ALTER TABLE companies 
ADD COLUMN IF NOT EXISTS company_zipcode TEXT;

-- Step 2: Copy data from company_zip to company_zipcode for consistency
UPDATE companies 
SET company_zipcode = company_zip 
WHERE company_zip IS NOT NULL AND company_zipcode IS NULL;

-- Step 3: Add missing main_phone column for consistency
ALTER TABLE companies 
ADD COLUMN IF NOT EXISTS main_phone TEXT;

-- Step 5: Add missing main_email column for consistency
ALTER TABLE companies 
ADD COLUMN IF NOT EXISTS main_email TEXT;

-- Step 6: Add missing headquarters_* columns for AI to update
ALTER TABLE companies 
ADD COLUMN IF NOT EXISTS headquarters_address TEXT,
ADD COLUMN IF NOT EXISTS headquarters_city TEXT,
ADD COLUMN IF NOT EXISTS headquarters_state TEXT,
ADD COLUMN IF NOT EXISTS headquarters_zipcode TEXT,
ADD COLUMN IF NOT EXISTS headquarters_country TEXT;

-- Step 7: Add comments to document the field mappings
COMMENT ON COLUMN companies.company_zipcode IS 'Alternative name for company_zip for component compatibility';
COMMENT ON COLUMN companies.main_phone IS 'Alternative name for company_phone for component compatibility';
COMMENT ON COLUMN companies.main_email IS 'Main email field for component compatibility';
COMMENT ON COLUMN companies.headquarters_address IS 'AI-updated headquarters address (separate from company_address)';
COMMENT ON COLUMN companies.headquarters_city IS 'AI-updated headquarters city (separate from company_city)';
COMMENT ON COLUMN companies.headquarters_state IS 'AI-updated headquarters state (separate from company_state)';
COMMENT ON COLUMN companies.headquarters_zipcode IS 'AI-updated headquarters zipcode (separate from company_zip)';
COMMENT ON COLUMN companies.headquarters_country IS 'AI-updated headquarters country (separate from company_country)';

-- Step 8: Verify the changes
SELECT 
  'company_zipcode' as field_name, COUNT(*) as non_null_count 
FROM companies 
WHERE company_zipcode IS NOT NULL
UNION ALL
SELECT 
  'main_phone' as field_name, COUNT(*) as non_null_count 
FROM companies 
WHERE main_phone IS NOT NULL
UNION ALL
SELECT 
  'main_email' as field_name, COUNT(*) as non_null_count 
FROM companies 
WHERE main_email IS NOT NULL
UNION ALL
SELECT 
  'headquarters_address' as field_name, COUNT(*) as non_null_count 
FROM companies 
WHERE headquarters_address IS NOT NULL;
