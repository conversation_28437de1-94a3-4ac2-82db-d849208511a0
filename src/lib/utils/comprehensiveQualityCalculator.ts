import { DealV2, PropertyV2, DealNsfFieldV2 } from '@/components/dashboard/deals/shared/types-v2';

export interface ComprehensiveQualityMetrics {
  overview: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
  };
  debt: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
    criteriaCount: number;
    individualCriteria?: Array<{
      criteria_id: number;
      capital_position: string[];
      qualityScore: number;
      completedFields: number;
      totalFields: number;
      missingFields: string[];
    }>;
  };
  equity: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
    criteriaCount: number;
    individualCriteria?: Array<{
      criteria_id: number;
      capital_position: string[];
      qualityScore: number;
      completedFields: number;
      totalFields: number;
      missingFields: string[];
    }>;
  };
  nsf: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
    sourcesCount: number;
    usesCount: number;
  };
  property: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
  };
  financial: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
  };
  units: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
  };
  campaign: {
    qualityScore: number;
    completedFields: number;
    totalFields: number;
    missingFields: string[];
    fieldDetails: Record<string, { hasValue: boolean; value: any }>;
  };
}

// Helper function to check if a value is considered "populated"
function isValuePopulated(value: any): boolean {
  if (value === null || value === undefined) return false;
  if (typeof value === 'string') return value.trim() !== '';
  if (typeof value === 'number') return !isNaN(value);
  if (Array.isArray(value)) return value.length > 0;
  if (typeof value === 'boolean') return true;
  if (value instanceof Date) return !isNaN(value.getTime());
  return true;
}

// Helper function to calculate quality for a set of fields
function calculateSectionQuality(fields: Record<string, any>): {
  qualityScore: number;
  completedFields: number;
  totalFields: number;
  missingFields: string[];
  fieldDetails: Record<string, { hasValue: boolean; value: any }>;
} {
  const fieldEntries = Object.entries(fields);
  const totalFields = fieldEntries.length;
  
  if (totalFields === 0) {
    return {
      qualityScore: 100,
      completedFields: 0,
      totalFields: 0,
      missingFields: [],
      fieldDetails: {}
    };
  }

  const fieldDetails: Record<string, { hasValue: boolean; value: any }> = {};
  const missingFields: string[] = [];
  let completedFields = 0;

  fieldEntries.forEach(([fieldName, value]) => {
    const hasValue = isValuePopulated(value);
    fieldDetails[fieldName] = { hasValue, value };
    
    if (hasValue) {
      completedFields++;
    } else {
      missingFields.push(fieldName);
    }
  });

  const qualityScore = Math.round((completedFields / totalFields) * 100);

  return {
    qualityScore,
    completedFields,
    totalFields,
    missingFields,
    fieldDetails
  };
}

export function calculateComprehensiveQuality(deal: DealV2): ComprehensiveQualityMetrics {
  // Overview fields (basic deal information)
  const overviewFields = {
    dealName: deal.dealName,
    summary: deal.summary,
    dealType: deal.dealType,
    askCapitalPosition: deal.askCapitalPosition,
    askAmount: deal.askAmount,
    capitalRaiseTimeline: deal.capitalRaiseTimeline,
    dateReceived: deal.dateReceived,
    dealStage: deal.dealStage,
    dateClosed: deal.dateClosed,
    dateUnderContract: deal.dateUnderContract,
    strategy: deal.strategy,
    holdPeriod: deal.holdPeriod,
    dealStatus: deal.dealStatus,
    priority: deal.priority,
    reviewStatus: deal.reviewStatus,
    reviewedBy: deal.reviewedBy,
    isDistressed: deal.isDistressed,
    isInternalOnly: deal.isInternalOnly,
    extractionConfidence: deal.extractionConfidence,
    processorVersion: deal.processorVersion,
    llmModelUsed: deal.llmModelUsed,
    llmProvider: deal.llmProvider,
    documentFilename: deal.documentFilename,
    documentSizeBytes: deal.documentSizeBytes,
    documentSource: deal.documentSource,
    documentType: deal.documentType,
    extractionMethod: deal.extractionMethod,
    extractionTimestamp: deal.extractionTimestamp,
    createdAt: deal.createdAt,
    updatedAt: deal.updatedAt
  };

  // Calculate debt criteria quality based on actual investment criteria records
  let debtQuality = {
    qualityScore: 100,
    completedFields: 0,
    totalFields: 0,
    missingFields: [],
    fieldDetails: {},
    criteriaCount: 0
  };

  if (deal.investmentCriteriaDebt && deal.investmentCriteriaDebt.length > 0) {
    debtQuality.criteriaCount = deal.investmentCriteriaDebt.length;
    
    // Define relevant debt fields for quality calculation
    const debtFieldsToCheck = [
      'loanInterestRate', 'loanToValueMin', 'loanToValueMax', 'loanToCostMin', 'loanToCostMax',
      'minLoanTerm', 'maxLoanTerm', 'loanType', 'amortization', 'minLoanDscr', 'maxLoanDscr',
      'recourseLoan', 'lienPosition', 'rateType', 'loanProgram', 'structuredLoanTranche'
    ];
    
    let totalCompletedFields = 0;
    let totalPossibleFields = 0;
    const allMissingFields = new Set<string>();
    const allFieldDetails: Record<string, { hasValue: boolean; value: any }> = {};
    
    // Check each debt criteria record
    deal.investmentCriteriaDebt.forEach((criteria, index) => {
      debtFieldsToCheck.forEach(fieldName => {
        const value = (criteria as any)[fieldName];
        const hasValue = isValuePopulated(value);
        const fieldKey = `debt_${index}_${fieldName}`;
        
        allFieldDetails[fieldKey] = { hasValue, value };
        totalPossibleFields++;
        
        if (hasValue) {
          totalCompletedFields++;
        } else {
          allMissingFields.add(fieldKey);
        }
      });
    });
    
    debtQuality = {
      qualityScore: totalPossibleFields > 0 ? Math.round((totalCompletedFields / totalPossibleFields) * 100) : 100,
      completedFields: totalCompletedFields,
      totalFields: totalPossibleFields,
      missingFields: Array.from(allMissingFields),
      fieldDetails: allFieldDetails,
      criteriaCount: deal.investmentCriteriaDebt.length
    };
  }

  // Calculate equity criteria quality based on actual investment criteria records
  let equityQuality = {
    qualityScore: 100,
    completedFields: 0,
    totalFields: 0,
    missingFields: [],
    fieldDetails: {},
    criteriaCount: 0
  };

  if (deal.investmentCriteriaEquity && deal.investmentCriteriaEquity.length > 0) {
    equityQuality.criteriaCount = deal.investmentCriteriaEquity.length;
    
    // Define relevant equity fields for quality calculation
    const equityFieldsToCheck = [
      'targetReturn', 'minimumInternalRateOfReturn', 'minimumYieldOnCost', 'minimumEquityMultiple',
      'targetCashOnCashMin', 'minHoldPeriodYears', 'maxHoldPeriodYears', 'ownershipRequirement',
      'attachmentPoint', 'maxLeverageTolerance', 'yieldOnCost', 'targetReturnIrrOnEquity',
      'equityMultiple', 'positionSpecificIrr', 'positionSpecificEquityMultiple'
    ];
    
    let totalCompletedFields = 0;
    let totalPossibleFields = 0;
    const allMissingFields = new Set<string>();
    const allFieldDetails: Record<string, { hasValue: boolean; value: any }> = {};
    
    // Check each equity criteria record
    deal.investmentCriteriaEquity.forEach((criteria, index) => {
      equityFieldsToCheck.forEach(fieldName => {
        const value = (criteria as any)[fieldName];
        const hasValue = isValuePopulated(value);
        const fieldKey = `equity_${index}_${fieldName}`;
        
        allFieldDetails[fieldKey] = { hasValue, value };
        totalPossibleFields++;
        
        if (hasValue) {
          totalCompletedFields++;
        } else {
          allMissingFields.add(fieldKey);
        }
      });
    });
    
    equityQuality = {
      qualityScore: totalPossibleFields > 0 ? Math.round((totalCompletedFields / totalPossibleFields) * 100) : 100,
      completedFields: totalCompletedFields,
      totalFields: totalPossibleFields,
      missingFields: Array.from(allMissingFields),
      fieldDetails: allFieldDetails,
      criteriaCount: deal.investmentCriteriaEquity.length
    };
  }

  // NSF fields
  const nsfFields = {
    residentialNsfNetSquareFoot: deal.residentialNsfNetSquareFoot,
    retailNsfNetSquareFoot: deal.retailNsfNetSquareFoot,
    occupancyRate: deal.occupancyRate,
    communityFacilityNsfNetSquareFoot: deal.communityFacilityNsfNetSquareFoot,
    officeNsfNetSquareFoot: deal.officeNsfNetSquareFoot
  };

  // Property fields - check both deal-level and property object fields
  const propertyFields = {
    // Deal-level property fields
    propertyId: deal.propertyId,
    numApartmentUnits: deal.numApartmentUnits,
    closingTime: deal.closingTime,
    hotelKeys: deal.hotelKeys,
    parkingSf: deal.parkingSf,
    parkingSpots: deal.parkingSpots,
    totalNumAffordableHousingUnits: deal.totalNumAffordableHousingUnits,
    totalNumMarketRateUnits: deal.totalNumMarketRateUnits,
    // Property object fields
    propertyAddress: deal.property?.address,
    propertyCity: deal.property?.city,
    propertyState: deal.property?.state,
    propertyZipcode: deal.property?.zipcode,
    propertyCountry: deal.property?.country,
    propertyRegion: deal.property?.region,
    propertyMarket: deal.property?.market,
    propertySubmarket: deal.property?.submarket,
    propertyNeighborhood: deal.property?.neighborhood,
    propertyType: deal.property?.propertyType,
    propertySubpropertyType: deal.property?.subpropertyType,
    propertyStatus: deal.property?.propertyStatus,
    propertyBuildingSqft: deal.property?.buildingSqft,
    propertyLandAcres: deal.property?.landAcres,
    propertyLotArea: deal.property?.lotArea,
    propertyYearBuilt: deal.property?.yearBuilt,
    propertyYearRenovated: deal.property?.yearRenovated,
    propertyLatitude: deal.property?.latitude,
    propertyLongitude: deal.property?.longitude,
    propertyNumberOfUnits: deal.property?.numberOfUnits,
    propertyAppraisalValue: deal.property?.appraisalValue,
    propertyAppraisalValueDate: deal.property?.appraisalValueDate,
    propertyGsfGrossSquareFoot: deal.property?.gsfGrossSquareFoot,
    propertyZfaZoningFloorArea: deal.property?.zfaZoningFloorArea,
    propertyFar: deal.property?.far,
    propertyHistoricalOccupancyTrend: deal.property?.historicalOccupancyTrend,
    propertyEnvironmentalRiskScore: deal.property?.environmentalRiskScore,
    propertyDescription: deal.property?.propertyDescription,
    propertyFloorAreaRatio: deal.property?.floorAreaRatio,
    propertyZoningSquareFootage: deal.property?.zoningSquareFootage,
    propertyTotalNsfNetSquareFoot: deal.property?.totalNsfNetSquareFoot
  };

  // Financial fields
  const financialFields = {
    purchasePrice: deal.purchasePrice,
    totalProjectCost: deal.totalProjectCost,
    costPerTotalNsfNetSquareFoot: deal.costPerTotalNsfNetSquareFoot,
    costPerZoningFloorArea: deal.costPerZoningFloorArea
  };

  // Unit-related fields
  const unitFields = {
    numAffordableHousing1bedroomUnits: deal.numAffordableHousing1bedroomUnits,
    numAffordableHousing2bedroomUnits: deal.numAffordableHousing2bedroomUnits,
    numAffordableHousing3bedroomUnits: deal.numAffordableHousing3bedroomUnits,
    numAffordableHousingStudiosUnits: deal.numAffordableHousingStudiosUnits,
    numMarketRate1bedroomUnits: deal.numMarketRate1bedroomUnits,
    numMarketRate2bedroomUnits: deal.numMarketRate2bedroomUnits,
    numMarketRate3bedroomUnits: deal.numMarketRate3bedroomUnits,
    numMarketRateStudiosUnits: deal.numMarketRateStudiosUnits,
    affordableHousingRent1bedroomUnit: deal.affordableHousingRent1bedroomUnit,
    affordableHousingRent2bedroomUnit: deal.affordableHousingRent2bedroomUnit,
    affordableHousingRent3bedroomUnit: deal.affordableHousingRent3bedroomUnit,
    affordableHousingRentStudioUnit: deal.affordableHousingRentStudioUnit,
    marketRateRent1bedroomUnit: deal.marketRateRent1bedroomUnit,
    marketRateRent2bedroomUnit: deal.marketRateRent2bedroomUnit,
    marketRateRent3bedroomUnit: deal.marketRateRent3bedroomUnit,
    affordableHousingSale1bedroomUnit: deal.affordableHousingSale1bedroomUnit,
    affordableHousingSale2bedroomUnit: deal.affordableHousingSale2bedroomUnit,
    affordableHousingSale3bedroomUnit: deal.affordableHousingSale3bedroomUnit,
    affordableHousingSaleStudioUnit: deal.affordableHousingSaleStudioUnit,
    marketRateSale1bedroomUnit: deal.marketRateSale1bedroomUnit,
    marketRateSale2bedroomUnit: deal.marketRateSale2bedroomUnit,
    marketRateSale3bedroomUnit: deal.marketRateSale3bedroomUnit,
    marketRateSaleStudioUnit: deal.marketRateSaleStudioUnit,
    communityFacilityRent: deal.communityFacilityRent,
    communityFacilityRentAdditional: deal.communityFacilityRentAdditional,
    communityFacilitySalePrice: deal.communityFacilitySalePrice,
    communityFacilitySalePriceAdditional: deal.communityFacilitySalePriceAdditional
  };

  // Campaign fields
  const campaignFields = {
    dealCampaignDate: deal.dealCampaignDate,
    dealCampaignEmailsAdditionalInfoRequestedReceived: deal.dealCampaignEmailsAdditionalInfoRequestedReceived,
    dealCampaignEmailsBounceBackReceived: deal.dealCampaignEmailsBounceBackReceived,
    dealCampaignEmailsOutOfOfficeResponseReceived: deal.dealCampaignEmailsOutOfOfficeResponseReceived,
    dealCampaignEmailsResponseReceived: deal.dealCampaignEmailsResponseReceived,
    dealCampaignEmailsSentOut: deal.dealCampaignEmailsSentOut,
    dealCampaignEmailsSoftQuotesReceived: deal.dealCampaignEmailsSoftQuotesReceived,
    dealCampaignEmailsTermSheetsReceived: deal.dealCampaignEmailsTermSheetsReceived,
    dealCampaignStatus: deal.dealCampaignStatus,
    dealCampaignLeadScore: deal.dealCampaignLeadScore,
    dealCampaignPredictedScenario: deal.dealCampaignPredictedScenario
  };

  // Calculate quality for each section
  const overview = calculateSectionQuality(overviewFields);
  const debt = debtQuality; // Use the calculated debt criteria quality
  const equity = equityQuality; // Use the calculated equity criteria quality
  const nsf = calculateSectionQuality(nsfFields);
  const property = calculateSectionQuality(propertyFields);
  const financial = calculateSectionQuality(financialFields);
  const units = calculateSectionQuality(unitFields);
  const campaign = calculateSectionQuality(campaignFields);

  // Add NSF-specific counts
  const nsfFieldsData = deal.nsfFields || deal.nsf_fields || [];
  const sourcesCount = nsfFieldsData.filter(nsf => nsf.nsfContext === 'sources').length;
  const usesCount = nsfFieldsData.filter(nsf => nsf.nsfContext === 'uses_total').length;

  // Add investment criteria counts
  const debtCriteriaCount = deal.investmentCriteriaDebt?.length || 0;
  const equityCriteriaCount = deal.investmentCriteriaEquity?.length || 0;

  return {
    overview,
    debt: {
      ...debt,
      criteriaCount: debtCriteriaCount
    },
    equity: {
      ...equity,
      criteriaCount: equityCriteriaCount
    },
    nsf: {
      ...nsf,
      sourcesCount,
      usesCount
    },
    property,
    financial,
    units,
    campaign
  };
}
