import { BaseProcessor } from './BaseProcessor'
import { EntityData, ProcessorOptions, UnifiedEntityData } from '../../types/processing'
import { LLMFactory, LLMMessage, createProcessorLoggerAdapter, LogLevel } from '../llm'
import { CONTACT_INVESTMENT_CRITERIA_SYSTEM_PROMPT, CONTACT_INVESTMENT_CRITERIA_USER_TEMPLATE_FUNCTION } from '../prompts/contact-investment-criteria'
import { pool } from '../db'

interface ContactInvestmentCriteriaData {
  contactInvestmentCriteria: {
    // Core Identification
    investment_criteria_id?: number | null
    entity_id?: number | null
    entity_type: string
    
    // Relationships to company investment criteria (ONLY if matching company IC)
    investment_criteria_debt_id?: number | null
    investment_criteria_equity_id?: number | null
    
    // Deal Scope - Copy from company IC or extract from web research
    capital_position: string
    minimum_deal_size?: number | null
    maximum_deal_size?: number | null
    
    // Geography - Copy from company IC or extract from web research
    country: string[]
    region: string[]
    state: string[]
    city: string[]
    
    // Asset Strategy - Copy from company IC or extract from web research
    property_types: string[]
    property_subcategories: string[]
    strategies: string[]
    decision_making_process?: string | null
    
    // Additional information
    notes?: string | null
    
    // Timestamps
    created_at: string
    updated_at: string
  }
  
  processing_metadata: {
    data_sources_used: string[]
    selected_company_ic_id?: number | null
    confidence_score: number
    extraction_method: string
    matching_reason?: string
    web_search_queries?: string[]
  }
}

export class ContactInvestmentCriteriaProcessor extends BaseProcessor {
  private llmProvider: any

  constructor(options?: ProcessorOptions) {
    // Perplexity API specific rate limiting configuration for contact investment criteria
    const contactICBottleneckConfig = {
      maxConcurrent: 10,                   // Reduced from 5 to prevent queue overflow
      minTime: 1000,                      // 1 second between requests
      retryAttempts: 3,                   // Standard retries for LLM API
      retryDelayBase: 2500,               // 2.5 second base delay for retries
      retryDelayMax: 40000,               // Max 40 second retry delay
      timeout: 300000,                    // 5 minutes timeout for complex LLM processing
      highWater: 200,                     // Lower queue limit for complex tasks
      strategy: 'OVERFLOW' as any,        // Use OVERFLOW strategy
      defaultPriority: 5,                 // Normal priority for contact IC
      enableJobMetrics: true              // Track LLM API performance
    }

    super('contact_investment_criteria', {
      ...options,
      bottleneckConfig: options?.bottleneckConfig || contactICBottleneckConfig
    })
    
    // Create logger adapter for LLM provider
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this))
    
    // Check if LangSmith tracing should be enabled
    const enableTracing = process.env.LANGSMITH_TRACING !== 'false'

    // Use Perplexity for web-enhanced research capabilities
    this.llmProvider = LLMFactory.createProvider(
      'perplexity',
      loggerAdapter,
      {
        apiKey: process.env.PERPLEXITY_API_KEY,
        enableTracing: enableTracing
      }
    )
  }

  async processEntity(entityData: EntityData): Promise<{ success: boolean; error?: string }> {
    try {
      // Validate that this is contact data
      if (!entityData.id) {
        throw new Error('Entity ID is required for contact investment criteria processing')
      }

      // Get contact enrichment data
      const enrichmentData = await this.getContactEnrichmentData(entityData.id)
      
      // Get company investment criteria if available
      const companyIC = await this.getCompanyInvestmentCriteria((entityData as any).company_id)
      
      // Log the approach being used
      if (companyIC && companyIC.central && companyIC.central.length > 0) {
        this.log('info', `Processing contact ${entityData.id} using COMPANY IC MATCHING approach with ${companyIC.central.length} available records`)
      } else {
        this.log('info', `Processing contact ${entityData.id} using WEB RESEARCH approach - no company IC available`)
      }
      
      // Prepare the prompt with contact data, company IC, and enrichment data
      const userPrompt = CONTACT_INVESTMENT_CRITERIA_USER_TEMPLATE_FUNCTION(entityData, companyIC, enrichmentData)
      
      // Extract investment criteria using LLM
      const messages: LLMMessage[] = [
        { role: 'system', content: CONTACT_INVESTMENT_CRITERIA_SYSTEM_PROMPT },
        { role: 'user', content: userPrompt }
      ]
      
      const response = await this.llmProvider.callLLM(messages, {
        temperature: 0.1,
        maxTokens: 4000
      })

      // Parse the response
      const parsedData = this.parseResponse(response.content)
      if (!parsedData) {
        throw new Error('Failed to parse LLM response for contact investment criteria')
      }

      // Set the entity_id to the contact ID
      parsedData.contactInvestmentCriteria.entity_id = entityData.id

      // Log the processing method used
      const method = parsedData.processing_metadata.extraction_method
      const selectedId = parsedData.processing_metadata.selected_company_ic_id
      this.log('info', `Contact ${entityData.id} processed using ${method}${selectedId ? ` with selected company IC ID: ${selectedId}` : ''}`)

      // Save to database
      await this.saveContactInvestmentCriteria(parsedData)

      return { success: true }
    } catch (error) {
      console.error(`Error processing contact investment criteria for contact ${entityData.id}:`, error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  private parseResponse(response: string): ContactInvestmentCriteriaData | null {
    try {
      // Clean the response to extract JSON
      let cleanedResponse = response.trim()
      
      // Remove markdown code blocks if present
      if (cleanedResponse.startsWith('```json')) {
        cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '')
      } else if (cleanedResponse.startsWith('```')) {
        cleanedResponse = cleanedResponse.replace(/^```\s*/, '').replace(/\s*```$/, '')
      }
      
      // Parse JSON
      const parsed = JSON.parse(cleanedResponse)
      
      // Validate structure
      if (!parsed.contactInvestmentCriteria) {
        throw new Error('Missing contactInvestmentCriteria in response')
      }

      // Validate processing metadata
      if (!parsed.processing_metadata) {
        throw new Error('Missing processing_metadata in response')
      }

      // Validate extraction method
      const validMethods = ['company_ic_matching', 'web_research']
      if (!validMethods.includes(parsed.processing_metadata.extraction_method)) {
        throw new Error(`Invalid extraction_method: ${parsed.processing_metadata.extraction_method}`)
      }

      return parsed as ContactInvestmentCriteriaData
    } catch (error) {
      console.error('Error parsing contact investment criteria response:', error)
      console.error('Raw response:', response)
      return null
    }
  }

  private async getContactEnrichmentData(contactId: number): Promise<any> {
    try {
      const client = await pool.connect()
      
      try {
        const result = await client.query(`
          SELECT 
            title,
            executive_summary,
            honorable_achievements,
            career_timeline,
            additional_email,
            phone_number_secondary,
            twitter,
            facebook,
            instagram,
            youtube,
            education_college,
            education_college_year_graduated,
            education_high_school,
            education_high_school_year_graduated,
            hobbies,
            age,
            contact_address,
            contact_zip_code,
            contact_type,
            relationship_owner,
            role_in_decision_making,
            source_of_introduction,
            accredited_investor_status
          FROM contacts 
          WHERE contact_id = $1
        `, [contactId])
        
        if (result.rows.length === 0) {
          return {}
        }
        
        const row = result.rows[0]
        return {
          // Basic contact info
          title: row.title,
          
          // V2 Enrichment data
          executive_summary: row.executive_summary,
          honorable_achievements: row.honorable_achievements,
          career_timeline: row.career_timeline,
          
          // Contact information
          additional_email: row.additional_email,
          phone_number_secondary: row.phone_number_secondary,
          
          // Social media
          twitter: row.twitter,
          facebook: row.facebook,
          instagram: row.instagram,
          youtube: row.youtube,
          
          // Education
          education_college: row.education_college,
          education_college_year_graduated: row.education_college_year_graduated,
          education_high_school: row.education_high_school,
          education_high_school_year_graduated: row.education_high_school_year_graduated,
          
          // Personal details
          hobbies: row.hobbies,
          age: row.age,
          
          // Location details
          contact_address: row.contact_address,
          contact_zip_code: row.contact_zip_code,
          
          // Contact metadata
          contact_type: row.contact_type,
          relationship_owner: row.relationship_owner,
          role_in_decision_making: row.role_in_decision_making,
          
          // Interaction tracking
          source_of_introduction: row.source_of_introduction,
          
          // Compliance
          accredited_investor_status: row.accredited_investor_status
        }
      } finally {
        client.release()
      }
    } catch (error) {
      console.error('Error fetching contact enrichment data:', error)
      return {}
    }
  }

  private async getCompanyInvestmentCriteria(companyId?: number): Promise<any> {
    if (!companyId) {
      return null
    }

    try {
      const client = await pool.connect()
      
      try {
        // Get company investment criteria central records only
        const centralResult = await client.query(`
          SELECT *
          FROM investment_criteria_central 
          WHERE entity_id = $1 AND entity_type = 'company'
          ORDER BY created_at DESC
        `, [companyId])
        
        if (centralResult.rows.length === 0) {
          return null
        }

        // Return only central records for matching approach
        return {
          central: centralResult.rows
        }
      } finally {
        client.release()
      }
    } catch (error) {
      console.error('Error fetching company investment criteria:', error)
      return null
    }
  }

  /**
   * Convert JavaScript array to PostgreSQL array literal format
   */
  private arrayToPostgresArray(arr: string[] | null | undefined): string | null {
    if (!arr || arr.length === 0) {
      return null
    }
    
    // Escape quotes and wrap values that contain spaces or special characters
    const escapedValues = arr.map(value => {
      if (typeof value !== 'string') {
        return String(value)
      }
      
      // If value contains spaces, commas, quotes, or special characters, wrap in quotes and escape
      if (value.includes(' ') || value.includes(',') || value.includes('"') || value.includes('\'') || value.includes('{') || value.includes('}')) {
        return `"${value.replace(/"/g, '\\"')}"` 
      }
      
      return value
    })
    
    return `{${escapedValues.join(',')}}`
  }

  private async saveContactInvestmentCriteria(data: ContactInvestmentCriteriaData): Promise<void> {
    try {
      const client = await pool.connect()
      
      try {
        const ic = data.contactInvestmentCriteria
        
        // Always insert a new record for contact IC (do not overwrite existing)
        await client.query(`
          INSERT INTO investment_criteria_central (
            entity_id,
            entity_type,
            investment_criteria_debt_id,
            investment_criteria_equity_id,
            capital_position,
            minimum_deal_size,
            maximum_deal_size,
            country,
            region,
            state,
            city,
            property_types,
            property_subcategories,
            strategies,
            decision_making_process,
            notes,
            created_at,
            updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, NOW(), NOW())
        `, [
          ic.entity_id,
          ic.entity_type,
          ic.investment_criteria_debt_id,
          ic.investment_criteria_equity_id,
          ic.capital_position,
          ic.minimum_deal_size,
          ic.maximum_deal_size,
          this.arrayToPostgresArray(ic.country),
          this.arrayToPostgresArray(ic.region),
          this.arrayToPostgresArray(ic.state),
          this.arrayToPostgresArray(ic.city),
          this.arrayToPostgresArray(ic.property_types),
          this.arrayToPostgresArray(ic.property_subcategories),
          this.arrayToPostgresArray(ic.strategies),
          ic.decision_making_process,
          ic.notes
        ])

        // Update contact processing status
        await client.query(`
          UPDATE contacts 
          SET 
            contact_investment_criteria_status = 'completed',
            contact_investment_criteria_date = NOW(),
            contact_investment_criteria_error = NULL,
            contact_investment_criteria_error_count = 0
          WHERE contact_id = $1
        `, [ic.entity_id])

      } finally {
        client.release()
      }
    } catch (error) {
      console.error('Error saving contact investment criteria:', error)
      
      // Update error status
      if (data.contactInvestmentCriteria.entity_id) {
        try {
          const client = await pool.connect()
          
          try {
            await client.query(`
              UPDATE contacts 
              SET 
                contact_investment_criteria_status = 'failed',
                contact_investment_criteria_error = $1,
                contact_investment_criteria_error_count = COALESCE(contact_investment_criteria_error_count, 0) + 1
              WHERE contact_id = $2
            `, [error instanceof Error ? error.message : 'Unknown error', data.contactInvestmentCriteria.entity_id])
          } finally {
            client.release()
          }
        } catch (updateError) {
          console.error('Error updating contact error status:', updateError)
        }
      }
      
      throw error
    }
  }

  async getUnprocessedEntities(): Promise<UnifiedEntityData[]> {
    const baseFilters = {
      contactEnrichmentV2Status: 'completed'
    }

    const specificFilters = {
      contactInvestmentCriteriaStatus: 'pending'
    }

    return await this.getUnprocessedUnifiedEntities(baseFilters, specificFilters, 'contact')
  }

  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    const status = success ? 'completed' : 'failed'
    await this.updateProcessingStatus(entityId, status, error)
  }

  async updateProcessingStatus(entityId: number, status: 'pending' | 'running' | 'completed' | 'failed' | 'error', error?: string): Promise<void> {
    try {
      const client = await pool.connect()
      
      try {
        if (status === 'running') {
          await client.query(`
            UPDATE contacts 
            SET contact_investment_criteria_status = $1
            WHERE contact_id = $2
          `, [status, entityId])
        } else if (status === 'completed') {
          await client.query(`
            UPDATE contacts 
            SET 
              contact_investment_criteria_status = $1,
              contact_investment_criteria_date = NOW(),
              contact_investment_criteria_error = NULL,
              contact_investment_criteria_error_count = 0
            WHERE contact_id = $2
          `, [status, entityId])
        } else if (status === 'failed' || status === 'error') {
          await client.query(`
            UPDATE contacts 
            SET 
              contact_investment_criteria_status = $1,
              contact_investment_criteria_error = $2,
              contact_investment_criteria_error_count = COALESCE(contact_investment_criteria_error_count, 0) + 1
            WHERE contact_id = $3
          `, [status, error || 'Unknown error', entityId])
        }
      } finally {
        client.release()
      }
    } catch (dbError) {
      console.error('Error updating contact investment criteria processing status:', dbError)
      throw dbError
    }
  }
}
