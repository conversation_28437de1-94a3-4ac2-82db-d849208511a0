import { BaseProcessor } from './BaseProcessor'
import { ProcessorOptions, UnifiedEntityData } from '../../types/processing'
import { EMAIL_GENERATION_SYSTEM_PROMPT, EMAIL_GENERATION_USER_TEMPLATE } from '../prompts/email-generation'
import { LLMFactory, LLMMessage, createProcessorLoggerAdapter, LogLevel } from '../llm'

interface GeneratedEmail {
  subject1: string
  body1: string
  subject2: string
  body2: string
  subject3: string
  body3: string
}

interface ContactEnrichmentData {
  osint_profile?: string
  executive_summary?: string
  career_timeline?: any[]
  notable_activities?: any[]
  education?: any[]
  personal_tidbits?: any[]
  conversation_hooks?: any[]
  sources?: any[]
  company_type?: string
  capital_positions?: any[]
  confidence?: number
  reasoning?: string
}

interface CompanyOverviewData {
  companyname?: string
  companytype?: string
  businessmodel?: string
  fundsize?: string
  aum?: string
  numberofproperties?: number
  headquarters?: string
  numberofoffices?: number
  foundedyear?: number
  numberofemployees?: string
  investmentfocus?: any[]
  geographicfocus?: any[]
  recentdeals?: any[]
  mission?: string
  approach?: string
  targetreturn?: string
  propertytypes?: any[]
  strategies?: any[]
}

interface InvestmentCriteriaData {
  criteria_id?: number
  target_return?: number
  property_types?: string[]
  property_sub_categories?: string[]
  strategies?: string[]
  minimum_deal_size?: number
  maximum_deal_size?: number
  min_hold_period?: number
  max_hold_period?: number
  financial_products?: string[]
  historical_irr?: number
  historical_em?: number
  country?: string[]
  region?: string[]
  state?: string[]
  city?: string[]
  loan_program?: string[]
  loan_type?: string[]
}

interface MatchingDeal {
  deal_id: string | number
  deal_name?: string
  score?: number
  breakdown?: Array<{
    field: string
    score: number
    reason: string
    weight?: number
  }>
  reasons?: string[]
  scoring_method?: string
  location?: string
  deal_size?: number
  property_type?: string
  capital_position?: string
  city?: string
  state?: string
  ltc_ratio?: number
  ltv_ratio?: number
  dscr?: number
  loan_term?: number
  target_close_date?: string
  irr?: number
  equity_multiple?: number
  hold_period?: number
  deal_strategy?: string
  // Additional fields from the API
  minimum_deal_size?: number
  maximum_deal_size?: number
  historical_irr?: number
  historical_em?: number
  deal_region?: string
  deal_capital_position?: string
  deal_property_types?: string[]
  deal_strategies?: string[]
  deal_financial_products?: string[]
  deal_loan_type?: string
  deal_loan_program?: string
  deal_recourse_loan?: string
  min_hold_period?: number
  max_hold_period?: number
  min_loan_term?: number
  max_loan_term?: number
  interest_rate?: number
  loan_to_value_min?: number
  loan_to_value_max?: number
  loan_to_cost_min?: number
  loan_to_cost_max?: number
  loan_origination_fee_min?: number
  loan_origination_fee_max?: number
  loan_exit_fee_min?: number
  loan_exit_fee_max?: number
  min_loan_dscr?: number
  max_loan_dscr?: number
  extra_fields?: any
  // New fields from enhanced API with investment criteria
  matching_criteria_count?: number
  all_criteria_matches?: Array<{
    criteria_id: number
    score: number
    breakdown: Array<{
      field: string
      score: number
      weight: number
      reason: string
      confidence?: number
    }>
    reasons: string[]
    weightSum: number
    investment_criteria?: {
      target_return?: number
      deal_size?: {
        min?: string
        max?: string
        unit: string
      }
      historical_performance?: {
        irr?: number
        equity_multiple?: number
      }
      location?: {
        country?: string[]
        region?: string[]
        state?: string[]
        city?: string[]
      }
      capital_position?: string[]
      property_types?: string[]
      property_sub_categories?: string[]
      strategies?: string[]
      financial_products?: string[]
      loan_terms?: {
        type?: string[]
        program?: string[]
        recourse?: string[]
        min_hold_period?: number
        max_hold_period?: number
        min_loan_term?: string
        max_loan_term?: string
      }
      loan_metrics?: {
        interest_rate?: number
        ltv_min?: string
        ltv_max?: string
        ltc_min?: string
        ltc_max?: string
        origination_fee_min?: number
        origination_fee_max?: number
        exit_fee_min?: number
        exit_fee_max?: number
        dscr_min?: number
        dscr_max?: number
      }
    }
  }>
  // Additional fields from actual deals table
  sponsor_name?: string
  deal_stage?: string
  priority?: string
  property_description?: string
  yield_on_cost?: number
  projected_gp_equity_multiple?: number
  projected_gp_irr?: number
  projected_lp_equity_multiple?: number
  projected_lp_irr?: number
  projected_total_equity_multiple?: number
  projected_total_irr?: number
  extraction_confidence?: string
}

interface MatchingNews {
  news_id: string | number
  headline?: string
  summary?: string
  deal_size?: number
  publication_date?: string
  source_name?: string
  source_url?: string
  location_city?: string
  location_state?: string
  property_type?: string
  deal_type?: string
  buyer_name?: string
  seller_name?: string
  score?: number
  breakdown?: Array<{
    field: string
    score: number
    reason: string
    weight?: number
  }>
  reasons?: string[]
  // Additional fields from the API
  enrichment_id?: string | number
  title?: string
  content?: string
  url?: string
  published_at?: string
  company_name?: string
  deal_amount?: string
  property_types?: string[]
  locations?: string[]
  transaction_type?: string
  parties_involved?: string[]
}

interface CategorizedCriteria {
  criteria_id: number
  capital_position: string
  state: string[]
  deal_size: string
  property_type: string[]
  strategy: string[]
  target_return?: number
}

export class EmailGenerationProcessor extends BaseProcessor {
  private llmProvider;

  constructor(options: ProcessorOptions = {}) {
    super('EmailGeneration', options)

    // Create a logger adapter that uses our log method
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this));

    // Check if LangSmith tracing should be enabled
    const enableTracing = process.env.LANGSMITH_TRACING !== 'false'

    // Create LLM provider with OpenAI as primary and Perplexity as fallback
    // Email generation typically works better with OpenAI/GPT models
    this.llmProvider = LLMFactory.createProvider(
      'openai',
      loggerAdapter,
      {
        apiKey: process.env.OPENAI_API_KEY,
        enableTracing: enableTracing
      }
    );
  }

  async getUnprocessedEntities(): Promise<UnifiedEntityData[]> {
    const baseFilters = {
      contactEnrichmentStatus: 'completed'
    }
    const specificFilters = {
      emailGenerationStatus: 'pending'
    }
    return await this.getUnprocessedUnifiedEntities(baseFilters, specificFilters, 'contact')
  }

  getProcessingStateCondition(): string {
    return `processing_state = 'enrichment_completed'`
  }

  async processEntity(entity: UnifiedEntityData): Promise<{ success: boolean; error?: string }> {
    // Only process contacts for email generation
    if (entity.entity_type !== 'contact') {
      return { success: false, error: 'Email generation only supports contacts' }
    }

    try {
      this.log('info', `Generating email for contact: ${entity.first_name} ${entity.last_name} (${entity.email})`)

      // Set status to running
      await this.updateEmailGenerationStatus(entity.contact_id!, 'running')

      // Get comprehensive context data for the contact
      const contactEnrichmentData = await this.getContactEnrichmentData(entity.contact_id!)
      const companyOverviewData = await this.getCompanyOverviewData(entity.company_id!)
      const investmentCriteriaData = await this.getInvestmentCriteriaData(entity.contact_id!, entity.company_id!)

      // Get matching deals and news for personalization
      const matchingNews = await this.getMatchingNewsForContact(entity.contact_id!)

      // Categorize investment criteria from matching deals using the API route
      const matchingDeals = await this.getCategorizedCriteriaFromAPI(entity.contact_id!)

      if (!contactEnrichmentData) {
        return { success: false, error: 'No contact enrichment data available' }
      }
      // Check if we have relevant matching deals and news
      this.log('debug', `Checking relevance for contact ${entity.contact_id}: ${matchingDeals.length} deals, ${matchingNews.length} news items`)

      const hasRelevantDeals = matchingDeals.length > 0

      const hasRelevantNews = matchingNews.length > 0

      this.log('debug', `Relevance check for contact ${entity.contact_id}: hasRelevantDeals=${hasRelevantDeals}, hasRelevantNews=${hasRelevantNews}`)

      // Skip email generation if no relevant deals or news found
      if (!hasRelevantDeals && !hasRelevantNews) {
        this.log('info', `Skipping email generation for contact ${entity.contact_id}: No relevant deals or news found`)
        await this.updateEmailGenerationStatus(entity.contact_id!, 'skipped', 'No relevant deals or news found')
        return { success: true } // Mark as successful but skipped
      }

      // Generate personalized email
      const generatedEmail = await this.generatePersonalizedEmail(
        entity,
        contactEnrichmentData,
        companyOverviewData,
        investmentCriteriaData,
        matchingDeals.slice(0, 10),
        matchingNews.slice(0, 15)
      )

      if (!generatedEmail) {
        return { success: false, error: 'Failed to generate email' }
      }

      // Store generated email in messages table
      await this.storeGeneratedEmail(entity.contact_id!, generatedEmail)

      // Auto-sync to Smartlead if configured
      await this.syncToSmartlead(entity.contact_id!, generatedEmail, entity)

      this.log('info', `Successfully generated email for contact ${entity.contact_id}`)

      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error generating email for contact ${entity.contact_id}: ${errorMessage}`)
      return { success: false, error: errorMessage }
    }
  }

  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    if (success) {
      // Check if the status was already set to 'skipped' in processEntity
      const currentStatus = await this.getCurrentEmailGenerationStatus(entityId)
      if (currentStatus !== 'skipped') {
        // Email generation completed successfully
        await this.updateEmailGenerationStatus(entityId, 'completed')
      }
      // If status is 'skipped', leave it as is (already set in processEntity)
    } else {
      // Email generation failed
      await this.updateEmailGenerationStatus(entityId, 'failed', error)
      await this.incrementProcessingErrorCount('contact', entityId)
    }
  }

  /**
   * Get contact enrichment data from contact_enrichment table
   */
  private async getContactEnrichmentData(contactId: number): Promise<ContactEnrichmentData | null> {
    try {
      const sql = `
        SELECT 
          osint_profile,
          executive_summary,
          career_timeline,
          notable_activities,
          education,
          personal_tidbits,
          conversation_hooks,
          sources,
          company_type,
          capital_positions,
          confidence,
          reasoning
        FROM contact_enrichment
        WHERE contact_id = $1 AND status = 'completed'
        ORDER BY created_at DESC
        LIMIT 1
      `
      const result = await this.query(sql, [contactId])

      if (result.length === 0) {
        this.log('warn', `No contact enrichment data found for contact ${contactId}`)
        return null
      }

      return result[0] as ContactEnrichmentData
    } catch (error) {
      this.log('error', `Error fetching contact enrichment data for ${contactId}: ${error}`)
      return null
    }
  }

  /**
   * Get company overview data from company_extracted_data table
   */
  private async getCompanyOverviewData(companyId: number): Promise<CompanyOverviewData | null> {
    try {
      const sql = `
        SELECT 
          companyname,
          companytype,
          businessmodel,
          fundsize,
          aum,
          numberofproperties,
          headquarters,
          numberofoffices,
          foundedyear,
          numberofemployees,
          investmentfocus,
          geographicfocus,
          recentdeals,
          mission,
          approach,
          targetreturn,
          propertytypes,
          strategies
        FROM company_extracted_data
        WHERE company_id = $1
        ORDER BY created_at DESC
        LIMIT 1
      `
      const result = await this.query(sql, [companyId])

      if (result.length === 0) {
        this.log('warn', `No company overview data found for company ${companyId}`)
        return null
      }

      return result[0] as CompanyOverviewData
    } catch (error) {
      this.log('error', `Error fetching company overview data for ${companyId}: ${error}`)
      return null
    }
  }

  /**
   * Get investment criteria data from investment_criteria table
   */
  private async getInvestmentCriteriaData(contactId: number, companyId: number): Promise<InvestmentCriteriaData[]> {
    try {
      const sql = `
        SELECT 
          criteria_id,
          target_return,
          property_types,
          property_sub_categories,
          strategies,
          minimum_deal_size,
          maximum_deal_size,
          min_hold_period,
          max_hold_period,
          financial_products,
          historical_irr,
          historical_em,
          country,
          region,
          state,
          city,
          loan_program,
          loan_type
        FROM investment_criteria
        WHERE (
          (entity_type = 'Contact' AND entity_id = $1::text) OR
          (entity_type LIKE 'Company%' AND entity_id = $2::text)
        )
        AND (is_active IS NULL OR is_active = true)
        ORDER BY created_at DESC
      `
      const result = await this.query(sql, [contactId, companyId])

      if (result.length === 0) {
        this.log('warn', `No investment criteria data found for contact ${contactId} or company ${companyId}`)
        return []
      }

      return result as InvestmentCriteriaData[]
    } catch (error) {
      this.log('error', `Error fetching investment criteria data for contact ${contactId}, company ${companyId}: ${error}`)
      return []
    }
  }

  /**
   * Get matching deals for contact using the existing API endpoint with investment criteria data
   */
  private async getMatchingDealsForContact(contactId: number): Promise<MatchingDeal[]> {
    try {
      this.log('debug', `Fetching matching deals for contact ${contactId} with investment criteria data and CRM mode`)

      // Use the API endpoint with investment criteria data and CRM mode enabled
      const response = await fetch(`${process.env.API_BASE_URL || 'http://localhost:3030'}/api/matching/deals-for-contact/${contactId}?add_ic_data=true&is_crm=true`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (!data.matches || !Array.isArray(data.matches)) {
        this.log('debug', `No matching deals found for contact ${contactId}`)
        return []
      }

      // Get actual deal data from the deals table to ensure unique deal information
      const dealIds = data.matches.map((deal: any) => deal.deal_id).filter(Boolean)

      if (dealIds.length === 0) {
        this.log('debug', `No valid deal IDs found for contact ${contactId}`)
        return []
      }

      // Fetch only essential deal data from the deals table
      const dealsSql = `
        SELECT 
          deal_id,
          deal_name,
          sponsor_name,
          deal_stage,
          priority,
          property_description,
          yield_on_cost,
          projected_gp_irr,
          projected_lp_irr,
          projected_total_irr,
          extraction_confidence,
          extra_fields
        FROM deals 
        WHERE deal_id = ANY($1)
        ORDER BY deal_id
      `

      const dealsResult = await this.query(dealsSql, [dealIds])
      const dealsMap = new Map(dealsResult.map((deal: any) => [deal.deal_id, deal]))

      // Merge API matching data with actual deal data
      const enrichedDeals = data.matches.map((match: any) => {
        const actualDeal = dealsMap.get(match.deal_id)
        if (!actualDeal) {
          // this.log('warn', `No actual deal data found for deal_id ${match.deal_id}`)
          return match
        }

        // Merge the matching data with essential deal data
        return {
          ...match,
          // Use actual deal data for essential information
          deal_name: actualDeal.deal_name || match.deal_name,
          sponsor_name: actualDeal.sponsor_name,
          deal_stage: actualDeal.deal_stage,
          priority: actualDeal.priority,
          property_description: actualDeal.property_description,
          yield_on_cost: actualDeal.yield_on_cost,
          projected_gp_irr: actualDeal.projected_gp_irr,
          projected_lp_irr: actualDeal.projected_lp_irr,
          projected_total_irr: actualDeal.projected_total_irr,
          extraction_confidence: actualDeal.extraction_confidence,
          extra_fields: actualDeal.extra_fields
        }
      })

      this.log('debug', `Found ${data.matches.length} total deals (CRM filtered), enriched with actual deal data for contact ${contactId}`)
      return enrichedDeals as MatchingDeal[]
    } catch (error) {
      this.log('error', `Error fetching matching deals for contact ${contactId}: ${error}`)
      return []
    }
  }

  /**
   * Get matching news for contact using the existing API endpoint
   */
  private async getMatchingNewsForContact(contactId: number): Promise<MatchingNews[]> {
    try {
      this.log('debug', `Fetching matching news for contact ${contactId}`)

      // Use the existing API endpoint
      const response = await fetch(`${process.env.API_BASE_URL || 'http://localhost:3030'}/api/matching/news-for-contact/${contactId}`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (!data.matches || !Array.isArray(data.matches)) {
        this.log('debug', `No matching news found for contact ${contactId}`)
        return []
      }

      this.log('debug', `Found ${data.matches.length} matching news articles for contact ${contactId}`)
      return data.matches as MatchingNews[]
    } catch (error) {
      this.log('error', `Error fetching matching news for contact ${contactId}: ${error}`)
      return []
    }
  }





  /**
   * Get categorized criteria from the API route
   */
  private async getCategorizedCriteriaFromAPI(contactId: number): Promise<CategorizedCriteria[]> {
    try {
      this.log('debug', `Fetching categorized criteria from API for contact ${contactId}`)

      const response = await fetch(`${process.env.API_BASE_URL || 'http://localhost:3030'}/api/deals/contact-email-support/categorization`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ contactId })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (!data.success) {
        throw new Error(`API error: ${data.error || 'Unknown error'}`)
      }

      this.log('debug', `Successfully fetched categorized criteria: ${data.data.analysis.debtPositions} debt, ${data.data.analysis.equityPositions} equity positions`)

      // Log the categorized criteria for debugging

      return data.data.categorizedCriteria
    } catch (error) {
      this.log('error', `Error fetching categorized criteria from API: ${error}`)
      throw error
    }
  }

  /**
   * Generate personalized email using AI
   */
  private async generatePersonalizedEmail(
    contact: UnifiedEntityData,
    contactEnrichmentData: ContactEnrichmentData,
    companyOverviewData: CompanyOverviewData | null,
    investmentCriteriaData: InvestmentCriteriaData[],
    matchingDeals: CategorizedCriteria[],
    matchingNews: MatchingNews[]
  ): Promise<GeneratedEmail | null> {
    try {
      const messages = this.buildEmailGenerationMessages(
        contact,
        contactEnrichmentData,
        companyOverviewData,
        investmentCriteriaData,
        matchingDeals,
        matchingNews
      )

      // Use the LLM provider with fallback support
      const response = await this.rateLimitedCall(async () => {
        return await this.llmProvider.callLLM(messages);
      }, 3000) // 3 second delay between API calls

      if (!response.content) {
        throw new Error(`No response content from ${response.provider}`);
      }

      this.log('info', `Generated email using ${response.provider} (${response.model})`);

      return this.parseEmailResponse(response.content)
    } catch (error) {
      this.log('error', `Error calling LLM email generation API: ${error}`)
      return null
    }
  }

  /**
   * Build messages for email generation with all required parameters
   */
  private buildEmailGenerationMessages(
    contact: UnifiedEntityData,
    contactEnrichmentData: ContactEnrichmentData,
    companyOverviewData: CompanyOverviewData | null,
    investmentCriteriaData: InvestmentCriteriaData[],
    matchingDeals: CategorizedCriteria[],
    matchingNews: MatchingNews[]
  ): LLMMessage[] {
    // Determine sender context (this would typically come from user/company settings)
    const senderInfo = {
      name: process.env.SENDER_NAME || 'Your Name',
      company: process.env.SENDER_COMPANY || 'Your Company',
      title: process.env.SENDER_TITLE || 'Business Development'
    }
    console.log('senderInfo', senderInfo)

    // Build contact summary
    let contactSummary = `**Contact:** ${contact.first_name} ${contact.last_name}`
    if (contact.title) contactSummary += `\n**Title:** ${contact.title}`
    if (contact.company_name) contactSummary += `\n**Company:** ${contact.company_name}`
    if (contact.industry) contactSummary += `\n**Industry:** ${contact.industry}`

    // Build lead enrichment data summary
    let leadEnrichmentData = ''
    if (contactEnrichmentData.executive_summary) {
      leadEnrichmentData += `**Executive Summary:** ${contactEnrichmentData.executive_summary}\n`
    }

    if (contactEnrichmentData.conversation_hooks && contactEnrichmentData.conversation_hooks.length > 0) {
      leadEnrichmentData += `**Conversation Hooks:** ${contactEnrichmentData.conversation_hooks.slice(0, 3).join(', ')}\n`
    }

    if (contactEnrichmentData.notable_activities && contactEnrichmentData.notable_activities.length > 0) {
      leadEnrichmentData += `**Notable Activities:** ${contactEnrichmentData.notable_activities.slice(0, 2).join(', ')}\n`
    }

    if (contactEnrichmentData.personal_tidbits && contactEnrichmentData.personal_tidbits.length > 0) {
      leadEnrichmentData += `**Personal Tidbits:** ${contactEnrichmentData.personal_tidbits.slice(0, 2).join(', ')}\n`
    }

    if (contactEnrichmentData.company_type) {
      leadEnrichmentData += `**Company Type:** ${contactEnrichmentData.company_type}\n`
    }

    if (contactEnrichmentData.capital_positions && contactEnrichmentData.capital_positions.length > 0) {
      leadEnrichmentData += `**Capital Positions:** ${contactEnrichmentData.capital_positions.join(', ')}\n`
    }

    // Build company overview data summary
    let companyOverviewDataSummary = ''
    if (companyOverviewData) {
      if (companyOverviewData.companytype) {
        companyOverviewDataSummary += `**Company Type:** ${companyOverviewData.companytype}\n`
      }
      if (companyOverviewData.businessmodel) {
        companyOverviewDataSummary += `**Business Model:** ${companyOverviewData.businessmodel}\n`
      }
      if (companyOverviewData.fundsize) {
        companyOverviewDataSummary += `**Fund Size:** ${companyOverviewData.fundsize}\n`
      }
      if (companyOverviewData.aum) {
        companyOverviewDataSummary += `**AUM:** ${companyOverviewData.aum}\n`
      }
      if (companyOverviewData.investmentfocus && companyOverviewData.investmentfocus.length > 0) {
        companyOverviewDataSummary += `**Investment Focus:** ${companyOverviewData.investmentfocus.slice(0, 3).join(', ')}\n`
      }
      if (companyOverviewData.geographicfocus && companyOverviewData.geographicfocus.length > 0) {
        companyOverviewDataSummary += `**Geographic Focus:** ${companyOverviewData.geographicfocus.slice(0, 3).join(', ')}\n`
      }
      if (companyOverviewData.recentdeals && companyOverviewData.recentdeals.length > 0) {
        companyOverviewDataSummary += `**Recent Deals:** ${companyOverviewData.recentdeals.slice(0, 2).map((deal: any) =>
          typeof deal === 'string' ? deal : JSON.stringify(deal)
        ).join(', ')}\n`
      }
      if (companyOverviewData.mission) {
        companyOverviewDataSummary += `**Mission:** ${companyOverviewData.mission}\n`
      }
      if (companyOverviewData.targetreturn) {
        companyOverviewDataSummary += `**Target Return:** ${companyOverviewData.targetreturn}\n`
      }
    }

    // Build investment criteria data summary
    let investmentCriteriaDataSummary = ''
    if (investmentCriteriaData.length > 0) {
      investmentCriteriaDataSummary += `**Investment Criteria:**\n`
      investmentCriteriaData.forEach((criteria, index) => {
        if (index < 3) { // Limit to first 3 criteria
          investmentCriteriaDataSummary += `\n**Criteria ${index + 1}:**\n`
          if (criteria.target_return) {
            investmentCriteriaDataSummary += `- Target Return: ${criteria.target_return}\n`
          }
          if (criteria.property_types && criteria.property_types.length > 0) {
            investmentCriteriaDataSummary += `- Property Types: ${criteria.property_types.join(', ')}\n`
          }
          if (criteria.strategies && criteria.strategies.length > 0) {
            investmentCriteriaDataSummary += `- Strategies: ${criteria.strategies.join(', ')}\n`
          }
          if (criteria.minimum_deal_size || criteria.maximum_deal_size) {
            investmentCriteriaDataSummary += `- Deal Size: ${criteria.minimum_deal_size || 'N/A'} - ${criteria.maximum_deal_size || 'N/A'}\n`
          }
          if (criteria.state && criteria.state.length > 0) {
            investmentCriteriaDataSummary += `- Geographic Focus: ${criteria.state.join(', ')}\n`
          }
        }
      })
    }

    // Build categorized investment criteria summary
    let categorizedCriteriaSummary = ''
    const debtCapitalPositions = ['Mezzanine', 'Senior Debt', 'Stretch Senior']
    const hasCategorizedCriteria = matchingDeals.length > 0

    if (hasCategorizedCriteria) {
      categorizedCriteriaSummary += `**Matching Deals Data:**\n\n`
      matchingDeals.forEach((position, index) => {
        const capitalPositions = Array.isArray(position.capital_position) ? position.capital_position : [position.capital_position]
        const hasDebtPosition = capitalPositions.some(pos => debtCapitalPositions.includes(pos))
        categorizedCriteriaSummary += `${index + 1}. **${capitalPositions.join(', ')}**\n`
        if (position.state.length > 0) {
          categorizedCriteriaSummary += `   - State: ${position.state.slice(0, 3).join(', ')}\n`
        }
        categorizedCriteriaSummary += `   - Deal Size: ${position.deal_size}\n`
        if (position.property_type.length > 0) {
          categorizedCriteriaSummary += `   - Property Type: ${position.property_type.slice(0, 3).join(', ')}\n`
        }
        if (position.strategy.length > 0) {
          categorizedCriteriaSummary += `   - Strategy: ${position.strategy[0]}\n`
        }
        if (position.target_return && !hasDebtPosition) {
          categorizedCriteriaSummary += `   - Target Return: ${(position.target_return * 100).toFixed(1)}%\n`
        }
        categorizedCriteriaSummary += `\n`
      })
    }

    // Build matching news summary for email 3
    // console.log('Matching news:', JSON.stringify(matchingNews, null, 2))
    let matchingNewsData = ''
    if (matchingNews.length > 0) {
      matchingNewsData += `**Relevant Market News (Top ${Math.min(matchingNews.length, 5)}):**\n`
      matchingNews.forEach((news, index) => {
        const headline = news.headline || news.title || 'Market Update'
        const dealSize = news.deal_size || news.deal_amount
        const location = news.location_city || news.location_state || news.locations?.join(', ')
        const propertyType = news.property_type || news.property_types?.join(', ')
        const parties = news.buyer_name || news.seller_name || news.parties_involved?.join(', ')
        const summary = news.summary || news.content

        matchingNewsData += `\n**News ${index + 1}:** ${headline}\n`
        if (dealSize) matchingNewsData += `- Deal Size: $${dealSize}M\n`
        if (location) matchingNewsData += `- Location: ${location}\n`
        if (propertyType) matchingNewsData += `- Property Type: ${propertyType}\n`
        if (parties) matchingNewsData += `- Parties: ${parties}\n`
        if (summary) matchingNewsData += `- Summary: ${summary.substring(0, 150)}...\n`
        if (news.score) matchingNewsData += `- Relevance Score: ${news.score}%\n`
      })
    } else {
      matchingNewsData = '**Market News:** Stay tuned for relevant market insights matching your investment criteria.'
    }

    // Use the updated EMAIL_GENERATION_USER_TEMPLATE with all 8 parameters
    const userPrompt = EMAIL_GENERATION_USER_TEMPLATE(
      contactSummary,
      senderInfo,
      leadEnrichmentData,
      companyOverviewDataSummary,
      investmentCriteriaDataSummary,
      categorizedCriteriaSummary,
      matchingNewsData
    )

    return [
      { role: 'system', content: EMAIL_GENERATION_SYSTEM_PROMPT },
      { role: 'user', content: userPrompt }
    ]
  }

  /**
   * Parse email generation response
   */
  private parseEmailResponse(content: string): GeneratedEmail | null {
    try {
      // Extract JSON from response
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('No JSON found in response')
      }

      const result = this.llmProvider.parseJsonResponse(jsonMatch[0])

      if (!result) {
        throw new Error('Failed to parse email generation response')
      }

      return {
        subject1: result.subject1 || '',
        body1: result.body1 || '',
        subject2: result.subject2 || '',
        body2: result.body2 || '',
        subject3: result.subject3 || '',
        body3: result.body3 || ''
      }
    } catch (error) {
      this.log('error', `Error parsing email response: ${error}`)
      return null
    }
  }

  /**
   * Intelligently merge variables, replacing similar ones and adding new ones
   * @param existingVars Existing variables from metadata
   * @param newVars New variables from email generation
   * @returns Merged variables object
   */
  private mergeVariables(existingVars: Record<string, string>, newVars: Record<string, string>): Record<string, string> {
    const merged = { ...existingVars }

    // Define similarity threshold for content comparison
    const similarityThreshold = 0.8

    for (const [newKey, newValue] of Object.entries(newVars)) {
      let shouldReplace = false

      // Always replace if exact key match
      if (existingVars[newKey] !== undefined) {
        shouldReplace = true
        this.log('debug', `Replacing variable '${newKey}': '${existingVars[newKey]?.substring(0, 50)}...' -> '${newValue?.substring(0, 50)}...'`)
      } else {
        // Check for similar keys (e.g., body1 vs body_1, subject vs subject1)
        for (const existingKey of Object.keys(existingVars)) {
          if (this.areKeySimilar(existingKey, newKey)) {
            // If keys are similar, compare content similarity
            if (this.getContentSimilarity(existingVars[existingKey], newValue) < similarityThreshold) {
              // Content is different enough, replace the old one
              delete merged[existingKey]
              shouldReplace = true
              this.log('debug', `Replacing similar variable '${existingKey}' -> '${newKey}': content differs significantly`)
              break
            } else {
              this.log('debug', `Keeping existing variable '${existingKey}': content is similar to new '${newKey}'`)
            }
          }
        }
      }

      // Add or replace the variable
      if (shouldReplace || !existingVars[newKey]) {
        merged[newKey] = newValue
      }
    }

    return merged
  }

  /**
   * Check if two variable keys are similar (e.g., body1 vs body_1, subject vs subject1)
   */
  private areKeySimilar(key1: string, key2: string): boolean {
    // Normalize keys by removing underscores and numbers
    const normalize = (key: string) => key.toLowerCase().replace(/[_\d]/g, '')
    const base1 = normalize(key1)
    const base2 = normalize(key2)

    // Check if base names are the same
    if (base1 === base2) {
      return true
    }

    // Check for common variations
    const variations: Record<string, string[]> = {
      'subject': ['subj', 'title', 'headline'],
      'body': ['content', 'message', 'text'],
      'email': ['mail', 'emailaddress'],
      'firstname': ['first', 'fname'],
      'lastname': ['last', 'lname'],
      'companyname': ['company', 'org', 'organization']
    }

    for (const [base, alts] of Object.entries(variations)) {
      if ((base === base1 && alts.includes(base2)) || (base === base2 && alts.includes(base1))) {
        return true
      }
    }

    return false
  }

  /**
   * Calculate content similarity between two strings (simple Jaccard similarity)
   */
  private getContentSimilarity(str1: string, str2: string): number {
    if (!str1 || !str2) return 0
    if (str1 === str2) return 1

    // Simple word-based similarity
    const words1 = new Set(str1.toLowerCase().split(/\s+/))
    const words2 = new Set(str2.toLowerCase().split(/\s+/))

    const intersection = new Set(Array.from(words1).filter(word => words2.has(word)))
    const union = new Set([...Array.from(words1), ...Array.from(words2)])

    return intersection.size / union.size
  }

  /**
   * Store generated email in messages table with proper structure
   * Prevents duplicate messages and updates metadata with variables
   */
  private async storeGeneratedEmail(contactId: number, email: GeneratedEmail): Promise<void> {
    try {
      // Get contact details for subject line
      const contactSql = `
        SELECT c.first_name, c.last_name, c.email, c.title, co.company_name
        FROM contacts c
        LEFT JOIN companies co ON c.company_id = co.company_id
        WHERE c.contact_id = $1
      `
      const contactResult = await this.query(contactSql, [contactId])

      if (contactResult.length === 0) {
        throw new Error(`Contact not found: ${contactId}`)
      }

      const contact = contactResult[0]
      const subject = `DRIP CAMPAIGN FOR ${contact.first_name} ${contact.last_name}`

      // Check if message already exists for this contact with email generation metadata
      const existingMessageSql = `
        SELECT message_id, metadata, thread_id
        FROM messages 
        WHERE to_email = $1 
        AND subject = $2 
        AND metadata->>'email_generation_version' IS NOT NULL
        ORDER BY created_at DESC
        LIMIT 1
      `

      const existingResult = await this.query(existingMessageSql, [contact.email, subject])

      // Prepare new variables - ensure all values are strings
      const newVariables: Record<string, string> = {
        email: String(contact.email || ''),
        job_title: String(contact.title || ''),
        last_name: String(contact.last_name || ''),
        first_name: String(contact.first_name || ''),
        company_name: String(contact.company_name || ''),
        body1: String(email.body1),
        body2: String(email.body2),
        body3: String(email.body3),
        subject1: String(email.subject1),
        subject2: String(email.subject2),
        subject3: String(email.subject3)
      }

      if (existingResult.length > 0) {
        // Update existing message metadata
        const existingMessage = existingResult[0]
        let existingMetadata: any = {}

        try {
          existingMetadata = typeof existingMessage.metadata === 'string'
            ? JSON.parse(existingMessage.metadata)
            : existingMessage.metadata || {}
        } catch (parseError) {
          this.log('warn', `Error parsing existing metadata for message ${existingMessage.message_id}: ${parseError}`)
          existingMetadata = {}
        }

        // Intelligently merge variables - replace similar ones, add new ones
        const existingVariables: Record<string, string> = existingMetadata.variables || {}
        const mergedVariables = this.mergeVariables(existingVariables, newVariables)

        // Update metadata
        const updatedMetadata = {
          ...existingMetadata,
          variables: mergedVariables,
          last_updated: new Date().toISOString(),
          contact_id: contactId,
          email_generation_version: '1.1',
          update_count: (existingMetadata.update_count || 0) + 1
        }

        // Update existing message
        const updateSql = `
          UPDATE messages 
          SET metadata = $1, updated_at = NOW()
          WHERE message_id = $2
        `

        await this.query(updateSql, [JSON.stringify(updatedMetadata), existingMessage.message_id])

        this.log('info', `Updated existing message ${existingMessage.message_id} for contact ${contactId} - merged ${Object.keys(newVariables).length} variables`)

      } else {
        // Create new message
        let threadId: string

        try {
          threadId = await this.createThread(contactId, contact.email as string, subject)
        } catch (threadError) {
          this.log('warn', `Error creating thread, using fallback: ${threadError}`)
          // Fallback: try to find existing thread or use a default
          threadId = `thread_${contactId}_${Date.now()}`
        }

        // Prepare metadata with all the generated email data
        const metadata = {
          variables: newVariables,
          generated_at: new Date().toISOString(),
          contact_id: contactId,
          email_generation_version: '1.1',
          update_count: 1
        }

        // Store in messages table with thread_id
        const sql = `
          INSERT INTO messages (
            thread_id, from_email, to_email, subject, body, metadata, direction,smartlead_campaign_id, role, created_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW())
        `

        await this.query(sql, [
          threadId,
          process.env.SENDER_EMAIL || '<EMAIL>',
          contact.email,
          subject,
          '', // Empty body as requested
          JSON.stringify(metadata),
          'outbound',
          process.env.SMARTLEAD_CAMPAIGN_ID || '2289345',
          'assistant'
        ])

        this.log('info', `Created new generated email message for contact ${contactId} with ${Object.keys(newVariables).length} variables`)
      }

    } catch (error) {
      this.log('error', `Error storing generated email for contact ${contactId}: ${error}`)
      throw error
    }
  }

  /**
   * Create a thread for the email conversation
   */
  private async createThread(contactId: number, toEmail: string, subject: string): Promise<string> {
    try {
      // Get the workspace ID (using the first available workspace)
      const workspaceResult = await this.query('SELECT workspace_id FROM workspaces LIMIT 1')
      const workspaceId = workspaceResult[0]?.workspace_id

      if (!workspaceId) {
        throw new Error('No workspace found')
      }

      // Create thread
      const threadSql = `
        INSERT INTO threads (workspace_id, subject, to_email, contact_id, status, metadata, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
        RETURNING thread_id
      `

      const threadMetadata = {
        email_generation: true,
        processor_type: 'EmailGenerationProcessor',
        created_by: 'system'
      }

      const threadResult = await this.query(threadSql, [
        workspaceId,
        subject,
        toEmail,
        contactId,
        'active',
        JSON.stringify(threadMetadata)
      ])

      const threadId = threadResult[0].thread_id as string

      this.log('debug', `Created thread ${threadId} for contact ${contactId}`)
      return threadId

    } catch (error) {
      this.log('error', `Error creating thread for contact ${contactId}: ${error}`)
      throw error
    }
  }

  /**
   * Get current email generation status for a contact
   */
  private async getCurrentEmailGenerationStatus(contactId: number): Promise<string | null> {
    try {
      const sql = `
        SELECT email_generation_status
        FROM contacts
        WHERE contact_id = $1
      `
      const result = await this.query(sql, [contactId])

      if (result.length === 0) {
        return null
      }

      return result[0].email_generation_status as string
    } catch (error) {
      this.log('error', `Error getting email generation status for contact ${contactId}: ${error}`)
      return null
    }
  }

  /**
   * Auto-sync generated email to Smartlead campaign
   */
  private async syncToSmartlead(contactId: number, generatedEmail: GeneratedEmail, contact: UnifiedEntityData): Promise<void> {
    try {
      // Get campaign ID from options or use default
      const campaignId = this.options.campaignId || process.env.SMARTLEAD_CAMPAIGN_ID || '2317946'

      this.log('info', `Auto-syncing generated email to Smartlead campaign ${campaignId} for contact ${contactId}`)

      // Prepare the sync data using the first email variant
      const syncData = {
        campaignId: campaignId,
        subject: generatedEmail.subject1,
        body: generatedEmail.body1,
        is_html: true,
        custom_fields: {
          // Contact information
          first_name: contact.first_name || '',
          last_name: contact.last_name || '',
          email: contact.email || '',
          company_name: contact.company_name || '',
          job_title: contact.title || '',

          // All generated email variants
          subject1: generatedEmail.subject1,
          subject2: generatedEmail.subject2,
          subject3: generatedEmail.subject3,
          body1: generatedEmail.body1,
          body2: generatedEmail.body2,
          body3: generatedEmail.body3,

          // Metadata
          email_generation_processor: 'true',
          generated_at: new Date().toISOString(),
          processor_version: '1.1'
        }
      }

      // Call the Smartlead sync API
      const apiUrl = `${process.env.API_BASE_URL || 'http://localhost:3030'}/api/smartlead/contacts/${contactId}/sync`

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(syncData)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: `HTTP ${response.status}` }))
        throw new Error(`Smartlead sync failed: ${errorData.error || response.status}`)
      }

      const result = await response.json()

      this.log('info', `Successfully synced to Smartlead - Lead ID: ${result.lead_id || 'N/A'}, Campaign: ${campaignId}`)

      // Save processing attempt with Smartlead sync info
      await this.saveProcessingAttempt(
        'contact',
        contactId,
        'smartlead_sync',
        { campaign_id: campaignId, lead_id: result.lead_id },
        'smartlead_api',
        0, // No token usage for sync
        true
      )

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error syncing to Smartlead for contact ${contactId}: ${errorMessage}`)

      // Save failed processing attempt
      await this.saveProcessingAttempt(
        'contact',
        contactId,
        'smartlead_sync',
        undefined,
        'smartlead_api',
        0,
        false,
        errorMessage
      )

      // Don't throw the error - email generation should still be considered successful
      // even if Smartlead sync fails
    }
  }

  /**
   * Update contact email generation status
   */
  private async updateEmailGenerationStatus(contactId: number, status: string, error?: string): Promise<void> {
    const sql = `
      UPDATE contacts 
      SET email_generated = $2,
          email_generation_status = $3,
          email_generation_error = $4,
          email_generation_date = NOW(),
          updated_at = NOW() 
      WHERE contact_id = $1
    `

    await this.query(sql, [
      contactId,
      status === 'completed',
      status,
      error || null
    ])
  }
}

