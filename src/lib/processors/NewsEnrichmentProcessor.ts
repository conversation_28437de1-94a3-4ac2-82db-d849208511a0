import { BaseProcessor } from './BaseProcessor'
import { NewsProcessingState, EntityData, ProcessorOptions } from '../../types/processing'
import { createProcessor<PERSON><PERSON><PERSON><PERSON><PERSON>pter, LLMFactory, LLMMessage } from '../llm'
import { NEWS_ENRICHMENT_SYSTEM_PROMPT, NEWS_ENRICHMENT_USER_TEMPLATE_FUNCTION } from '../prompts/news-enrichment'

interface NewsData extends EntityData {
  news_id: number
  url: string
  news_title: string
  news_text: string
  source: string
  raw_html: string
  processing_state: NewsProcessingState
  enriched: boolean
}

interface Deal {
  // Property & Project Details
  propertyType: string[]  // Changed from string to string[] to match database jsonb
  subPropertyType: string[]  // Changed from string to string[] to match database jsonb
  strategies: string[]
  projectName: string[]
  address: string[]
  locationCity: string[]
  locationState: string[]
  locationNeighborhood: string[]
  zipCode: string[]
  squareFootage: number
  unitCount: number
  constructionType: string[]  // Changed from string to string[] to match database jsonb
  projectTimeline: string
  jobCreation: number
  subsidyInfo: string
  
  // Transaction Details
  dealType: string[]
  dealSize: number
  dealStatus: string
  capRate: number
  pricePerSf: number
  loanType: string[]
  equityType: string[]
  
  // Capital & Fund Information
  financingType: string[]
  capitalStackNotes: string[]
  capitalMarketsTopic: string
  interestRateImpact: string[]
  fundLoanType: string[]
  fundEquityType: string[]
  fundName: string[]
  fundType: string[]
  fundSize: number
  fundStrategy: string[]
  fundraisingStatus: string
  capitalRaised: number
  targetIrr: number
  syndicationInfo: any
  
  // Distress & Financial Instruments
  distressFlag: boolean
  loanPerformance: string
  delinquencyRate: number
  defaultNotices: number
  restructuringEvent: string
  recoveryEstimate: number
  cmbsData: any
  ratingAgencyAction: string[]
  
  confidenceScore: number
}

interface ParticipantCompany {
  name: string
  role: string
  confidenceScore: number
}

interface LinkedEntity {
  name: string
  type: string
  role: string
  confidenceScore: number
}

interface MentionedCompany {
  name: string
  context: string
  role: string
  confidenceScore: number
}

interface MentionedPerson {
  name: string
  title: string
  company: string
  context: string
  confidenceScore: number
}

interface QuoteTag {
  source: string
  role: string
  statement: string
  context: string
}

interface ExtractedData {
  newsDate: string
  newsTopic: string
  marketTrendTags: string[]
  summary: string
  entityInformation: {
    buyerName: string[]
    sellerName: string[]
    lenderName: string[]
    brokerName: string[]
    companyName: string[]
    equityPartner: string[]
    developerName: string[]
    tenantName: string[]
    reportAuthor: string[]
    linkedEntities: LinkedEntity[]
  }
  deals: Deal[]
  marketMetrics: {
    marketName: string[]
    submarketName: string[]
    timePeriod: string[]
    vacancyRate: number
    availabilityRate: number
    rentalRate: number
    absorptionRate: number
    transactionVolume: number
    constructionPipeline: number
    deliveries: number
    occupancyChange: number
  }
  marketTrends: {
    rentalTrend: string[]
    capRateAvg: number
    capRateTrend: string[]
    newSupplyTrend: string[]
    demandTrend: string[]
    salesVolumeChange: number
    leasingActivity: string[]
    sentimentSummary: string
    forecastSummary: string
    macroeconomicCommentary: string
    remoteWorkImpact: string
    distressIndicator: boolean
  }
  systemFields: {
    llmTags: string[]
    quotesLlmTags: QuoteTag[]
    sourceConfidence: number
    extractionNotes: string
  }
}

export class NewsEnrichmentProcessor extends BaseProcessor {
  private llmProvider: any
  private mappings: Record<string, string[]> = {}

  constructor(options: ProcessorOptions = {}) {
    super('NewsEnrichment', options)
    
    // Create a logger adapter that uses our log method
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this))

    // Check if LangSmith tracing should be enabled
    const enableTracing = process.env.LANGSMITH_TRACING !== 'false'

    // Create LLM provider using Perplexity sonar model
    this.llmProvider = LLMFactory.createProvider(
      'perplexity',
      loggerAdapter,
      {
        apiKey: process.env.PERPLEXITY_API_KEY,
        enableTracing: enableTracing
      }
    )
  }

  /**
   * Get unprocessed news entries that have been fetched but not enriched
   */
  async getUnprocessedEntities(): Promise<NewsData[]> {
    const conditions = [
      'fetched = true',
      'news_text IS NOT NULL',
      '(bad_url IS NULL OR bad_url = false)'
    ]
    const params: unknown[] = []
    let paramIndex = 1

    // Handle multi-ID processing
    if (this.options.multiIds && this.options.multiIds.length > 0) {
      this.log('info', `Processing multiple news IDs: ${this.options.multiIds.join(', ')}`)
      
      // Create placeholder string for IN clause
      const placeholders = this.options.multiIds.map((_, index) => `$${paramIndex + index}`).join(', ')
      conditions.push(`id IN (${placeholders})`)
      
      // Add all IDs to params
      this.options.multiIds.forEach(id => params.push(id))
      paramIndex += this.options.multiIds.length
      
      // For multi-ID runs, we process all specified IDs regardless of status
      // Remove any status-based conditions for multi-ID runs to allow reprocessing
      // (In this case, we don't have specific status conditions, but this pattern is consistent)
    } else if (this.options.singleId) {
      conditions.push(`id = $${paramIndex}`)
      params.push(this.options.singleId)
      paramIndex++
    }

    const sql = `
      SELECT 
        id,
        url,
        news_source as source,
        news_title,
        news_text,
        raw_html,
        enriched
      FROM news
      WHERE ${conditions.join(' AND ')}
      AND news_source != 'bisnow'
      ORDER BY created_at DESC
      ${this.options.limit ? `LIMIT ${this.options.limit}` : ''}
    `

    const result = await this.query(sql, params)

    return result.map(row => ({
      id: row.id as number,
      news_id: row.id as number,
      url: row.url as string,
      source: row.source as string || '',
      news_title: row.news_title as string || '',
      news_text: row.news_text as string || '',
      raw_html: row.raw_html as string || '',
      processing_state: 'pending' as NewsProcessingState,
      enriched: row.enriched as boolean
    }))
  }

  /**
   * Load mappings from central_mapping table
   */
  private async loadMappings(): Promise<void> {
    try {
      const sql = `
        SELECT type, level_1, value_1, level_2, value_2, level_3, value_3
        FROM central_mapping 
        WHERE is_active = true 
        ORDER BY type, level_1, value_1
      `
      const result = await this.query(sql)
      
      this.mappings = {}
      for (const row of result) {
        const type = row.type as string
        const level1 = row.level_1 as string
        const value1 = row.value_1 as string
        const level2 = row.level_2 as string
        const value2 = row.value_2 as string
        
        // Primary level (type -> value_1)
        if (!this.mappings[type]) {
          this.mappings[type] = []
        }
        
        if (value1 && !this.mappings[type].includes(value1)) {
          this.mappings[type].push(value1)
        }
        
        // Secondary level for subcategories (level_2 -> value_2)
        if (level2 && value2) {
          const subcategoryKey = `${type} - ${level2}`
          if (!this.mappings[subcategoryKey]) {
            this.mappings[subcategoryKey] = []
          }
          
          if (!this.mappings[subcategoryKey].includes(value2)) {
            this.mappings[subcategoryKey].push(value2)
          }
        }
      }
      
      this.log('info', `Loaded ${Object.keys(this.mappings).length} mapping types`)
    } catch (error) {
      this.log('error', `Failed to load mappings: ${error}`)
      this.mappings = {}
    }
  }

  /**
   * Safely serialize data to JSON for database storage
   */
  private safeJsonStringify(data: any): string {
    try {
      if (data === null || data === undefined) {
        return '[]'
      }
      
      // Handle arrays
      if (Array.isArray(data)) {
        // Ensure all array elements are serializable
        const cleanArray = data.map(item => {
          if (typeof item === 'object' && item !== null) {
            // For complex objects, extract only the essential properties
            if (item.name !== undefined) {
              return { name: item.name, type: item.type, role: item.role, confidenceScore: item.confidenceScore }
            }
            return item
          }
          return item
        })
        return JSON.stringify(cleanArray)
      }
      
      // Handle objects
      if (typeof data === 'object') {
        // For complex objects, extract only the essential properties
        if (data.name !== undefined) {
          return JSON.stringify({ name: data.name, type: data.type, role: data.role, confidenceScore: data.confidenceScore })
        }
        return JSON.stringify(data)
      }
      
      // Handle strings - wrap in array for consistency
      if (typeof data === 'string') {
        return JSON.stringify([data])
      }
      
      // Handle other primitives
      return JSON.stringify([String(data)])
    } catch (error) {
      this.log('warn', `Failed to serialize JSON data: ${error}, using empty array. Data type: ${typeof data}`)
      return '[]'
    }
  }

  /**
   * Parse and validate date strings from LLM responses
   * Always returns a string in YYYY-MM-DD format or null if invalid.
   */
  private parseDate(dateStr: string | null | undefined): string | null {
    if (!dateStr) return null
    try {
      // Normalize to YYYY-MM-DD
      let normalizedDate = dateStr.trim()
      // If it's just YYYY-MM, add -01 for the day
      if (/^\d{4}-\d{2}$/.test(normalizedDate)) {
        normalizedDate = `${normalizedDate}-01`
      }
      // If it's just YYYY, add -01-01
      if (/^\d{4}$/.test(normalizedDate)) {
        normalizedDate = `${normalizedDate}-01-01`
      }
      // Try to parse the date
      const date = new Date(normalizedDate)
      // Check if it's a valid date
      if (isNaN(date.getTime())) {
        this.log('warn', `Invalid date format: ${dateStr}, using null`)
        return null
      }
      // Return in YYYY-MM-DD format (UTC)
      return date.toISOString().split('T')[0]
    } catch (error) {
      this.log('warn', `Error parsing date "${dateStr}": ${error}, using null`)
      return null
    }
  }

  /**
   * Process a single news entity by enriching it with extracted data
   */
  async processEntity(entity: NewsData): Promise<{ success: boolean; error?: string }> {
    try {
      this.log('info', `Processing news enrichment for ID ${entity.news_id}: ${entity.url}`)
      
      // Mark as running
      await this.setNewsEnrichmentStatus(entity.news_id, 'running')
      
      // Load mappings if not already loaded
      if (Object.keys(this.mappings).length === 0) {
        await this.loadMappings()
      }

      // Extract text from HTML if needed
      if (!entity.news_text) {
        throw new Error('No content available for processing')
      }

      // Generate the prompt using the already extracted title and text
      const prompt = NEWS_ENRICHMENT_USER_TEMPLATE_FUNCTION({
        title: entity.news_title || 'No title extracted',
        text: entity.news_text || 'No content available',
        source: entity.source,
        url: entity.url
      }, this.mappings)

      // Call LLM for extraction
      const messages: LLMMessage[] = [
        { role: 'system', content: NEWS_ENRICHMENT_SYSTEM_PROMPT },
        { role: 'user', content: prompt }
      ]

      // Use Perplexity with sonar model for web-enhanced research
      const response = await this.llmProvider.callLLM(messages, {
        temperature: 0.1,
        maxTokens: 8000,
        model: 'sonar'
      })

      // Parse the response
      const extractedData = await this.parseExtractionResponse(response.content)
      // console.log('extractedData', extractedData)
      // Store the extracted data in the new news_enrichment table
      await this.storeNewsEnrichmentData(entity.news_id, extractedData, entity)
      
      // Update metadata in news table
      await this.updateNewsMetadata(entity.news_id, extractedData)

      this.log('info', `Successfully processed news enrichment for ID ${entity.news_id}`)
      
      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error processing news enrichment ${entity.news_id}: ${errorMessage}`)
      
      // Store error in database
      try {
        await this.storeEnrichmentError(entity.news_id, errorMessage)
      } catch (dbError) {
        this.log('error', `Failed to store error for news ${entity.news_id}: ${dbError}`)
      }
      
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Parse the LLM response and validate JSON
   */
  private async parseExtractionResponse(response: string): Promise<ExtractedData> {
    try {
      // Clean the response - remove any markdown or extra text
      const cleanResponse = response
        .replace(/```json\s*/g, '')
        .replace(/```\s*/g, '')
        .trim()
      
      // Parse JSON using the provider's parsing method
      const data = this.llmProvider.parseJsonResponse(cleanResponse)
      
      if (!data) {
        throw new Error('Failed to parse LLM response as JSON')
      }
      
      // Validate required fields
      if (!data.entityInformation) {
        throw new Error('Missing required fields in extraction response')
      }
      
      // Ensure all array fields are properly initialized
      const validatedData = {
        ...data,
        deals: Array.isArray(data.deals) ? data.deals : [],
        entityInformation: {
          buyerName: Array.isArray(data.entityInformation?.buyerName) ? data.entityInformation.buyerName : [],
          sellerName: Array.isArray(data.entityInformation?.sellerName) ? data.entityInformation.sellerName : [],
          lenderName: Array.isArray(data.entityInformation?.lenderName) ? data.entityInformation.lenderName : [],
          brokerName: Array.isArray(data.entityInformation?.brokerName) ? data.entityInformation.brokerName : [],
          companyName: Array.isArray(data.entityInformation?.companyName) ? data.entityInformation.companyName : [],
          equityPartner: Array.isArray(data.entityInformation?.equityPartner) ? data.entityInformation.equityPartner : [],
          developerName: Array.isArray(data.entityInformation?.developerName) ? data.entityInformation.developerName : [],
          tenantName: Array.isArray(data.entityInformation?.tenantName) ? data.entityInformation.tenantName : [],
          reportAuthor: Array.isArray(data.entityInformation?.reportAuthor) ? data.entityInformation.reportAuthor : [],
          linkedEntities: Array.isArray(data.entityInformation?.linkedEntities) ? data.entityInformation.linkedEntities : []
        },
        marketTrendTags: Array.isArray(data.marketTrendTags) ? data.marketTrendTags : [],
        marketMetrics: {
          marketName: Array.isArray(data.marketMetrics?.marketName) ? data.marketMetrics.marketName : [],
          submarketName: Array.isArray(data.marketMetrics?.submarketName) ? data.marketMetrics.submarketName : [],
          timePeriod: Array.isArray(data.marketMetrics?.timePeriod) ? data.marketMetrics.timePeriod : [],
          vacancyRate: data.marketMetrics?.vacancyRate || null,
          availabilityRate: data.marketMetrics?.availabilityRate || null,
          rentalRate: data.marketMetrics?.rentalRate || null,
          absorptionRate: data.marketMetrics?.absorptionRate || null,
          transactionVolume: data.marketMetrics?.transactionVolume || null,
          constructionPipeline: data.marketMetrics?.constructionPipeline || null,
          deliveries: data.marketMetrics?.deliveries || null,
          occupancyChange: data.marketMetrics?.occupancyChange || null
        },
        marketTrends: {
          rentalTrend: Array.isArray(data.marketTrends?.rentalTrend) ? data.marketTrends.rentalTrend : [],
          capRateAvg: data.marketTrends?.capRateAvg || null,
          capRateTrend: Array.isArray(data.marketTrends?.capRateTrend) ? data.marketTrends.capRateTrend : [],
          newSupplyTrend: Array.isArray(data.marketTrends?.newSupplyTrend) ? data.marketTrends.newSupplyTrend : [],
          demandTrend: Array.isArray(data.marketTrends?.demandTrend) ? data.marketTrends.demandTrend : [],
          salesVolumeChange: data.marketTrends?.salesVolumeChange || null,
          leasingActivity: Array.isArray(data.marketTrends?.leasingActivity) ? data.marketTrends.leasingActivity : [],
          sentimentSummary: data.marketTrends?.sentimentSummary || null,
          forecastSummary: data.marketTrends?.forecastSummary || null,
          macroeconomicCommentary: data.marketTrends?.macroeconomicCommentary || null,
          remoteWorkImpact: data.marketTrends?.remoteWorkImpact || null,
          distressIndicator: data.marketTrends?.distressIndicator || false
        },
        systemFields: {
          llmTags: Array.isArray(data.systemFields?.llmTags) ? data.systemFields.llmTags : [],
          quotesLlmTags: Array.isArray(data.systemFields?.quotesLlmTags) ? data.systemFields.quotesLlmTags : [],
          sourceConfidence: data.systemFields?.sourceConfidence || 0,
          extractionNotes: data.systemFields?.extractionNotes || ''
        }
      }
      
      return validatedData as ExtractedData
    } catch (error) {
      this.log('error', `Failed to parse extraction response: ${error}`)
      this.log('debug', `Response content: ${response}`)
      throw new Error(`JSON parsing failed: ${error}`)
    }
  }

  /**
   * Store comprehensive news enrichment data in the news_enrichment table
   * Create multiple records if multiple deals exist
   */
  private async storeNewsEnrichmentData(newsId: number, data: ExtractedData, entity: NewsData): Promise<void> {
    try {
      // Get basic article information
      const source = entity.source || 'unknown'
      const headline = entity.news_title || ''
      const bodyText = entity.news_text || ''
      const sourceUrl = entity.url || ''
      
      // If no deals, create one record for the article
      if (!data.deals || data.deals.length === 0) {
        await this.createNewsEnrichmentRecord(newsId, data, entity, null, false)
        this.log('info', `Created 1 news enrichment record for article ${newsId} (no deals)`)
        return
      }

      // Create one record per deal
      for (let i = 0; i < data.deals.length; i++) {
        const deal = data.deals[i]
        await this.createNewsEnrichmentRecord(newsId, data, entity, deal, true)
      }

      this.log('info', `Created ${data.deals.length} news enrichment records for article ${newsId}`)
    } catch (error) {
      this.log('error', `Error storing news enrichment data for ${newsId}: ${error}`)
      throw error
    }
  }

  /**
   * Create a single news enrichment record
   */
  private async createNewsEnrichmentRecord(
    newsId: number, 
    data: ExtractedData, 
    entity: NewsData, 
    deal: Deal | null, 
    isDealSpecific: boolean
  ): Promise<void> {
    const sql = `
      INSERT INTO news_enrichment (
        news_id, source_name, source_url, data_source_link, publication_date, scrape_date, 
        last_updated, scraping_source_type, headline, summary, body_text, news_topic, 
        market_trend_tags, buyer_name, seller_name, lender_name, broker_name,
        company_name, equity_partner, developer_name, tenant_name, report_author,
        linked_entities, property_type, sub_property_type, strategies, project_name,
        address, location_city, location_state, location_neighborhood, zip_code,
        square_footage, unit_count, construction_type, project_timeline, job_creation,
        subsidy_info, deal_type, deal_size, deal_status, cap_rate, price_per_sf,
        loan_type, equity_type, financing_type, capital_stack_notes, capital_markets_topic,
        interest_rate_impact, fund_loan_type, fund_equity_type, fund_name, fund_type,
        fund_size, fund_strategy, fundraising_status, capital_raised, target_irr,
        syndication_info, distress_flag, loan_performance, delinquency_rate,
        default_notices, restructuring_event, recovery_estimate, cmbs_data,
        rating_agency_action, market_name, submarket_name, time_period, vacancy_rate,
        availability_rate, rental_rate, absorption_rate, transaction_volume,
        construction_pipeline, deliveries, occupancy_change, rental_trend, cap_rate_avg,
        cap_rate_trend, new_supply_trend, demand_trend, sales_volume_change,
        leasing_activity, sentiment_summary, forecast_summary, macroeconomic_commentary,
        remote_work_impact, distress_indicator, llm_tags, quotes_llm_tags,
        source_confidence, extraction_notes, deal_record_id, is_deal_specific
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20,
        $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32, $33, $34, $35, $36, $37, $38,
        $39, $40, $41, $42, $43, $44, $45, $46, $47, $48, $49, $50, $51, $52, $53, $54, $55, $56,
        $57, $58, $59, $60, $61, $62, $63, $64, $65, $66, $67, $68, $69, $70, $71, $72, $73, $74,
        $75, $76, $77, $78, $79, $80, $81, $82, $83, $84, $85, $86, $87, $88, $89, $90, $91, $92,
        $93, $94, $95, $96
      )
    `

    const params = [
      newsId, // $1
      entity.source || 'unknown', // $2
      entity.url || '', // $3
      entity.url || '', // $4 - data_source_link (using URL as source)
      this.parseDate(data.newsDate), // $5
      new Date(), // $6 - scrape_date
      new Date(), // $7 - last_updated
      'HTML Parse', // $8 - scraping_source_type
      entity.news_title || '', // $9
      data.summary || '', // $10
      entity.news_text || '', // $11
      this.safeJsonStringify(data.newsTopic), // $12 - Store as JSON string
      this.safeJsonStringify(data.marketTrendTags), // $13
      this.safeJsonStringify(data.entityInformation?.buyerName), // $14
      this.safeJsonStringify(data.entityInformation?.sellerName), // $15
      this.safeJsonStringify(data.entityInformation?.lenderName), // $16
      this.safeJsonStringify(data.entityInformation?.brokerName), // $17
      this.safeJsonStringify(data.entityInformation?.companyName), // $18
      this.safeJsonStringify(data.entityInformation?.equityPartner), // $19
      this.safeJsonStringify(data.entityInformation?.developerName), // $20
      this.safeJsonStringify(data.entityInformation?.tenantName), // $21
      this.safeJsonStringify(data.entityInformation?.reportAuthor), // $22
      this.safeJsonStringify(data.entityInformation?.linkedEntities), // $23
      this.safeJsonStringify(deal?.propertyType), // $24
      this.safeJsonStringify(deal?.subPropertyType), // $25
      this.safeJsonStringify(deal?.strategies), // $26
      this.safeJsonStringify(deal?.projectName), // $27
      this.safeJsonStringify(deal?.address), // $28
      this.safeJsonStringify(deal?.locationCity), // $29
      this.safeJsonStringify(deal?.locationState), // $30
      this.safeJsonStringify(deal?.locationNeighborhood), // $31
      this.safeJsonStringify(deal?.zipCode), // $32
      deal?.squareFootage || null, // $33
      deal?.unitCount || null, // $34
      this.safeJsonStringify(deal?.constructionType), // $35
      deal?.projectTimeline || null, // $36
      deal?.jobCreation || null, // $37
      deal?.subsidyInfo || null, // $38
      this.safeJsonStringify(deal?.dealType), // $39
      (deal?.dealSize && deal.dealSize > 0) ? deal.dealSize : null, // $40 - Only store valid positive deal sizes
      deal?.dealStatus || null, // $41
      deal?.capRate || null, // $42
      deal?.pricePerSf || null, // $43
      this.safeJsonStringify(deal?.loanType), // $44
      this.safeJsonStringify(deal?.equityType), // $45
      this.safeJsonStringify(deal?.financingType), // $46
      this.safeJsonStringify(deal?.capitalStackNotes), // $47
      deal?.capitalMarketsTopic || null, // $48
      this.safeJsonStringify(deal?.interestRateImpact), // $49
      this.safeJsonStringify(deal?.fundLoanType), // $50
      this.safeJsonStringify(deal?.fundEquityType), // $51
      this.safeJsonStringify(deal?.fundName), // $52
      this.safeJsonStringify(deal?.fundType), // $53
      deal?.fundSize || null, // $54
      this.safeJsonStringify(deal?.fundStrategy), // $55
      deal?.fundraisingStatus || null, // $56
      deal?.capitalRaised || null, // $57
      deal?.targetIrr || null, // $58
      this.safeJsonStringify(deal?.syndicationInfo), // $59
      deal?.distressFlag || null, // $60
      deal?.loanPerformance || null, // $61
      deal?.delinquencyRate || null, // $62
      deal?.defaultNotices || null, // $63
      deal?.restructuringEvent || null, // $64
      deal?.recoveryEstimate || null, // $65
      this.safeJsonStringify(deal?.cmbsData), // $66
      this.safeJsonStringify(deal?.ratingAgencyAction), // $67
      this.safeJsonStringify(data.marketMetrics?.marketName), // $68
      this.safeJsonStringify(data.marketMetrics?.submarketName), // $69
      this.safeJsonStringify(data.marketMetrics?.timePeriod), // $70
      data.marketMetrics?.vacancyRate || null, // $71
      data.marketMetrics?.availabilityRate || null, // $72
      data.marketMetrics?.rentalRate || null, // $73
      data.marketMetrics?.absorptionRate ? BigInt(Math.round(data.marketMetrics.absorptionRate)) : null, // $74 - Convert to bigint
      data.marketMetrics?.transactionVolume || null, // $75
      data.marketMetrics?.constructionPipeline ? BigInt(Math.round(data.marketMetrics.constructionPipeline)) : null, // $76 - Convert to bigint
      data.marketMetrics?.deliveries ? BigInt(Math.round(data.marketMetrics.deliveries)) : null, // $77 - Convert to bigint
      data.marketMetrics?.occupancyChange || null, // $78
      this.safeJsonStringify(data.marketTrends?.rentalTrend), // $79
      data.marketTrends?.capRateAvg || null, // $80
      this.safeJsonStringify(data.marketTrends?.capRateTrend), // $81
      this.safeJsonStringify(data.marketTrends?.newSupplyTrend), // $82
      this.safeJsonStringify(data.marketTrends?.demandTrend), // $83
      data.marketTrends?.salesVolumeChange || null, // $84
      this.safeJsonStringify(data.marketTrends?.leasingActivity), // $85
      data.marketTrends?.sentimentSummary || null, // $86
      data.marketTrends?.forecastSummary || null, // $87
      data.marketTrends?.macroeconomicCommentary || null, // $88
      data.marketTrends?.remoteWorkImpact || null, // $89
      data.marketTrends?.distressIndicator || null, // $90
      this.safeJsonStringify(data.systemFields?.llmTags), // $91
      this.safeJsonStringify(data.systemFields?.quotesLlmTags), // $92
      data.systemFields?.sourceConfidence || null, // $93
      data.systemFields?.extractionNotes || null, // $94
      null, // $95 - deal_record_id (not used yet)
      isDealSpecific // $96
    ]

    try {
      await this.query(sql, params)
    } catch (error) {
      this.log('error', `Failed to insert news enrichment record for news ${newsId}: ${error}`)
      this.log('debug', `SQL: ${sql}, Params: ${JSON.stringify(params)}`)
      throw new Error(`Failed to insert news enrichment record: ${error}`)
    }
  }

  /**
   * Update news table with summary metadata
   */
  private async updateNewsMetadata(newsId: number, data: ExtractedData): Promise<void> {
    const sql = `
      UPDATE news 
      SET 
        enriched = true,
        enrichment_status = 'completed',
        contains_deals = $1,
        deal_count = $2,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
    `

    const params = [
      (data.deals?.length || 0) > 0, // $1 - contains_deals
      data.deals?.length || 0, // $2 - deal_count
      newsId // $3
    ]

    await this.query(sql, params)
  }

  /**
   * Store enrichment error
   */
  private async storeEnrichmentError(newsId: number, error: string): Promise<void> {
    const sql = `
      UPDATE news
      SET enrichment_error = $1,
          enrichment_status = $2
      WHERE id = $3
    `
    
    await this.query(sql, [error, 'failed', newsId])
  }

  /**
   * Update news enrichment status
   */
  private async setNewsEnrichmentStatus(newsId: number, status: NewsProcessingState, error?: string): Promise<void> {
    // Convert status to string to ensure PostgreSQL compatibility
    const statusStr = String(status)
    
    if (error) {
      const sql = `
        UPDATE news
        SET 
          enrichment_status = $1,
          enrichment_error = $2,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $3
      `
      
      await this.query(sql, [statusStr, error, newsId])
    } else {
      const sql = `
        UPDATE news
        SET 
          enrichment_status = $1,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `
      
      await this.query(sql, [statusStr, newsId])
    }
  }

  /**
   * Update entity status after processing
   */
  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    try {
      if (success) {
        await this.setNewsEnrichmentStatus(entityId, 'completed')
        this.log('info', `News ${entityId} enrichment completed successfully`)
      } else {
        await this.setNewsEnrichmentStatus(entityId, 'failed', error)
        this.log('error', `News ${entityId} enrichment failed: ${error}`)
      }
    } catch (updateError) {
      this.log('error', `Error updating status for news ${entityId}: ${updateError}`)
    }
  }
} 