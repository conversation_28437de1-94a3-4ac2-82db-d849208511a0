import { BaseProcessor } from './BaseProcessor';
import { generateDealRequirementPrompt, generateDealRequirementPromptWithText, generateDealRequirementPromptWithMappings } from '../prompts/deal-requirement';
import { pool } from '../db';
import {
  LLMFactory,
  createProcessorLoggerAdapter,
  LLMMessage,
  LLMResponse,
} from "../llm";
import {
  AbstractTextExtractor,
  PDFTextExtractor,
  CSVTextExtractor,
  XLSXTextExtractor,
  WordTextExtractor,
} from "../utils/textExtractor";
import * as XLSX from "xlsx";
import * as fs from "fs";
import * as path from "path";

export class DealRequirementProcessor extends BaseProcessor {
  private entityId: number;
  private entityType: string;
  private llmProvider: any;

  constructor(entityId: number, entityType: string = 'Deal') {
    super("DealRequirementProcessor");
    this.entityId = entityId;
    this.entityType = entityType;
    
    // Initialize LLM provider (using same approach as DealProcessor)
    const loggerAdapter = createProcessorLoggerAdapter(this.log.bind(this));
    
    // Use the same LLM provider setup as DealProcessor
    const selectedModel = "gemini-flash"; // Default model
    
    // Map the model name to the actual Gemini model
    const modelName = "gemini-2.5-flash"; // Use flash model for requirement extraction
    
    console.log("🔍 DealRequirementProcessor Constructor - Model Selection Debug:");
    console.log("  - Selected model:", selectedModel);
    console.log("  - Using Gemini provider with model:", modelName);

    // Check if LangSmith tracing should be enabled
    const enableTracing = process.env.LANGSMITH_TRACING !== 'false'

    this.llmProvider = LLMFactory.createProvider(
      "gemini",
      loggerAdapter,
      process.env.GEMINI_API_KEY
        ? {
            apiKey: process.env.GEMINI_API_KEY,
            enableTracing: enableTracing,
            defaultOptions: {
              temperature: 0.1,
              maxTokens: 8000,
              model: modelName,
            },
          }
        : { enableTracing: enableTracing }
    );
  }

  // Required BaseProcessor methods
  async getUnprocessedEntities(): Promise<any[]> {
    return []; // Not used for this processor
  }

  async processEntity(entity: any): Promise<{ success: boolean; error?: string }> {
    return { success: false, error: "Not implemented" }; // Not used for this processor
  }

  // Helper method to call LLM
  private async callLLM(prompt: string): Promise<string | null> {
    try {
      const messages: LLMMessage[] = [
        { role: "system", content: prompt }
      ];
      
      const response: LLMResponse = await this.llmProvider.callLLM(messages);
      return response.content;
    } catch (error) {
      console.error('Error calling LLM:', error);
      return null;
    }
  }

  // Static method to get extractor (copied from DealProcessor)
  static getExtractor(mimeType: string | undefined): AbstractTextExtractor | null {
    if (!mimeType) return null;
    
    switch (mimeType) {
      case "application/pdf":
        return new PDFTextExtractor();
      case "text/csv":
        return new CSVTextExtractor();
      case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
      case "application/vnd.ms-excel":
        return new XLSXTextExtractor();
      case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
      case "application/msword":
        return new WordTextExtractor();
      default:
        return null;
    }
  }

  /**
   * Fetch standardized mappings from central_mapping table (copied from DealProcessor)
   */
  private async getMappings(): Promise<{ [key: string]: string[] }> {
    try {
      const sql = `
        SELECT type, level_1, value_1, level_2, value_2 
        FROM central_mapping 
        WHERE is_active = true 
        ORDER BY type, level_1, value_1
      `;
      const results = await pool.query(sql);
      const mappings: { [key: string]: string[] } = {};
      for (const row of results.rows) {
        const type = row.type as string;
        const level1 = row.level_1 as string;
        const value1 = row.value_1 as string;
        const level2 = row.level_2 as string;
        const value2 = row.value_2 as string;
        if (!mappings[type]) {
          mappings[type] = [];
        }
        if (value1 && !mappings[type].includes(value1)) {
          mappings[type].push(value1);
        }
        if (level2 && value2) {
          const subcategoryKey = `${type} - ${level2}`;
          if (!mappings[subcategoryKey]) {
            mappings[subcategoryKey] = [];
          }
          if (!mappings[subcategoryKey].includes(value2)) {
            mappings[subcategoryKey].push(value2);
          }
        }
      }
      console.log(`Loaded ${Object.keys(mappings).length} mapping types from central_mapping table`);
      return mappings;
    } catch (error) {
      console.error(`Error fetching mappings: ${error}`);
      return {};
    }
  }

  private async savePromptLog(
    messages: any[],
    provider: string,
    fileNames: string[],
    additionalContext?: Record<string, any>
  ): Promise<string> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
      const sanitizedFileNames = fileNames.map(name => name.replace(/[^a-zA-Z0-9.-]/g, "_")).join("_");
      const promptFileName = `prompt-${provider}-${sanitizedFileNames}-${timestamp}.json`;
      const promptDir = path.join(process.cwd(), "prompt-logs");
      
      // Ensure prompt logs directory exists
      if (!fs.existsSync(promptDir)) {
        fs.mkdirSync(promptDir, { recursive: true });
      }
      
      const promptFilePath = path.join(promptDir, promptFileName);
      
      // Clean up messages for logging (remove binary data but keep structure)
      const cleanedMessages = messages.map(msg => {
        if (provider === "gemini") {
          return {
            role: msg.role,
            parts: msg.parts?.map((part: any) => {
              if (part.fileBuffer) {
                return {
                  type: "file",
                  fileName: part.fileName,
                  mimeType: part.mimeType,
                  fileSize: part.fileBuffer.length,
                  fileBuffer: "[BINARY DATA REMOVED]"
                };
              }
              return part;
            })
          };
        } else {
          // OpenAI format
          return {
            role: msg.role,
            content: Array.isArray(msg.content) 
              ? msg.content.map((content: any) => {
                  if (content.type === "image_url" || content.type === "file") {
                    return {
                      type: content.type,
                      [content.type === "image_url" ? "image_url" : "file"]: {
                        ...content[content.type === "image_url" ? "image_url" : "file"],
                        [content.type === "image_url" ? "url" : "file_data"]: "[BASE64 DATA REMOVED]"
                      }
                    };
                  }
                  return content;
                })
              : msg.content
          };
        }
      });
      
      const promptData = {
        timestamp: new Date().toISOString(),
        provider: provider,
        model: "gemini-2.5-flash",
        fileNames: fileNames,
        fileCount: fileNames.length,
        messages: cleanedMessages,
        additionalContext: additionalContext || {},
        totalMessageCount: messages.length
      };
      
      fs.writeFileSync(promptFilePath, JSON.stringify(promptData, null, 2));
      console.log(`Prompt details saved to: ${promptFilePath}`);
      
      return promptFilePath;
    } catch (err) {
      console.error(`Failed to create prompt log file: ${err}`);
      return `Failed to create prompt log file: ${err}`;
    }
  }

  async processFiles(files: Array<{
    buffer: Buffer;
    mimeType: string;
    fileName: string;
  }>): Promise<any> {
    try {
      console.log(`Processing ${files.length} files for deal requirements extraction...`);

      // Process files similar to DealProcessor
      const uploadFiles = await Promise.all(files.map(async (file) => {
        const isExcel =
          file.mimeType === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
          file.mimeType === "application/vnd.ms-excel" ||
          file.fileName.toLowerCase().endsWith(".xlsx") ||
          file.fileName.toLowerCase().endsWith(".xls");
        const isWord =
          file.mimeType === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
          file.mimeType === "application/msword" ||
          file.fileName.toLowerCase().endsWith(".docx") ||
          file.fileName.toLowerCase().endsWith(".doc");
        
        if (isExcel) {
          try {
            const workbook = XLSX.read(file.buffer, { type: "buffer" });
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];
            const csvContent = XLSX.utils.sheet_to_csv(worksheet);
            return {
              buffer: Buffer.from(csvContent, "utf-8"),
              mimeType: "text/csv",
              fileName: file.fileName.replace(/\.(xlsx|xls)$/i, ".csv"),
            };
          } catch (err) {
            console.warn(`Failed to convert Excel to CSV for ${file.fileName}: ${err}. Uploading original file.`);
            return file;
          }
        } else if (isWord) {
          try {
            const extractor = DealRequirementProcessor.getExtractor(file.mimeType);
            if (extractor) {
              const textContent = await extractor.extractText(file.buffer);
              return {
                buffer: Buffer.from(textContent, "utf-8"),
                mimeType: "text/plain",
                fileName: file.fileName.replace(/\.(docx|doc)$/i, ".txt"),
              };
            } else {
              console.warn(`No extractor available for Word document ${file.fileName}. Uploading original file.`);
              return file;
            }
          } catch (err) {
            console.warn(`Failed to convert Word document to text for ${file.fileName}: ${err}. Uploading original file.`);
            return file;
          }
        }
        return file;
      }));



      // Filter out LLM output files to prevent sending them to LLM
      const filteredFiles = uploadFiles.filter(file => {
        const isLLMOutput = file.fileName.includes('llm_output') || 
                           file.fileName.includes('LLM Output') ||
                           file.fileName.includes('requirement_extraction') ||
                           file.mimeType === 'application/json';
        
        if (isLLMOutput) {
          console.log(`Skipping LLM output file: ${file.fileName}`);
          return false;
        }
        return true;
      });

      console.log(`Processing ${filteredFiles.length} files for requirement extraction (${uploadFiles.length - filteredFiles.length} LLM output files filtered out)...`);
      
      // Log file information
      filteredFiles.forEach((file, index) => {
        console.log(`File ${index + 1}: ${file.fileName} (${file.mimeType}) - Size: ${file.buffer.length} bytes`);
      });

      // Get mappings from central_mapping table (like DealProcessor)
      const mappings = await this.getMappings();
      
      // Generate the requirement extraction prompt with mappings
      const systemPrompt = generateDealRequirementPromptWithMappings(mappings);

      // Prepare files for Gemini multimodal input (same as DealProcessor)
      const geminiMessages: any[] = [
        {
          role: "system",
          parts: [systemPrompt],
        },
        {
          role: "user",
          parts: filteredFiles.map((file) => ({
            fileBuffer: file.buffer,
            mimeType: file.mimeType,
            fileName: file.fileName,
          })),
        },
      ];

      console.log(`Calling Gemini with ${filteredFiles.length} files for requirement extraction...`);
      
      // Save prompt log for debugging (same as DealProcessor)
      await this.savePromptLog(
        geminiMessages,
        "gemini",
        filteredFiles.map(f => f.fileName),
        {
          totalFiles: filteredFiles.length,
          fileTypes: filteredFiles.map(f => f.mimeType)
        }
      );
      
      // Call LLM with native file support (same as DealProcessor)
      let response: LLMResponse;
      try {
        response = await (this.llmProvider as any).callLLMWithFiles(
          geminiMessages,
          undefined,
          false // Use flash model for requirement extraction (same as DealProcessor)
        );
      } catch (error: any) {
        console.error("🔍 Gemini API Error Details:");
        console.error("  - Error type:", error?.constructor?.name || 'Unknown');
        console.error("  - Error message:", error?.message || 'No message');
        console.error("  - Error stack:", error?.stack || 'No stack');
        if (error?.response) {
          console.error("  - Response status:", error.response.status);
          console.error("  - Response data:", error.response.data);
        }
        throw error;
      }

      const extractionResult = response.content;
      
      if (!extractionResult) {
        throw new Error('No extraction result from LLM');
      }

      // Log the raw response from Gemini
      console.log('🔍 Raw Gemini Response:');
      console.log('Response length:', extractionResult.length);
      console.log('Response preview (first 500 chars):', extractionResult.substring(0, 500));
      console.log('Response preview (last 500 chars):', extractionResult.substring(Math.max(0, extractionResult.length - 500)));
      console.log('Full response:', extractionResult);

      // Clean the response to remove markdown code blocks
      let cleanedResponse = extractionResult.trim();
      
      // Remove markdown code blocks if present
      if (cleanedResponse.startsWith('```json')) {
        cleanedResponse = cleanedResponse.replace(/^```json\s*/, '');
      } else if (cleanedResponse.startsWith('```')) {
        cleanedResponse = cleanedResponse.replace(/^```\s*/, '');
      }
      
      if (cleanedResponse.endsWith('```')) {
        cleanedResponse = cleanedResponse.replace(/\s*```$/, '');
      }
      
      console.log('🔍 Cleaned Response:');
      console.log('Cleaned response:', cleanedResponse);

      // Parse the JSON response
      let parsedResult;
      try {
        parsedResult = JSON.parse(cleanedResponse);
      } catch (error) {
        console.error('Failed to parse LLM response as JSON. Error:', error);
        console.error('Raw response that failed to parse:', extractionResult);
        console.error('Cleaned response that failed to parse:', cleanedResponse);
        throw new Error('Invalid JSON response from LLM');
      }

      // Process the capital requests and update criteria
      await this.processCapitalRequests(parsedResult);

      // Save LLM output as a file in the deal files
      await this.saveLLMOutputAsFile(extractionResult, filteredFiles.map(f => f.fileName));

      return {
        success: true,
        capital_requests: parsedResult.capital_requests || [],
        message: 'Capital requirements extracted and criteria updated successfully'
      };

    } catch (error) {
      console.error('Error in DealRequirementProcessor:', error);
      throw error;
    }
  }

  private async processCapitalRequests(result: any): Promise<void> {
    const capitalRequests = result.capital_requests || [];
    
    if (capitalRequests.length === 0) {
      console.log('No capital requests found in extraction result');
      return;
    }

    // Get existing criteria for this entity
    const existingCriteriaQuery = `
      SELECT criteria_id, capital_position, extra_fields
      FROM investment_criteria 
      WHERE entity_type = $1 AND entity_id = $2 AND is_active = true
    `;
    
    const existingCriteriaResult = await pool.query(existingCriteriaQuery, [
      this.entityType, 
      this.entityId.toString()
    ]);

    const existingCriteria = existingCriteriaResult.rows;

    // Process each capital request
    for (const request of capitalRequests) {
      const { capital_position, amount, loan_purpose, notes } = request;
      
      // Find matching criteria
      const matchingCriteria = existingCriteria.filter(criteria => {
        // Match by capital position if it's an array
        if (criteria.capital_position && Array.isArray(criteria.capital_position)) {
          return criteria.capital_position.includes(capital_position);
        }
        
        // Match by capital position if it's a string
        if (criteria.capital_position === capital_position) {
          return true;
        }
        
        return false;
      });

                      // Update matching criteria with is_requested = true
        for (const criteria of matchingCriteria) {
          const updateQuery = `
            UPDATE investment_criteria 
            SET 
              is_requested = true,
              updated_at = NOW()
            WHERE criteria_id = $1
          `;

          await pool.query(updateQuery, [criteria.criteria_id]);

          console.log(`Updated criteria ${criteria.criteria_id} with is_requested = true`);
        }

        // If no matching criteria found, log it but don't create new criteria
        if (matchingCriteria.length === 0) {
          console.log(`No matching criteria found for capital position: ${capital_position}. Skipping creation.`);
        }
    }
  }

  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    try {
      // Update the deal's extraction status
      await pool.query(`
        UPDATE deals 
        SET 
          extraction_confidence = $1,
          processing_notes = $2,
          updated_at = NOW()
        WHERE deal_id = $3
      `, [
        success ? 'high' : 'low',
        success ? 'Requirement extraction completed successfully' : `Requirement extraction failed: ${error || 'Unknown error'}`,
        entityId
      ]);
    } catch (error) {
      console.error('Error updating entity status:', error);
    }
  }

  private async saveLLMOutputAsFile(llmOutput: string, processedFileNames: string[]): Promise<void> {
    try {
      const { FileManager } = await import('../utils/fileManager');
      
      // Create a timestamp for the file name
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fileName = `requirement_extraction_${timestamp}.json`;
      
      // Create the file content with metadata
      const fileContent = {
        extraction_timestamp: new Date().toISOString(),
        deal_id: this.entityId,
        processor: 'DealRequirementProcessor',
        processed_files: processedFileNames,
        llm_output: llmOutput,
        extraction_result: JSON.parse(llmOutput) // Parse the JSON for easier reading
      };
      
      // Convert to JSON string
      const fileBuffer = Buffer.from(JSON.stringify(fileContent, null, 2), 'utf-8');
      
      // Upload the file using FileManager
      const uploadRequest = {
        original_name: fileName,
        title: `Requirement Extraction Output - ${new Date().toLocaleDateString()}`,
        description: `LLM output from requirement extraction for deal ${this.entityId}`,
        uploaded_by: 'system',
        upload_source: 'requirement_extraction',
        is_public: false,
        access_level: 'private' as const,
        tags: ['requirement_extraction', 'llm_output', 'deal_processing'],
        metadata: {
          processor: 'DealRequirementProcessor',
          deal_id: this.entityId,
          extraction_type: 'requirement_extraction'
        }
      };
      
      const options = {
        validate_table_column: false,
        allow_duplicates: true,
        auto_generate_title: false,
        preserve_original_name: true
      };
      
      // Upload the file
      const { file: uploadedFile } = await FileManager.uploadFile(fileBuffer, uploadRequest, options);
      
      // Create relationship to the deal
      const relationshipRequest = {
        target_table_name: 'deals',
        target_column_name: 'deal_id',
        target_row_id: this.entityId.toString(),
        relationship_type: 'llm_output',
        relationship_title: 'Requirement Extraction Output',
        relationship_notes: `LLM output from requirement extraction processing`,
        display_order: 999, // Put at the end
        is_primary: false
      };
      
      await FileManager.createFileRelationship(uploadedFile.file_id, relationshipRequest, options);
      
      console.log(`✅ LLM output saved as file: ${fileName} (ID: ${uploadedFile.file_id})`);
      
    } catch (error) {
      console.error('Error saving LLM output as file:', error);
      // Don't throw error to avoid failing the entire process
    }
  }
} 