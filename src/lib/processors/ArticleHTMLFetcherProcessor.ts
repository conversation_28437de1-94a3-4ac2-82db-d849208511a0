import { BaseProcessor } from './BaseProcessor'
import { NewsProcessingState, EntityData, ProcessorOptions } from '../../types/processing'
import { createProcessorLoggerAdapter } from '../llm'
import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer'
import * as cheerio from 'cheerio'
import { LoginUtility, LoginConfiguration, LoginResult } from '../scrapers/LoginUtility'
import path from 'path'
import fs from 'fs'

interface ArticleData extends EntityData {
  article_id: number
  url: string
  processing_state: NewsProcessingState
  fetched: boolean
}

interface BrowserOptions {
  headless: boolean
  timeout: number
  minContentSize: number
  maxRetries: number
  retryDelay: number
}

export class ArticleHTMLFetcherProcessor extends BaseProcessor {
  private browser: Browser | null = null
  private browserOptions: BrowserOptions
  private browserRestartFrequency: number = 50
  private requestCount: number = 0
  private loginUtility: LoginUtility
  private loggedInSites: Set<string> = new Set()
  private loginAttempts: Map<string, number> = new Map()
  private maxLoginAttempts: number = 2
  private profileDir: string
  private cookieDir: string

  constructor(options: ProcessorOptions = {}) {
    super('ArticleHTMLFetcher', options)

    this.browserOptions = {
      headless: true,
      timeout: 30000,
      minContentSize: 1000,
      maxRetries: 3,
      retryDelay: 2000
    }

    // Setup cookie persistence directories
    this.profileDir = path.join(process.cwd(), 'scraper_profiles')
    this.cookieDir = path.join(this.profileDir, 'cookies')

    // Ensure directories exist
    if (!fs.existsSync(this.profileDir)) {
      fs.mkdirSync(this.profileDir, { recursive: true })
    }
    if (!fs.existsSync(this.cookieDir)) {
      fs.mkdirSync(this.cookieDir, { recursive: true })
    }

    // Initialize login utility with logger
    this.loginUtility = new LoginUtility((level, message) => {
      this.log(level as 'debug' | 'info' | 'warn' | 'error', message)
    })

    this.log('info', `ArticleHTMLFetcher initialized with headless: ${this.browserOptions.headless}`)
  }

  /**
   * Initialize browser with proper configuration and persistent profile
   */
  private async initializeBrowser(): Promise<Browser> {
    if (this.browser) {
      return this.browser
    }

        try {
      this.log('info', 'Launching browser...')
      
      const userDataDir = path.join(this.profileDir, 'chrome_profile')
      this.log('debug', `Browser userDataDir: ${userDataDir}`)
      
      const browser = await puppeteer.launch({
        headless: this.browserOptions.headless,
        userDataDir: userDataDir, // Add persistent user data directory
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-extensions',
          '--disable-popup-blocking',
          '--disable-background-timer-throttling',
          '--disable-backgrounding-occluded-windows',
          '--disable-renderer-backgrounding',
          '--disable-features=TranslateUI',
          '--disable-ipc-flooding-protection',
          '--no-first-run',
          '--no-default-browser-check',
          '--disable-default-apps',
          '--disable-sync',
          '--disable-background-networking',
          '--disable-component-update',
          '--disable-client-side-phishing-detection',
          '--enable-cookies',
          '--disable-cookie-encryption',
          '--profile-directory=Default'
        ],
        timeout: 30000
      })

      this.browser = browser
      this.log('info', 'Browser launched successfully')
      return browser
    } catch (error) {
      this.log('error', `Failed to launch browser: ${error}`)
      throw new Error(`Browser initialization failed: ${error}`)
    }
  }

  /**
   * Save cookies for a specific domain
   */
  private async saveCookies(page: Page, domain: string): Promise<void> {
    try {
      const cookies = await page.cookies()
      const cookieFile = path.join(this.cookieDir, `${domain}.json`)

      if (cookies.length === 0) {
        this.log('warn', `No cookies found to save for ${domain}`)
        return
      }

      fs.writeFileSync(cookieFile, JSON.stringify(cookies, null, 2))
      this.log('info', `Saved ${cookies.length} cookies for ${domain} to ${cookieFile}`)
    } catch (error) {
      this.log('error', `Failed to save cookies for ${domain}: ${error}`)
    }
  }

  /**
   * Load cookies for a specific domain
   */
  private async loadCookies(page: Page, domain: string): Promise<boolean> {
    try {
      const cookieFile = path.join(this.cookieDir, `${domain}.json`)

      if (!fs.existsSync(cookieFile)) {
        this.log('debug', `No cookie file found for ${domain} at ${cookieFile}`)
        return false
      }

      const cookieData = fs.readFileSync(cookieFile, 'utf-8')
      const cookies = JSON.parse(cookieData)

      if (!cookies || cookies.length === 0) {
        this.log('warn', `Cookie file for ${domain} exists but is empty`)
        return false
      }

      // Navigate to domain before applying cookies
      const baseUrl = `https://${domain}`
      this.log('debug', `Navigating to ${baseUrl} before loading cookies...`)
      await page.goto(baseUrl, { waitUntil: 'domcontentloaded', timeout: this.browserOptions.timeout })

      // Set cookies
      await page.setCookie(...cookies)
      this.log('info', `Loaded ${cookies.length} cookies for ${domain}`)

      // Refresh the page to apply cookies
      await page.reload({ waitUntil: 'domcontentloaded' })
      await new Promise(resolve => setTimeout(resolve, 2000))

      return true
    } catch (error) {
      this.log('error', `Failed to load cookies for ${domain}: ${error}`)
      return false
    }
  }

  /**
   * Get unprocessed article entries
   */
  async getUnprocessedEntities(): Promise<ArticleData[]> {
    const conditions = [
      'article_url IS NOT NULL'
    ]
    const params: unknown[] = []
    let paramIndex = 1

    // Handle multi-ID processing
    if (this.options.multiIds && this.options.multiIds.length > 0) {
      this.log('info', `Processing multiple article IDs: ${this.options.multiIds.join(', ')}`)

      // Create placeholder string for IN clause
      const placeholders = this.options.multiIds.map((_, index) => `$${paramIndex + index}`).join(', ')
      conditions.push(`article_id IN (${placeholders})`)

      // Add all IDs to params
      this.options.multiIds.forEach(id => params.push(id))
      paramIndex += this.options.multiIds.length

      // For multi-ID runs, allow reprocessing regardless of status
      const statusConditionIndex = conditions.findIndex(c => c.includes('fetch_status'))
      if (statusConditionIndex !== -1) {
        conditions.splice(statusConditionIndex, 1)
      }
    } else if (this.options.singleId) {
      conditions.push(`article_id = $${paramIndex}`)
      params.push(this.options.singleId)
      paramIndex++
    } else {
      conditions.push(`fetch_status = 'pending'`)
    }

    const sql = `
      SELECT article_id, article_url, fetch_status
      FROM article
      WHERE ${conditions.join(' AND ')}
      ORDER BY created_at ASC, publication_name ASC
      ${this.options.limit && !this.options.multiIds ? `LIMIT ${this.options.limit}` : ''}
    `

    const articleEntries = await this.query(sql, params)

    return articleEntries.map(entry => ({
      id: entry.article_id as number,
      article_id: entry.article_id as number,
      url: entry.article_url as string,
      processing_state: 'pending' as NewsProcessingState,
      fetched: entry.fetch_status === 'completed'
    }))
  }

  /**
   * Process a single article entity by fetching its HTML content
   */
  async processEntity(entity: ArticleData): Promise<{ success: boolean; error?: string }> {
    try {
      this.log('info', `Fetching HTML for article ID ${entity.article_id}: ${entity.url}`)

      // Mark as running
      await this.updateArticleHTMLFetchingStatus(entity.article_id, 'running')

      // Check if we need to restart browser
      if (this.requestCount >= this.browserRestartFrequency) {
        await this.restartBrowser()
      }

      const browser = await this.initializeBrowser()
      const page = await browser.newPage()

      try {
        // Configure page settings with randomization to avoid detection
        const viewports = [
          { width: 1920, height: 1080 },
          { width: 1366, height: 768 },
          { width: 1536, height: 864 },
          { width: 1440, height: 900 },
          { width: 1600, height: 900 }
        ]

        const userAgents = [
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
          'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]

        const randomViewport = viewports[Math.floor(Math.random() * viewports.length)]
        const randomUserAgent = userAgents[Math.floor(Math.random() * userAgents.length)]

        await page.setViewport(randomViewport)
        await page.setUserAgent(randomUserAgent)

        // Add extra headers to appear more human-like
        await page.setExtraHTTPHeaders({
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.9',
          'Accept-Encoding': 'gzip, deflate, br',
          'DNT': '1',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
        })

        this.log('debug', `Using viewport: ${randomViewport.width}x${randomViewport.height}, User-Agent: ${randomUserAgent.substring(0, 50)}...`)

        // Set longer timeout for slow pages
        page.setDefaultTimeout(this.browserOptions.timeout)
        page.setDefaultNavigationTimeout(this.browserOptions.timeout)

        // Check if login is required for this site and handle cookies
        const siteName = this.loginUtility.detectSiteFromUrl(entity.url)
        if (siteName && this.loginUtility.isSiteSupported(siteName)) {
          const config = this.loginUtility.getSiteConfig(siteName)
          if (config) {
            // Try to load existing cookies first
            const cookiesLoaded = await this.loadCookies(page, config.domain)
            if (cookiesLoaded) {
              this.log('info', `Loaded existing cookies for ${siteName}`)
            }

            // Ensure we're logged in (will use cookies if available)
            await this.ensureLoggedIn(page, siteName)
          }
        }

        // Navigate to URL with retry logic
        const htmlContent = await this.fetchWithRetry(page, entity.url)

        if (!htmlContent) {
          throw new Error('Failed to fetch HTML content after all retries')
        }

        // Validate content size
        if (htmlContent.length < this.browserOptions.minContentSize) {
          this.log('warn', `Content too small (${htmlContent.length} chars) for ${entity.url}`)
          // Continue processing even with small content as it might be intentional
        }

        // Store the HTML content
        await this.storeHTMLContent(entity.article_id, htmlContent)

        // Save cookies after successful fetch to maintain session
        if (siteName && this.loginUtility.isSiteSupported(siteName)) {
          const config = this.loginUtility.getSiteConfig(siteName)
          if (config) {
            await this.saveCookies(page, config.domain)
          }
        }

        this.log('info', `Successfully fetched ${htmlContent.length} characters for article ID ${entity.article_id}`)
        this.requestCount++

        return { success: true }
      } finally {
        await page.close()
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error processing article ${entity.article_id}: ${errorMessage}`)

      // Store error in database
      try {
        await this.storeError(entity.article_id, errorMessage)
      } catch (dbError) {
        this.log('error', `Failed to store error for article ${entity.article_id}: ${dbError}`)
      }

      return { success: false, error: errorMessage }
    }
  }

  /**
   * Ensure user is logged in to a site
   */
  private async ensureLoggedIn(page: Page, siteName: string): Promise<void> {
    // Check if we've already logged in to this site
    if (this.loggedInSites.has(siteName)) {
      this.log('debug', `Already logged in to ${siteName}`)
      return
    }

    // Check if we've exceeded max login attempts
    const attempts = this.loginAttempts.get(siteName) || 0
    if (attempts >= this.maxLoginAttempts) {
      this.log('warn', `Max login attempts exceeded for ${siteName}, continuing without login`)
      return
    }

    try {
      this.log('info', `Attempting to login to ${siteName}...`)

      // Get site configuration
      const config = this.loginUtility.getSiteConfig(siteName)
      if (!config) {
        this.log('warn', `No configuration found for ${siteName}`)
        return
      }

      // Navigate to the site's home page first
      await page.goto(config.homeUrl, { waitUntil: 'networkidle2', timeout: this.browserOptions.timeout })

      // Check if already logged in
      const isLoggedIn = await this.loginUtility.isLoggedIn(page, config)
      if (isLoggedIn) {
        this.log('info', `Already logged in to ${siteName}`)
        this.loggedInSites.add(siteName)
        return
      }

      // Attempt login
      const loginResult = await this.loginUtility.login(page, config)

      // Update login attempts
      this.loginAttempts.set(siteName, attempts + 1)

      if (loginResult.success) {
        this.log('info', `Successfully logged in to ${siteName}`)
        this.loggedInSites.add(siteName)

        // Save cookies after successful login
        await this.saveCookies(page, config.domain)
      } else {
        this.log('warn', `Login failed for ${siteName}: ${loginResult.error}`)

        // If we have credentials but login failed, wait before retrying
        if (config.email && config.password) {
          await new Promise(resolve => setTimeout(resolve, 5000))
        }
      }
    } catch (error) {
      this.log('error', `Login error for ${siteName}: ${error}`)
      this.loginAttempts.set(siteName, attempts + 1)
    }
  }

  /**
   * Fetch HTML content with retry logic
   */
  private async fetchWithRetry(page: Page, url: string): Promise<string | null> {
    for (let attempt = 1; attempt <= this.browserOptions.maxRetries; attempt++) {
      try {
        this.log('info', `Attempt ${attempt}/${this.browserOptions.maxRetries} for ${url}`)
        
        // Add debug logging for page state
        this.log('debug', `Page isClosed: ${page.isClosed()}`)

        // Navigate to the URL
        this.log('debug', `Starting page.goto for ${url}`)
        const response = await page.goto(url, {
          waitUntil: 'networkidle2',
          timeout: this.browserOptions.timeout
        })
        this.log('debug', `page.goto completed, response status: ${response?.status()}`)
        

        // Handle HTTP responses - 304 (Not Modified) is a valid response
        if (!response) {
          throw new Error('No response received')
        }

        const status = response.status()

        // Check for blocking HTTP status codes
        if (status === 403 || status === 429 || status === 503) {
          this.log('warn', `Detected blocking HTTP status ${status} for ${url} on attempt ${attempt}`)

          // If this is not the last attempt, add delay and retry (without restarting browser)
          if (attempt < this.browserOptions.maxRetries) {
            const delaySeconds = 10 + Math.random() * 10
            this.log('info', `Waiting ${delaySeconds.toFixed(1)} seconds before retrying due to blocking...`)
            await this.sleep(delaySeconds * 1000)
            continue
          } else {
            // On last attempt, treat as failure
            throw new Error(`Site is blocking requests with HTTP ${status} - all attempts failed`)
          }
        }

        if (!response.ok() && status !== 304) {
          throw new Error(`HTTP ${status} ${response.statusText()}`)
        }

        // For 304 responses, log that we're using cached content
        if (status === 304) {
          this.log('debug', `Received HTTP 304 (Not Modified) for ${url} - using cached content`)
        }

        // Wait for content to load
        await new Promise(resolve => setTimeout(resolve, 2000))

        // Check if we encountered a login wall, paywall, or blocking
        const pageContent = await page.content()

        // First check for blocking - this takes priority over login walls
        if (this.isBlocked(pageContent, url)) {
          this.log('warn', `Detected blocking for ${url} on attempt ${attempt}`)

          // If this is not the last attempt, add delay and retry (without restarting browser)
          if (attempt < this.browserOptions.maxRetries) {
            const delaySeconds = 10 + Math.random() * 10
            this.log('info', `Waiting ${delaySeconds.toFixed(1)} seconds before retrying due to content blocking...`)
            await this.sleep(delaySeconds * 1000)
            continue
          } else {
            // On last attempt, treat as failure
            throw new Error('Site is blocking requests - all attempts failed')
          }
        }

        // Check for login wall or paywall
        if (this.isLoginWall(pageContent) || this.isPaywall(pageContent)) {
          // Try to detect site and login
          const siteName = this.loginUtility.detectSiteFromUrl(url)
          if (siteName && this.loginUtility.isSiteSupported(siteName) && !this.loggedInSites.has(siteName)) {
            this.log('info', `Detected login wall for ${siteName}, attempting login...`)
            await this.ensureLoggedIn(page, siteName)

            // Retry fetching after login
            const retryResponse = await page.goto(url, {
              waitUntil: 'networkidle2',
              timeout: this.browserOptions.timeout
            })

            // Handle HTTP responses after login - 304 (Not Modified) is a valid response
            if (!retryResponse) {
              throw new Error('No response received after login')
            }

            const retryStatus = retryResponse.status()
            if (!retryResponse.ok() && retryStatus !== 304) {
              throw new Error(`HTTP ${retryStatus} ${retryResponse.statusText()} after login`)
            }

            // For 304 responses, log that we're using cached content
            if (retryStatus === 304) {
              this.log('debug', `Received HTTP 304 (Not Modified) for ${url} after login - using cached content`)
            }
          }
        }

        // Get the final HTML content
        const htmlContent = await page.content()

        // Final blocking check on the retrieved content
        if (this.isBlocked(htmlContent, url)) {
          this.log('warn', `Detected blocking in final content for ${url} on attempt ${attempt}`)

          // If this is not the last attempt, add delay and retry (without restarting browser)
          if (attempt < this.browserOptions.maxRetries) {
            const delaySeconds = 10 + Math.random() * 10
            this.log('info', `Waiting ${delaySeconds.toFixed(1)} seconds before retrying due to final content blocking...`)
            await this.sleep(delaySeconds * 1000)
            continue
          } else {
            // On last attempt, treat as failure
            throw new Error('Site is blocking requests in final content - all attempts failed')
          }
        }

        // Validate content
        if (htmlContent.length < this.browserOptions.minContentSize) {
          this.log('warn', `Content too small (${htmlContent.length} chars) on attempt ${attempt}`)
          if (attempt < this.browserOptions.maxRetries) {
            await this.sleep(this.browserOptions.retryDelay)
            continue
          }
        }

        return htmlContent
      } catch (error) {
        this.log('warn', `Attempt ${attempt} failed for ${url}: ${error}`)

        if (attempt < this.browserOptions.maxRetries) {
          await this.sleep(this.browserOptions.retryDelay * attempt) // Exponential backoff
        } else {
          this.log('error', `All ${this.browserOptions.maxRetries} attempts failed for ${url}`)
          return null
        }
      }
    }

    return null
  }

  /**
   * Check if page contains login wall indicators
   */
  private isLoginWall(htmlContent: string): boolean {
    const loginWallIndicators = [
      'please log in',
      'please sign in',
      'login to continue',
      'sign in to continue',
      'subscription required',
      'please subscribe',
      'create an account',
      'register to read',
      'become a subscriber',
      'join to read',
      'member login',
      'subscriber login',
      'access denied',
      'leaky_paywall_message_wrap',
      'leaky_paywall_message'
    ]

    const lowerContent = htmlContent.toLowerCase()
    return loginWallIndicators.some(indicator => lowerContent.includes(indicator))
  }

  /**
   * Check if page contains paywall indicators
   */
  private isPaywall(htmlContent: string): boolean {
    const paywallIndicators = [
      'paywall',
      'subscribe to read',
      'upgrade to premium',
      'premium content',
      'subscriber only',
      'members only',
      'exclusive content',
      'limited access',
      'free trial',
      'subscription',
      'unlock this article',
      'read more with subscription',
      'leaky_paywall_message_wrap',
      'leaky_paywall_message'
    ]

    const lowerContent = htmlContent.toLowerCase()
    return paywallIndicators.some(indicator => lowerContent.includes(indicator))
  }

  /**
   * Check if browser is blocked by the source
   */
  private isBlocked(htmlContent: string, url: string): boolean {
    const blockingIndicators = [
      // Strong CAPTCHA and bot detection indicators (more specific)
      'please verify you are human',
      'verify you are not a robot',
      'please solve the captcha',
      'solve the captcha to continue',
      'complete the captcha',
      'captcha verification required',
      
      // Clear bot detection messages
      'bot detection',
      'security check required',
      'checking your browser',
      'verifying your browser',
      'please wait while we check',
      'just a moment while we verify',
      
      // Cloudflare specific
      'cf-browser-verification',
      'cf-wrapper',
      'cloudflare security check',
      'ddos protection by cloudflare',

      // Rate limiting and access control (specific messages)
      'rate limit exceeded',
      'too many requests from your ip',
      'access denied - blocked',
      'your access has been blocked',
      'temporarily blocked due to',
      'service temporarily unavailable',
      'bandwidth limit exceeded',
      'request has been blocked',
      'access restricted for your ip',
      'your ip has been blocked',
      'unauthorized access detected',

      // Anti-bot measures (specific)
      'unusual traffic from your network',
      'suspicious activity detected',
      'automated requests not allowed',
      'looks like you are using an automated tool',
      'unusual activity from your ip',
      'bot protection enabled',
      'human verification required',
      'verification required to continue',
      'challenge required to access',

      // Specific security services
      'protected by incapsula',
      'protected by imperva',
      'protected by sucuri',
      'perimeterx security',
      'distil networks',
      'datadome protection',

      // Clear error pages only
      'error 403 forbidden',
      'error 429 too many requests',
      'error 503 service unavailable',
      'http 403 forbidden',
      'http 429 too many requests',
      'http 503 service unavailable'
    ]

    const lowerContent = htmlContent.toLowerCase()
    const lowerUrl = url.toLowerCase()

    // Check content for blocking indicators
    const contentBlocked = blockingIndicators.some(indicator => {
      if (lowerContent.includes(indicator)) {
        this.log('debug', `Blocking detected: found indicator "${indicator}" in content`)
        return true
      }
      return false
    })

    // Check if we're being redirected to a challenge page (more specific)
    const challengeUrls = [
      '/challenge/',
      '/captcha/',
      '/security-check/',
      '/human-verification/',
      '/bot-check/',
      'cloudflare.com/challenge',
      'verify-access'
    ]

    const urlBlocked = challengeUrls.some(challenge => {
      if (lowerUrl.includes(challenge)) {
        this.log('debug', `URL blocking detected: found "${challenge}" in URL "${lowerUrl}"`)
        return true
      }
      return false
    })

    // Check for very small content (might indicate a blocking page)
    const tooSmall = htmlContent.length < 500 && (
      lowerContent.includes('blocked') ||
      lowerContent.includes('denied') ||
      lowerContent.includes('forbidden') ||
      lowerContent.includes('captcha') ||
      lowerContent.includes('verify')
    )

    if (tooSmall) {
      this.log('debug', `Small content blocking detected: ${htmlContent.length} chars`)
    }

    const isBlocked = contentBlocked || urlBlocked || tooSmall
    if (isBlocked) {
      this.log('debug', `Content blocking result: contentBlocked=${contentBlocked}, urlBlocked=${urlBlocked}, tooSmall=${tooSmall}`)
    }

    return isBlocked
  }

  /**
   * Handle blocked scenario by restarting browser and adding delay
   */
  private async handleBlocked(url: string): Promise<void> {
    this.log('warn', `Detected blocking for ${url}. Restarting browser...`)

    // Restart browser immediately
    await this.restartBrowser()

    // Add a longer delay to avoid immediate re-blocking
    const delaySeconds = 10 + Math.random() * 10 // Random delay between 10-20 seconds
    this.log('info', `Waiting ${delaySeconds.toFixed(1)} seconds before retrying...`)
    await this.sleep(delaySeconds * 1000)

    // Clear any cached login state for all sites to force re-authentication
    this.loggedInSites.clear()
    this.loginAttempts.clear()

    this.log('info', 'Browser restarted and ready for retry')
  }

  /**
   * Extract title from HTML using Cheerio
   */
  private extractTitleFromHTML(html: string): string {
    try {
      const $ = cheerio.load(html)

      // Try to find title using common selectors in order of preference
      const titleSelectors = [
        'h1.entry-title',          // PincusCo and many WordPress sites
        '.entry-title',            // General WordPress entry title
        'h1.post-title',          // Common blog post title
        'h1.article-title',       // Article title
        '.article-title',         // Alternative article title
        'h1.title',               // Generic title
        '.title h1',              // Title within title container
        'article h1',             // H1 within article
        '.post-header h1',        // H1 in post header
        '.article-header h1',     // H1 in article header
        'header h1',              // H1 in header
        'h1',                     // Fallback to any h1
        'title',                  // HTML title tag as last resort
      ]

      for (const selector of titleSelectors) {
        const element = $(selector).first()
        if (element.length > 0) {
          const title = element.text().trim()
          if (title.length > 0) {
            this.log('debug', `Extracted title using selector '${selector}': ${title}`)
            return title
          }
        }
      }

      this.log('warn', 'No title found using any selector')
      return ''

    } catch (error) {
      this.log('error', `Error extracting title from HTML: ${error}`)
      return ''
    }
  }

  /**
   * Extract clean text content from HTML using Cheerio
   */
  private extractTextFromHTML(html: string): string {
    try {
      const $ = cheerio.load(html)

      // Remove unwanted elements
      $('script, style, nav, header, footer, aside, .advertisement, .ad, .ads, .sidebar, .menu, .navigation, .comments, .comment, .social, .share, .related, .popup, .modal, .overlay, .cookie, .gdpr').remove()

      // Try to find main content using common selectors
      const mainContentSelectors = [
        'article',
        '[role="main"]',
        'main',
        '.content',
        '.post-content',
        '.article-content',
        '.entry-content',
        '.post-body',
        '.article-body',
        '.story-body',
        '.news-content',
        '.text-content',
        '.main-content',
        '#content',
        '#main',
        '#article',
        '.article'
      ]

      let mainContent = ''

      // Try each selector until we find content
      for (const selector of mainContentSelectors) {
        const element = $(selector).first()
        if (element.length > 0) {
          const text = element.text().trim()
          if (text.length > 200) { // Ensure we have substantial content
            mainContent = text
            break
          }
        }
      }

      // If no main content found, try to extract from common paragraph containers
      if (!mainContent) {
        const paragraphs = $('p').map((_, el) => $(el).text().trim()).get()
        mainContent = paragraphs.join('\n\n')
      }

      // If still no content, fall back to body text
      if (!mainContent || mainContent.length < 100) {
        mainContent = $('body').text().trim()
      }

      // Clean up the text
      mainContent = mainContent
        .replace(/\s+/g, ' ') // Replace multiple spaces with single space
        .replace(/\n\s*\n/g, '\n\n') // Clean up multiple newlines
        .trim()

      // Validate minimum content length
      if (mainContent.length < 100) {
        this.log('warn', `Extracted text too short (${mainContent.length} chars), may not be quality content`)
      }

      return mainContent

    } catch (error) {
      this.log('error', `Error extracting text from HTML: ${error}`)
      return ''
    }
  }

  /**
   * Store HTML content in database
   */
  private async storeHTMLContent(articleId: number, htmlContent: string): Promise<void> {
    // Extract title and text from HTML
    const extractedTitle = this.extractTitleFromHTML(htmlContent)
    const extractedText = this.extractTextFromHTML(htmlContent)

    const sql = `
      UPDATE article
      SET article_body_text = $1,
          headline = $2,
          fetch_status = 'completed',
          fetch_date = NOW(),
          fetch_error = NULL,
          updated_at = NOW()
      WHERE article_id = $3
    `

    try {
      await this.query(sql, [extractedText, extractedTitle, articleId])
      const titleInfo = extractedTitle ? ` with title: "${extractedTitle}"` : ' (no title found)'
      this.log('debug', `Stored extracted text (${extractedText.length} chars), and title${titleInfo} for article ID ${articleId}`)
    } catch (error) {
      this.log('error', `Error storing text, and title for article ID ${articleId}: ${error}`)
      throw error
    }
  }

  /**
   * Store error in database
   */
  private async storeError(articleId: number, error: string): Promise<void> {
    const sql = `
      UPDATE article
      SET fetch_error = $1,
          fetch_status = 'failed',
          updated_at = NOW()
      WHERE article_id = $2
    `

    try {
      await this.query(sql, [JSON.stringify({ error, timestamp: new Date().toISOString() }), articleId])
      this.log('debug', `Stored error for article ID ${articleId}`)
    } catch (dbError) {
      this.log('error', `Error storing error for article ID ${articleId}: ${dbError}`)
      throw dbError
    }
  }



  /**
   * Update entity status after processing
   */
  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    try {
      if (success) {
        // Check if text were actually saved
        const checkSql = `
          SELECT LENGTH(article_body_text) as text_length, LENGTH(headline) as title_length, fetch_status
          FROM article
          WHERE article_id = $1
        `

        const result = await this.query(checkSql, [entityId])
        const textLength = parseInt(String(result[0]?.text_length) || '0', 10)
        const titleLength = parseInt(String(result[0]?.title_length) || '0', 10)
        const fetchStatus = result[0]?.fetch_status as string

        // Check if this is a manual processing (singleId mode)
        const isManualProcessing = this.options.singleId === entityId

        if (fetchStatus === 'completed' && textLength > 0) {
          await this.updateArticleHTMLFetchingStatus(entityId, 'completed')
          this.log('info', `Article ${entityId} marked as completed with ${textLength} characters of text${isManualProcessing ? ' (manual processing)' : ''}`)
        } else if (fetchStatus === 'completed' && textLength === 0) {
          // Fetch completed but no text extracted
          await this.updateArticleHTMLFetchingStatus(entityId, 'failed', 'Fetch completed but no text extracted')
          this.log('warn', `Article ${entityId} processing partially failed: fetch completed but no text extracted${isManualProcessing ? ' (manual processing)' : ''}`)
        } else {
          // Success but no content saved
          await this.updateArticleHTMLFetchingStatus(entityId, 'failed', 'No content saved')
          this.log('warn', `Article ${entityId} processing failed: no content saved${isManualProcessing ? ' (manual processing)' : ''}`)
        }
      } else {
        // Failed processing
        await this.updateArticleHTMLFetchingStatus(entityId, 'failed', error)
        const errorMsg = error ? `: ${error}` : ''
        this.log('error', `Article ${entityId} processing failed${errorMsg}`)
      }
    } catch (updateError) {
      this.log('error', `Error updating status for article ${entityId}: ${updateError}`)
    }
  }

  /**
   * Restart browser to prevent memory leaks and avoid blocking
   */
  private async restartBrowser(): Promise<void> {
    this.log('info', 'Restarting browser to prevent memory leaks and avoid blocking...')

    if (this.browser) {
      try {
        await this.browser.close()
      } catch (error) {
        this.log('warn', `Error closing browser: ${error}`)
      }
      this.browser = null
    }

    // Reset login state when browser restarts
    this.loggedInSites.clear()
    this.loginAttempts.clear()

    // Add a random delay to avoid pattern detection
    const restartDelay = 3000 + Math.random() * 2000 // Random delay between 3-5 seconds
    this.log('info', `Waiting ${(restartDelay / 1000).toFixed(1)} seconds before restarting...`)
    await this.sleep(restartDelay)

    this.requestCount = 0
    this.log('info', 'Browser restarted - login state cleared, cookies will be reloaded from disk')
  }

  /**
   * Sleep utility function
   */
  async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    if (this.browser) {
      try {
        await this.browser.close()
        this.browser = null
        this.log('info', 'Browser closed')
      } catch (error) {
        this.log('error', `Error closing browser: ${error}`)
      }
    }

    // Clear login state
    this.loggedInSites.clear()
    this.loginAttempts.clear()
  }

  /**
   * Override the main process method to add cleanup
   */
  async process(): Promise<any> {
    try {
      const result = await super.process()
      return result
    } finally {
      await this.cleanup()
    }
  }
}