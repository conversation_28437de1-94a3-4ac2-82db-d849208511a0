import { BaseProcessor } from './BaseProcessor'
import { ProcessorResult, EntityData } from '../../types/processing'

interface ScrapingOperation {
  type: 'scraping' | 'fetching' | 'manager'
  siteName?: string
  maxPages?: number
  tryLogin?: boolean
}

interface ScrapingAPIResponse {
  success: boolean
  operationId: string
  message: string
  type: string
  siteName?: string
  error?: string
}

interface ScrapingStatus {
  id: string
  type: string
  startTime: string
  duration: number
}

interface ScrapingStatusResponse {
  success: boolean
  activeProcessors: ScrapingStatus[]
  totalActive: number
}

/**
 * ArticleLinkFetcherProcessor - Calls the scraping API endpoint for comprehensive article/news scraping
 * 
 * This processor orchestrates web scraping operations by calling the /api/scraping endpoint
 * which handles browser automation, login capabilities, and scraping from multiple sources:
 * - Bisnow
 * - TheRealDeal
 * - GlobeSt
 * - PincusCo
 */
export class ArticleLinkFetcherProcessor extends BaseProcessor {
  private baseUrl: string
  private operations: ScrapingOperation[]

  constructor(options: { 
    limit?: number
    baseUrl?: string
    operations?: ScrapingOperation[]
    bottleneckConfig?: any
  } = {}) {
    super('ArticleLinkFetcherProcessor', {
      ...options,
      bottleneckConfig: {
        maxConcurrent: 1, // Only one scraping operation at a time
        minTime: 30000,   // 30 seconds between operations
        reservoir: 5,     // Max 5 operations per interval
        reservoirRefreshAmount: 5,
        reservoirRefreshInterval: 5 * 60 * 1000, // 5 minutes
        ...options.bottleneckConfig
      }
    })

    this.baseUrl = options.baseUrl || 'http://localhost:3030'
    this.operations = options.operations || [
      { type: 'manager', tryLogin: true, maxPages: 30 }, // Run all scrapers
      { type: 'fetching', maxPages: 50 } // News fetching
    ]
  }

  // Required abstract method implementations
  async getUnprocessedEntities(): Promise<EntityData[]> {
    // For API scraping, we don't process entities from database
    // Instead we process the configured operations
    return this.operations.map((op, index) => ({
      id: index,
      entity_type: 'article',
      entity_id: index,
      ...op as any
    })) as EntityData[]
  }

  async processEntity(entity: EntityData): Promise<{ success: boolean; error?: string }> {
    const operation: ScrapingOperation = {
      type: (entity as any).type,
      siteName: (entity as any).siteName,
      maxPages: (entity as any).maxPages,
      tryLogin: (entity as any).tryLogin
    }
    
    try {
      this.log('info', `Starting ${operation.type} scraping operation${operation.siteName ? ` for ${operation.siteName}` : ''}`)
      
      // Start the scraping operation
      const startResult = await this.startScrapingOperation(operation)
      
      if (!startResult.success) {
        return { success: false, error: `Failed to start ${operation.type} operation: ${startResult.error}` }
      }

      this.log('info', `Started scraping operation ${startResult.operationId}`)

      // Wait for the operation to complete or timeout
      const completed = await this.waitForCompletion(startResult.operationId, 30 * 60 * 1000) // 30 minutes timeout
      
      if (completed) {
        this.log('info', `Scraping operation ${startResult.operationId} completed successfully`)
        return { success: true }
      } else {
        this.log('warn', `Scraping operation ${startResult.operationId} timed out or failed`)
        return { success: false, error: `Operation ${startResult.operationId} timed out` }
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.log('error', `Error in ${operation.type} operation: ${errorMessage}`)
      return { success: false, error: errorMessage }
    }
  }

  async updateEntityStatus(entityId: number, success: boolean, error?: string): Promise<void> {
    // For API scraping, we don't need to update database status
    // Just log the result
    if (success) {
      this.log('info', `Scraping operation ${entityId} completed successfully`)
    } else {
      this.log('error', `Scraping operation ${entityId} failed: ${error}`)
    }
  }

  /**
   * Start a scraping operation via the API
   */
  private async startScrapingOperation(operation: ScrapingOperation): Promise<ScrapingAPIResponse> {
    const response = await fetch(`${this.baseUrl}/api/scraping`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(operation)
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return await response.json()
  }

  /**
   * Check the status of active scraping operations
   */
  private async getScrapingStatus(): Promise<ScrapingStatusResponse> {
    const response = await fetch(`${this.baseUrl}/api/scraping?action=status`)
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return await response.json()
  }

  /**
   * Wait for a scraping operation to complete
   */
  private async waitForCompletion(operationId: string, timeoutMs: number): Promise<boolean> {
    const startTime = Date.now()
    const checkInterval = 30000 // Check every 30 seconds

    while (Date.now() - startTime < timeoutMs) {
      try {
        const status = await this.getScrapingStatus()
        
        // Check if our operation is still running
        const isRunning = status.activeProcessors.some(p => p.id === operationId)
        
        if (!isRunning) {
          // Operation completed (or failed and was cleaned up)
          return true
        }

        this.log('info', `Operation ${operationId} still running, waiting...`)
        await new Promise(resolve => setTimeout(resolve, checkInterval))

      } catch (error) {
        this.log('error', `Error checking status for operation ${operationId}: ${error}`)
        await new Promise(resolve => setTimeout(resolve, checkInterval))
      }
    }

    return false // Timed out
  }
}