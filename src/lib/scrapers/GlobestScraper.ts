import { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SiteConfig, ScrapingResult } from './BaseScraper';
import * as path from 'path';

export class GlobestScraper extends BaseScraper {
  private startUrl: string;

  constructor(browser: <PERSON>rowser, page: Page, siteConfig: SiteConfig, waitTime: number = 1000) {
    super(browser, page, siteConfig, waitTime);
    this.startUrl = 'https://www.globest.com/';
  }

  async isLoggedIn(): Promise<boolean> {
    try {
      console.log(`[INFO] Checking login status for ${this.siteName}`);
      
      // If Register link is present, user is NOT logged in
      const registerElements = await this.page.$$('a[href*="ALMMD_GLOBEST_Reg"]');
      
      for (const element of registerElements) {
        const text = await element.evaluate(el => el.textContent?.trim() || '');
        if (text.toLowerCase().includes('register')) {
          console.log(`[INFO] Found Register link - user is NOT logged in to ${this.siteName}`);
          return false;
        }
      }
      
      console.log(`[INFO] No Register link found - user appears to be logged in to ${this.siteName}`);
      return true;
    } catch (error) {
      console.log(`[WARNING] Error checking login status for ${this.siteName}: ${error}`);
      return false;
    }
  }

  async login(): Promise<boolean> {
    console.log(`[INFO] ${this.siteName} login not implemented - proceeding without login`);
    return true;
  }

  async scrapeLinks(maxPages: number = 30): Promise<ScrapingResult> {
    console.log(`[INFO] Starting ${this.siteName} link scraping...`);
    
    const result: ScrapingResult = {
      newLinksFound: 0,
      totalLinksProcessed: 0,
      errors: [],
      success: false
    };
    
    // Navigate to the homepage
    if (!(await this.safeNavigate(this.startUrl))) {
      result.errors.push(`Could not access ${this.siteName} website`);
      return result;
    }
    
    let consecutiveNoNew = 0;
    const newLinksFile = path.join(this.profileDir, 'new_links.txt');
    
    try {
      for (let i = 1; i <= maxPages; i++) {
        console.log(`[${this.siteName.toUpperCase()}] Page ${i}: Fetching links...`);
        
        // Get all links on the page
        const linkElements = await this.page.$$eval('a', anchors => 
          anchors.map(anchor => anchor.href).filter(href => href)
        );
        
        // Filter valid article links
        const validLinks: string[] = [];
        let totalLinksFound = 0;
        
        for (const href of linkElements) {
          totalLinksFound++;
          if (this.isValidArticleUrl(href)) {
            validLinks.push(href);
          } else {
            // console.log(`[${this.siteName.toUpperCase()}] Page ${i}: ❌ Filtered out non-article URL: ${href}`);
          }
        }
        
        // Store new valid links in database
        const newlyInserted = await this.storeNewLinksInDb(validLinks);
        const insertCount = newlyInserted.length;
        await this.saveNewLinksToFile(newlyInserted, newLinksFile);
        
        result.totalLinksProcessed += totalLinksFound;
        result.newLinksFound += insertCount;
        
        console.log(`[${this.siteName.toUpperCase()}] Page ${i}: Found ${totalLinksFound} total links, filtered to ${validLinks.length} valid links, inserted ${insertCount} new ones.`);
        
        if (insertCount === 0) {
          consecutiveNoNew++;
          console.log(`[${this.siteName.toUpperCase()}] Page ${i}: No new article links found. Consecutive count: ${consecutiveNoNew}/${this.maxConsecutiveNoNew}`);
          if (consecutiveNoNew >= this.maxConsecutiveNoNew) {
            console.log(`[${this.siteName.toUpperCase()}] Hit max consecutive pages (${this.maxConsecutiveNoNew}) with 0 new article inserts. Stopping.`);
            break;
          }
        } else {
          consecutiveNoNew = 0;
          console.log(`[${this.siteName.toUpperCase()}] Page ${i}: Found ${insertCount} new articles. Reset consecutive counter.`);
        }
        
        // Scroll and wait
        await this.scrollToBottom();
        await new Promise(resolve => setTimeout(resolve, this.waitTime));
      }
      
      result.success = true;
      console.log(`[INFO] Finished ${this.siteName} scraping.`);
      
    } catch (error) {
      console.log(`[ERROR] Error during ${this.siteName} scraping: ${error}`);
      result.errors.push(String(error));
    }
    
    return result;
  }

  protected isValidArticleUrl(url: string): boolean {
    if (!super.isValidArticleUrl(url)) {
      return false;
    }
    
    // GlobeSt-specific validation - be more permissive
    const normalizedUrl = url.toLowerCase();
    
    // Exclude specific non-article patterns for GlobeSt
    const globestExcludePatterns = [
      '/advertise',
      '/sitemap',
      'almmd_globest_reg',
      '/events/',
      '/careers/',
      '/webinar',
      '/podcast',
      '/video/',
      '/photo/'
    ];
    
    // Check if URL contains any excluded patterns
    if (globestExcludePatterns.some(pattern => normalizedUrl.includes(pattern))) {
      return false;
    }
    
    // Real estate keywords for content detection
    const realEstateKeywords = [
      'hotel', 'office', 'residential', 'commercial', 'retail', 'industrial',
      'property', 'building', 'development', 'construction', 'real-estate',
      'investment', 'sale', 'lease', 'rent', 'acquisition', 'financing',
      'mortgage', 'loan', 'deal', 'transaction', 'market', 'portfolio',
      'reit', 'fund', 'capital', 'equity', 'debt', 'refinance', 'refi',
      'multifamily', 'apartment', 'condo', 'co-op', 'townhouse', 'warehouse',
      'facility', 'plaza', 'tower', 'center', 'complex', 'square', 'avenue',
      'street', 'million', 'billion', 'sf', 'sqft', 'acre', 'floor', 'story',
      'zoning', 'permits', 'planning', 'approval', 'closing', 'sold',
      'law', 'legal', 'technology', 'tech', 'proptech', 'fintech'
    ];
    
    // Check if URL contains real estate keywords
    const hasRealEstateContent = realEstateKeywords.some(keyword => 
      normalizedUrl.includes(keyword)
    );
    
    // GlobeSt section patterns (but don't require them)
    const globestSectionPatterns = [
      '/sectors/', '/news/', '/articles/', '/multifamily/', '/office/',
      '/retail/', '/industrial/', '/capital-markets/', '/construction/',
      '/law/', '/technology/'
    ];
    
    const hasKnownSection = globestSectionPatterns.some(pattern => url.includes(pattern));
    
    // Also check for URLs with meaningful content (longer descriptive paths)
    const urlPath = url.split('globest.com')[1] || '';
    const hasDescriptiveContent = urlPath.length > 15 && urlPath.includes('-');
    
    // Numeric ID patterns (GlobeSt articles often have IDs)
    const hasNumericId = /\/[\w-]+-\d+\/?$/.test(url);
    
    // Accept if it has real estate content, known sections, descriptive content, or numeric IDs
    return hasRealEstateContent || hasKnownSection || hasDescriptiveContent || hasNumericId;
  }
} 