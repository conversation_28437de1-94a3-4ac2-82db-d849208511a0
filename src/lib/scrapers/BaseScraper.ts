import { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { pool } from '@/lib/db';
import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import { MappingData } from '@/types/processing';

export interface SiteConfig {
  name: string;
  domain: string;
  home_url: string;
  login_url?: string;
  email_field_selector?: string;
  password_field_selector?: string;
  submit_button_selector?: string;
  success_indicator_xpath?: string;
  already_logged_in_indicators?: string[];
  not_logged_in_indicators?: string[];
  email?: string;
  password?: string;
  [key: string]: any;
}

export interface ScrapingResult {
  newLinksFound: number;
  totalLinksProcessed: number;
  errors: string[];
  success: boolean;
}

export abstract class BaseScraper {
  protected browser: Browser;
  protected page: Page;
  protected siteConfig: SiteConfig;
  protected waitTime: number;
  protected consecutiveNoNew: number = 0;
  protected maxConsecutiveNoNew: number = 3;
  protected profileDir: string;
  protected cookieDir: string;
  protected newsSource: string;
  protected siteName: string;

  private static mappingCache: MappingData | null = null;
  constructor(browser: Browser, page: Page, siteConfig: SiteConfig, waitTime: number = 1000) {
    this.browser = browser;
    this.page = page;
    this.siteConfig = siteConfig;
    this.waitTime = waitTime;
    this.newsSource = siteConfig.name;
    this.siteName = siteConfig.name;
    
    // Setup directories
    const currentDir = path.dirname(fileURLToPath(import.meta.url));
    this.profileDir = path.join(currentDir, '../../../', 'scraper_profiles');
    this.cookieDir = path.join(this.profileDir, 'cookies');
    
    // Ensure directories exist
    this.ensureDirectories();
  }

  private ensureDirectories(): void {
    if (!fs.existsSync(this.profileDir)) {
      fs.mkdirSync(this.profileDir, { recursive: true });
    }
    if (!fs.existsSync(this.cookieDir)) {
      fs.mkdirSync(this.cookieDir, { recursive: true });
    }
  }

  protected async safeNavigate(url: string, maxRetries: number = 3): Promise<boolean> {
    let retryCount = 0;
    let delay = 2000;

    while (retryCount < maxRetries) {
      try {
        console.log(`[INFO] Navigating to URL: ${url}`);
        await this.page.goto(url, { 
          waitUntil: 'networkidle2', 
          timeout: 30000 
        });
        await new Promise(resolve => setTimeout(resolve, this.waitTime));
        return true;
      } catch (error: any) {
        retryCount++;
        const errorMessage = error.message || String(error);
        
        // Check for common network errors
        const networkError = ['ERR_NAME_NOT_RESOLVED', 'ERR_CONNECTION_REFUSED', 
                             'ERR_NETWORK_CHANGED', 'ERR_CONNECTION_RESET',
                             'ERR_INTERNET_DISCONNECTED', 'ERR_TIMED_OUT'].some(err => 
                               errorMessage.includes(err));
        
        if (networkError && retryCount < maxRetries) {
          console.log(`[WARNING] Network error accessing ${url}: ${errorMessage}`);
          console.log(`[INFO] Retrying in ${delay}ms (attempt ${retryCount}/${maxRetries})...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          delay *= 2;
        } else {
          console.log(`[ERROR] Failed to navigate to ${url} after ${retryCount} attempts: ${errorMessage}`);
          return false;
        }
      }
    }
    
    return false;
  }

  protected async scrollToBottom(): Promise<void> {
    await this.page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });
    await new Promise(resolve => setTimeout(resolve, this.waitTime));
  }

  protected async saveCookies(domain: string): Promise<void> {
    try {
      const cookies = await this.page.cookies();
      const cookieFile = path.join(this.cookieDir, `${domain}.json`);
      
      if (cookies.length === 0) {
        console.log(`[WARNING] No cookies found to save for ${domain}`);
        return;
      }
      
      fs.writeFileSync(cookieFile, JSON.stringify(cookies, null, 2));
      console.log(`[INFO] Saved ${cookies.length} cookies for ${domain} to ${cookieFile}`);
    } catch (error) {
      console.log(`[ERROR] Failed to save cookies for ${domain}: ${error}`);
    }
  }

  protected async loadCookies(domain: string): Promise<boolean> {
    try {
      const cookieFile = path.join(this.cookieDir, `${domain}.json`);
      
      if (!fs.existsSync(cookieFile)) {
        console.log(`[WARNING] No cookie file found for ${domain} at ${cookieFile}`);
        return false;
      }
      
      // Navigate to domain before applying cookies
      const baseUrl = this.siteConfig.home_url || `https://www.${domain}`;
      console.log(`[INFO] Navigating to ${baseUrl} before loading cookies...`);
      if (!(await this.safeNavigate(baseUrl))) {
        console.log(`[WARNING] Failed to navigate to ${baseUrl} before loading cookies`);
        return false;
      }
      
      const cookieData = fs.readFileSync(cookieFile, 'utf-8');
      const cookies = JSON.parse(cookieData);
      
      if (!cookies || cookies.length === 0) {
        console.log(`[WARNING] Cookie file for ${domain} exists but is empty`);
        return false;
      }
      
      // Set cookies
      await this.page.setCookie(...cookies);
      console.log(`[INFO] Loaded ${cookies.length} cookies for ${domain}`);
      
      // Refresh the page to apply cookies
      await this.page.reload();
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      return true;
    } catch (error) {
      console.log(`[ERROR] Failed to load cookies for ${domain}: ${error}`);
      return false;
    }
  }

  protected async storeNewLinksInDb(links: string[]): Promise<string[]> {
    if (!links || links.length === 0) {
      return [];
    }

    // Normalize and deduplicate
    const normalizedSet = new Set<string>();
    for (const rawLink of links) {
      const normLink = this.normalizeUrl(rawLink);
      if (normLink) {
        normalizedSet.add(normLink);
      }
    }

    if (normalizedSet.size === 0) {
      return [];
    }

    const client = await pool.connect();
    try {
      // Check which URLs already exist in the new article table
      const existingResult = await client.query(
        'SELECT article_url FROM article WHERE article_url = ANY($1)',
        [Array.from(normalizedSet)]
      );

      const existingUrls = new Set(existingResult.rows.map(row => row.article_url));
      const toInsert = Array.from(normalizedSet).filter(url => !existingUrls.has(url));

      if (toInsert.length === 0) {
        return [];
      }

      // Insert new URLs into the new article table with conflict handling
      const insertQuery = `
        INSERT INTO article (
          article_url,
          publication_name,
          scraping_source_type,
          fetch_status,
          extraction_status,
          created_at,
          updated_at
        )
        VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
        ON CONFLICT (article_url) DO NOTHING
      `;

      let actualInserted = 0;
      for (const url of toInsert) {
        const result = await client.query(insertQuery, [
          url,                    // article_url
          this.newsSource,        // publication_name
          'HTML Parse',           // scraping_source_type
          'pending',              // fetch_status
          'pending',              // extraction_status
        ]);
        if (result.rowCount && result.rowCount > 0) {
          actualInserted++;
        }
      }
      console.log(`[INFO] Attempted to insert ${toInsert.length} URLs, actually inserted ${actualInserted} new ${this.newsSource} links into article table.`);

      // Return only the URLs that were actually inserted (for backward compatibility)
      return actualInserted === toInsert.length ? toInsert : [];
    } catch (error) {
      console.log(`[ERROR] storeNewLinksInDb => ${error}`);
      return [];
    } finally {
      client.release();
    }
  }

  protected normalizeUrl(url: string): string {
    if (!url) return url;
    
    // Remove everything after '?'
    const basePart = url.split('?')[0];
    // Remove trailing slash or '?'
    return basePart.replace(/[/?]+$/, '');
  }

  protected async saveNewLinksToFile(links: string[], filePath: string): Promise<number> {
    if (!links || links.length === 0) {
      return 0;
    }

    let existing = new Set<string>();
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf-8');
      existing = new Set(content.split('\n').filter(line => line.trim()));
    }

    const toAdd = links.filter(link => !existing.has(link));
    if (toAdd.length > 0) {
      fs.appendFileSync(filePath, toAdd.join('\n') + '\n');
      console.log(`[INFO] Wrote ${toAdd.length} newly inserted links to ${filePath}.`);
      return toAdd.length;
    } else {
      console.log('[INFO] No additional new links to save to file.');
      return 0;
    }
  }

  protected async takeScreenshot(filename: string): Promise<void> {
    try {
      const screenshotPath = path.join(this.profileDir, `${filename}.png`);
      await this.page.screenshot({ path: screenshotPath, fullPage: true });
      console.log(`[INFO] Screenshot saved to ${screenshotPath}`);
    } catch (error) {
      console.log(`[ERROR] Failed to take screenshot: ${error}`);
    }
  }

  protected isValidArticleUrl(url: string, siteDomain?: string): boolean {
    if (!url) return false;
    
    // ENHANCED: Strict protocol validation
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      // console.log(`[VALIDATION] ❌ Invalid protocol: ${url}`);
      return false;
    }
    
    // ENHANCED: Check for malformed URLs
    if (url.includes('javascript:') || url.includes('mailto:') || url.includes('tel:') || 
        url === 'url' || url.length < 10 || url.includes('void(0)')) {
          // console.log(`[VALIDATION] ❌ Malformed URL: ${url}`);
      return false;
    }
    
    // Basic validation - contains the site domain and looks like an article URL
    const domain = siteDomain || this.siteConfig.domain;
    if (!url.includes(domain)) {
      // console.log(`[VALIDATION] ❌ Wrong domain for ${this.newsSource}: ${url}`);
      return false;
    }
    
    // ENHANCED: Strict domain validation per news source
    if (!this.isValidDomainForSource(url, domain)) {
      // console.log(`[VALIDATION] ❌ Invalid domain for ${this.newsSource}: ${url}`);
      return false;
    }
    
    // Normalize URL for checking
    const normalizedUrl = url.toLowerCase();
    
    // ENHANCED: Filter out social media and external services
    const socialMediaPatterns = [
      'twitter.com', 'facebook.com', 'instagram.com', 'linkedin.com', 'youtube.com',
      'tiktok.com', 'pinterest.com', 'snapchat.com'
    ];
    
    if (socialMediaPatterns.some(pattern => normalizedUrl.includes(pattern))) {
      // console.log(`[VALIDATION] ❌ Social media URL: ${url}`);
      return false;
    }
    
    // ENHANCED: Filter out external service domains
    const externalServicePatterns = [
      'selectleaders.com', 'bisnowelevate.com', 'northgatereg.com', 'bigscoots.com',
      'alm.com', 'benefitspro.com', 'thinkadvisor.com', 'treasuryandrisk.com',
      'queryly.com', 'parsintl.com', 'touchpointmarkets.com', 'dragonforms.com',
      'tradepub.com', 'nationalunderwriter.com', 'cutimes.com', 'propertycasualty360.com',
      'consultingmag.com'
    ];
    
    if (externalServicePatterns.some(pattern => normalizedUrl.includes(pattern))) {
      // console.log(`[VALIDATION] ❌ External service URL: ${url}`);
      return false;
    }
    
    // Filter out file extensions and technical URLs
    const fileExtensions = [
      '.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp', '.ico',
      '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.zip',
      '.css', '.js', '.xml', '.rss', '.json', '.txt', '.csv'
    ];
    
    if (fileExtensions.some(ext => normalizedUrl.includes(ext))) {
      // console.log(`[VALIDATION] ❌ File extension: ${url}`);
      return false;
    }
    
    // Filter out clearly non-article URLs
    const excludePatterns = [
      '/contact', '/about', '/privacy', '/terms', '/login', '/logout', '/register',
      '/wp-admin', '/wp-content', '/wp-login', '/admin/', '/dashboard',
      '/search?', '/sitemap', '/feed/', '/rss', '/api/', '/ajax',
      'javascript:', 'mailto:', 'tel:', '#', '?utm_', '&utm_',
      '/newsletter', '/subscribe', '/unsubscribe', '/account/', '/profile/',
      '/cart/', '/checkout/', '/payment/', '/billing/'
    ];
    
    if (excludePatterns.some(pattern => normalizedUrl.includes(pattern))) {
      // console.log(`[VALIDATION] ❌ Excluded pattern: ${url}`);
      return false;
    }
    
    // Site-specific validation
    return this.isValidSiteSpecificUrl(url, normalizedUrl, domain);
  }

  // ENHANCED: New method for strict domain validation
  protected isValidDomainForSource(url: string, domain: string): boolean {
    const normalizedUrl = url.toLowerCase();
    
    // Define strict domain patterns for each news source
    const domainPatterns: Record<string, string[]> = {
      'bisnow': ['bisnow.com'],
      'pincus': ['pincus'],
      'globest': ['globest.com'],
      'therealdeal': ['therealdeal.com'],
      'therealDeal': ['therealdeal.com']
    };
    
    const expectedDomains = domainPatterns[this.newsSource.toLowerCase()] || [domain];
    
    return expectedDomains.some(expectedDomain => 
      normalizedUrl.includes(expectedDomain.toLowerCase())
    );
  }

  protected isValidSiteSpecificUrl(url: string, normalizedUrl: string, domain: string): boolean {
    // Default implementation - can be overridden by subclasses
    
    // Site-specific patterns
    if (domain.includes('globest.com')) {
      // Globest: Only allow URLs with date patterns like /2025/04/02/
      const datePattern = /\/\d{4}\/\d{2}\/\d{2}\//;
      return datePattern.test(url);
    }
    
    if (domain.includes('therealdeal.com')) {
      // TheRealDeal: Allow URLs with location and date patterns like /san-francisco/2025/07/06/
      const locationDatePattern = /\/[a-z-]+\/\d{4}\/\d{2}\/\d{2}\//;
      return locationDatePattern.test(url);
    }
    
    if (domain.includes('bisnow.com')) {
      // Bisnow: Allow city pages (intermediate pages) and article URLs
      const urlPath = url.split(domain)[1] || '';
      
      // Allow city pages (like /baltimore, /chicago, /denver, etc.)
      // These are intermediate pages that contain article links
      const cityPagePattern = /^\/[a-z-]+\/?$/;
      if (cityPagePattern.test(urlPath)) {
        return true;
      }
      
      // Allow actual article URLs with news content
      if (normalizedUrl.includes('/news/')) {
        return true;
      }
      
      // Allow URLs with article ID numbers (common Bisnow pattern)
      if (/\/\d+$/.test(urlPath)) {
        return true;
      }
      
      return false;
    }
    
    // Default validation for other sites
    const urlPath = url.split(domain)[1] || '';
    if (urlPath.length < 5 || urlPath === '/' || urlPath === '') {
      return false;
    }
    
    // Filter out URLs with query parameters that suggest they're not articles
    if (normalizedUrl.includes('?page=') || normalizedUrl.includes('&page=') ||
        normalizedUrl.includes('?search=') || normalizedUrl.includes('&search=') ||
        normalizedUrl.includes('?filter=') || normalizedUrl.includes('&filter=')) {
      return false;
    }
    
    // If we've made it this far, it's likely an article URL
    return true;
  }

  // Abstract methods that must be implemented by subclasses
  abstract isLoggedIn(): Promise<boolean>;
  abstract login(): Promise<boolean>;
  abstract scrapeLinks(maxPages?: number): Promise<ScrapingResult>;

  // Main method to run the scraper
  async runScraper(tryLogin: boolean = false, maxPages: number = 30): Promise<ScrapingResult> {
    try {
      console.log(`[INFO] Starting ${this.siteName} scraper...`);
      
      // Navigate to home page
      if (!(await this.safeNavigate(this.siteConfig.home_url))) {
        return {
          newLinksFound: 0,
          totalLinksProcessed: 0,
          errors: [`Failed to navigate to ${this.siteConfig.home_url}`],
          success: false
        };
      }
      
      // Try to load cookies first (always try to load cached session)
      console.log(`[INFO] Loading cached cookies for ${this.siteConfig.domain}`);
      const cookiesLoaded = await this.loadCookies(this.siteConfig.domain);
      
      if (cookiesLoaded) {
        console.log(`[INFO] Loaded cached cookies for ${this.siteName}`);
      }
      
      // Check if we're logged in after loading cookies
      const loggedIn = await this.isLoggedIn();
      
      // If not logged in and login is requested, try fresh login
      if (!loggedIn && tryLogin) {
        console.log(`[INFO] Not logged in to ${this.siteName}. Attempting fresh login...`);
        const loginSuccess = await this.login();
        if (loginSuccess) {
          console.log(`[INFO] Successfully logged in to ${this.siteName}`);
          await this.saveCookies(this.siteConfig.domain);
        } else {
          console.log(`[WARNING] Login failed, continuing with available access`);
        }
      }
      
      // Determine access level
      const finalLoggedIn = await this.isLoggedIn();
      if (finalLoggedIn) {
        console.log(`[INFO] Scraping ${this.siteName} with authenticated access`);
      } else {
        console.log(`[INFO] Scraping ${this.siteName} with guest access`);
      }
      
      // Run the scraping regardless of login status
      const result = await this.scrapeLinks(maxPages);
      
      // Save cookies after scraping (if login occurred)
      if (finalLoggedIn) {
        await this.saveCookies(this.siteConfig.domain);
      }
      
      console.log(`[INFO] Finished scraping ${this.siteName}. Found ${result.newLinksFound} new links.`);
      
      return result;
    } catch (error) {
      console.log(`[ERROR] Error in runScraper for ${this.siteName}: ${error}`);
      return {
        newLinksFound: 0,
        totalLinksProcessed: 0,
        errors: [String(error)],
        success: false
      };
    }
  }

  /**
   * Fetch standardized mappings from central_mapping table (static)
   */
  public static async getMappings(): Promise<MappingData> {
    if (BaseScraper.mappingCache) {
      return BaseScraper.mappingCache;
    }
    try {
      const sql = `
        SELECT type, level_1, value_1, level_2, value_2 
        FROM central_mapping 
        WHERE is_active = true 
        ORDER BY type, level_1, value_1
      `;
      const results = await pool.query(sql);
      const mappings: MappingData = {};
      for (const row of results.rows) {
        const type = row.type as string;
        const level1 = row.level_1 as string;
        const value1 = row.value_1 as string;
        const level2 = row.level_2 as string;
        const value2 = row.value_2 as string;
        // Primary level (type -> value_1)
        if (!mappings[type]) {
          mappings[type] = [];
        }
        if (value1 && !mappings[type].includes(value1)) {
          mappings[type].push(value1);
        }
        // Secondary level for subcategories (level_2 -> value_2)
        if (level2 && value2) {
          const subcategoryKey = `${type} - ${level2}`;
          if (!mappings[subcategoryKey]) {
            mappings[subcategoryKey] = [];
          }
          if (!mappings[subcategoryKey].includes(value2)) {
            mappings[subcategoryKey].push(value2);
          }
        }
      }
      BaseScraper.mappingCache = mappings;
      console.log(`[INFO] Loaded ${Object.keys(mappings).length} mapping types from central_mapping table`);
      return mappings;
    } catch (error) {
      console.log(`[ERROR] Error fetching mappings: ${error}`);
      // Return empty mappings to continue processing
      return {};
    }
  }
} 