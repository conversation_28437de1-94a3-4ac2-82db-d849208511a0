import { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, SiteConfig, ScrapingResult } from './BaseScraper';
import * as path from 'path';

export class PincusScraper extends BaseScraper {
  private startUrl: string;

  constructor(browser: Browser, page: Page, siteConfig: SiteConfig, waitTime: number = 1000) {
    super(browser, page, siteConfig, waitTime);
    this.startUrl = 'https://www.pincusco.com/';
  }

  async isLoggedIn(): Promise<boolean> {
    try {
      console.log(`[INFO] Checking login status for ${this.siteName}`);
      
      const content = await this.page.content();
      const pageSource = content.toLowerCase();
      
      const loggedInIndicators = this.siteConfig.already_logged_in_indicators || ['log out', 'my account', 'subscriber profile'];
      
      // Check for logged in indicators
      for (const indicator of loggedInIndicators) {
        if (pageSource.includes(indicator.toLowerCase())) {
          console.log(`[INFO] Detected logged-in state for ${this.siteName} (found: '${indicator}')`);
          return true;
        }
      }
      
      console.log(`[INFO] No login indicators found for ${this.siteName}`);
      return false;
    } catch (error) {
      console.log(`[WARNING] Error checking login status for ${this.siteName}: ${error}`);
      return false;
    }
  }

  async login(): Promise<boolean> {
    console.log(`[INFO] ${this.siteName} login not implemented - proceeding without login`);
    return true;
  }

  async scrapeLinks(maxPages: number = 30): Promise<ScrapingResult> {
    console.log(`[INFO] Starting ${this.siteName} link scraping...`);
    
    const result: ScrapingResult = {
      newLinksFound: 0,
      totalLinksProcessed: 0,
      errors: [],
      success: false
    };
    
    // Navigate to the homepage
    if (!(await this.safeNavigate(this.startUrl))) {
      result.errors.push(`Could not access ${this.siteName} website`);
      return result;
    }
    
    let consecutiveNoNew = 0;
    const newLinksFile = path.join(this.profileDir, 'new_links.txt');
    
    try {
      for (let i = 1; i <= maxPages; i++) {
        console.log(`[${this.siteName.toUpperCase()}] Page ${i}: Fetching links...`);
        
        // Get all links on the page, focusing on article links
        const linkElements = await this.page.$$eval('a', anchors => 
          anchors.map(anchor => anchor.href).filter(href => href)
        );
        
        // Filter valid article links
        const validLinks: string[] = [];
        let totalLinksFound = 0;
        
        for (const href of linkElements) {
          totalLinksFound++;
          if (this.isValidArticleUrl(href)) {
            validLinks.push(href);
          } else {
            // console.log(`[${this.siteName.toUpperCase()}] Page ${i}: ❌ Filtered out non-article URL: ${href}`);
          }
        }
        
        // Store new valid links in database
        const newlyInserted = await this.storeNewLinksInDb(validLinks);
        const insertCount = newlyInserted.length;
        await this.saveNewLinksToFile(newlyInserted, newLinksFile);
        
        result.totalLinksProcessed += totalLinksFound;
        result.newLinksFound += insertCount;
        
        console.log(`[${this.siteName.toUpperCase()}] Page ${i}: Found ${totalLinksFound} total links, filtered to ${validLinks.length} valid links, inserted ${insertCount} new ones.`);
        
        if (insertCount === 0) {
          consecutiveNoNew++;
          console.log(`[${this.siteName.toUpperCase()}] Page ${i}: No new article links found. Consecutive count: ${consecutiveNoNew}/${this.maxConsecutiveNoNew}`);
          if (consecutiveNoNew >= this.maxConsecutiveNoNew) {
            console.log(`[${this.siteName.toUpperCase()}] Hit max consecutive pages (${this.maxConsecutiveNoNew}) with 0 new article inserts. Stopping.`);
            break;
          }
        } else {
          consecutiveNoNew = 0;
          console.log(`[${this.siteName.toUpperCase()}] Page ${i}: Found ${insertCount} new articles. Reset consecutive counter.`);
        }
        
        // Scroll and wait, then try to navigate to more content
        await this.scrollToBottom();
        await new Promise(resolve => setTimeout(resolve, this.waitTime));
        
        // Try to find pagination or refresh
        await this.page.reload();
        await new Promise(resolve => setTimeout(resolve, this.waitTime));
      }
      
      result.success = true;
      console.log(`[INFO] Finished ${this.siteName} scraping.`);
      
    } catch (error) {
      console.log(`[ERROR] Error during ${this.siteName} scraping: ${error}`);
      result.errors.push(String(error));
    }
    
    return result;
  }

  protected isValidArticleUrl(url: string): boolean {
    if (!super.isValidArticleUrl(url)) {
      return false;
    }
    
    // PincusCo-specific validation - be restrictive to get only articles
    const normalizedUrl = url.toLowerCase();
    const urlPath = url.split('pincusco.com')[1] || '';
    
    // Exclude dashboard and report patterns
    const pincusExcludePatterns = [
      // Dashboard and subscription pages
      'pincusco-subscription',
      'pincusco-quarterly',
      'pincusco-foreclosure',
      'dashboard-charts-tables',
      '-professional',
      '-free-version',
      '-basic-financing-version',
      'posts-subscribers',
      
      // Report and data pages
      'past-12-months-',
      'new-development-condos-updated-daily',
      
      // System and category pages
      '/wp-admin',
      '/wp-login',
      '/feed',
      '/author/',
      '/date/',
      '/page/',
      '/tag/',
      '/category/',
      '/events/',
      '/careers/',
      '/advertise',
      'development_feed',
      '_feed'
    ];
    
    // Check if URL contains any excluded patterns
    if (pincusExcludePatterns.some(pattern => normalizedUrl.includes(pattern))) {
      return false;
    }
    
    // PincusCo articles must be direct URLs with meaningful content
    // Valid articles are typically at the root level with descriptive names
    // Examples: /mixed-use-in-nolita-sells-for-5-5m-adds-to-possible-assemblage
    
    // Must not be a category, feed, or system page
    if (urlPath.startsWith('/category') || urlPath.includes('_feed') || urlPath.includes('/wp-')) {
      return false;
    }
    
    // Must have meaningful descriptive content (long URLs with hyphens)
    const hasDescriptiveContent = urlPath.length > 15 && urlPath.includes('-');
    
    // Check for real estate transaction indicators (common in PincusCo articles)
    const transactionIndicators = [
      'sells', 'pays', 'signs', 'buys', 'purchases', 'acquires',
      'loan', 'refi', 'refinance', 'construction-loan',
      'million', 'billion', 'm-', '-m-', '-m$',
      'mixed-use', 'industrial', 'commercial', 'residential',
      'hotel', 'office', 'retail', 'warehouse', 'building',
      'manhattan', 'brooklyn', 'queens', 'bronx', 'tribeca',
      'nolita', 'maspeth', 'bedford-stuyvesant', 'lenox-hill'
    ];
    
    const hasTransactionContent = transactionIndicators.some(indicator => 
      normalizedUrl.includes(indicator)
    );
    
    // Must have both descriptive content AND transaction-related content
    return hasDescriptiveContent && hasTransactionContent;
  }
}
