import { pool } from '@/lib/db';
import { AsyncUploadService, type UploadLogRecord } from '@/lib/services/AsyncUploadService';
import { InvestmentCriteriaProcessor, type ProcessingInput, type EnhancedInvestmentCriteriaRecord, type CentralMappings, type GlobalRowData } from '@/lib/processors/InvestmentCriteriaProcessor';
import { extractDomain, formatWebsite, normalizeLinkedInUrl } from '@/lib/utils/domain';
import Bottleneck from 'bottleneck';

export interface ProcessingStats {
  uploadId: string
  totalRows: number
  processedRows: number
  successfulRows: number
  failedRows: number
  conflictRows: number
  companiesCreated: number
  contactsCreated: number
  investmentCriteriaCreated: number
  startTime: number
  endTime: number
  processingTimeMs: number
  errors: string[]

  // Legacy properties for backward compatibility
  companiesProcessed?: number
  contactsProcessed?: number
  investmentCriteriaProcessed?: number
  conflictsDetected?: number
  errorsEncountered?: number
}

interface DatabaseFieldsMetadata {
  companies: string[]
  contacts: string[]
  investment_criteria_central: string[]
  investment_criteria_debt: string[]
  investment_criteria_equity: string[]
}

interface ConflictData {
  [fieldName: string]: {
    existing_value: any
    new_value: any
    source: string
    created_at: string
    field_type: string
    match_reason?: string
  }
}

interface DatabaseCompany {
  company_id: number
  company_name: string
  company_website?: string
  company_linkedin?: string
  [key: string]: any
}

interface DatabaseContact {
  contact_id: number
  first_name?: string
  last_name?: string
  email?: string
  linkedin_url?: string
  [key: string]: any
}

interface BottleneckConfig {
  maxConcurrent: number
  minTime: number
  highWater: number
  strategy: any
  timeout: number
  retryAttempts: number
  retryDelayBase: number
  retryDelayMax: number
  defaultPriority: number
  enableJobMetrics: boolean
}

export class UploadProcessor {
  private batchSize: number;
  private client: any;
  private databaseFields: DatabaseFieldsMetadata | null = null;
  private concurrency: number;
  private bottleneck: Bottleneck;
  private bottleneckConfig: BottleneckConfig;
  private jobMetrics: Map<string, { startTime: number; endTime?: number; success?: boolean; error?: string }>;

  constructor(batchSize: number = 500, concurrency: number = 1) {
    this.batchSize = batchSize;
    this.concurrency = concurrency;
    this.jobMetrics = new Map();
    
    // Initialize Bottleneck configuration for upload processing
    this.bottleneckConfig = this.getDefaultBottleneckConfig();
    
    // Create Bottleneck instance for upload processing
    this.bottleneck = new Bottleneck({
      maxConcurrent: this.bottleneckConfig.maxConcurrent,
      minTime: this.bottleneckConfig.minTime,
      highWater: this.bottleneckConfig.highWater,
      strategy: this.bottleneckConfig.strategy,
      timeout: this.bottleneckConfig.timeout,
      retryDelayBase: this.bottleneckConfig.retryDelayBase,
      retryDelayMax: this.bottleneckConfig.retryDelayMax
    });

    // Set up Bottleneck event listeners for monitoring
    this.setupBottleneckEventListeners();
  }

  /**
   * Get default Bottleneck configuration optimized for upload processing
   */
  private getDefaultBottleneckConfig(): BottleneckConfig {
    return {
      maxConcurrent: parseInt(process.env.UPLOAD_PROCESSOR_MAX_CONCURRENT || '5'),
      minTime: parseInt(process.env.UPLOAD_PROCESSOR_MIN_TIME || '100'),
      highWater: parseInt(process.env.UPLOAD_PROCESSOR_HIGH_WATER || '1000'),
      strategy: Bottleneck.strategy.OVERFLOW,
      timeout: parseInt(process.env.UPLOAD_PROCESSOR_JOB_TIMEOUT || '180000'), // 3 minutes
      retryAttempts: parseInt(process.env.UPLOAD_PROCESSOR_RETRY_ATTEMPTS || '3'),
      retryDelayBase: parseInt(process.env.UPLOAD_PROCESSOR_RETRY_DELAY_BASE || '1000'),
      retryDelayMax: parseInt(process.env.UPLOAD_PROCESSOR_RETRY_DELAY_MAX || '30000'),
      defaultPriority: parseInt(process.env.UPLOAD_PROCESSOR_DEFAULT_PRIORITY || '5'),
      enableJobMetrics: process.env.UPLOAD_PROCESSOR_ENABLE_METRICS === 'true'
    };
  }

  /**
   * Set up Bottleneck event listeners for monitoring and debugging
   */
  private setupBottleneckEventListeners(): void {
    this.bottleneck.on('failed', (error) => {
      console.error(`[UploadProcessor] Job failed:`, error);
    });

    this.bottleneck.on('retry', (error, jobInfo) => {
      console.warn(`[UploadProcessor] Job retrying (attempt ${jobInfo.retryCount}):`, error);
    });

    // Monitor queue status
    setInterval(async () => {
      const status = await this.getBottleneckStatus();
      if (status.queued > 0 || status.running > 0) {
        console.log(`[UploadProcessor] Queue Status: ${status.queued} queued, ${status.running} running`);
      }
    }, 10000); // Log every 10 seconds
  }

  /**
   * Execute a function with Bottleneck rate limiting and retry logic
   */
  private async executeWithBottleneck<T>(
    fn: () => Promise<T>,
    jobId: string,
    priority?: number,
    enableRetry: boolean = true
  ): Promise<T> {
    const startTime = Date.now();
    
    if (this.bottleneckConfig.enableJobMetrics) {
      this.jobMetrics.set(jobId, { startTime });
    }

    try {
      const result = await this.bottleneck.schedule(
        { priority: priority || this.bottleneckConfig.defaultPriority },
        async () => {
          if (enableRetry) {
            return this.executeWithRetry(fn, jobId);
          } else {
            return fn();
          }
        }
      );

      return result;
    } catch (error) {
      if (this.bottleneckConfig.enableJobMetrics) {
        const metric = this.jobMetrics.get(jobId);
        if (metric) {
          metric.endTime = Date.now();
          metric.success = false;
          metric.error = error instanceof Error ? error.message : String(error);
        }
      }
      throw error;
    }
  }

  /**
   * Execute function with exponential backoff retry logic
   */
  private async executeWithRetry<T>(fn: () => Promise<T>, jobId: string): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= this.bottleneckConfig.retryAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt === this.bottleneckConfig.retryAttempts) {
          throw lastError;
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          this.bottleneckConfig.retryDelayBase * Math.pow(2, attempt - 1),
          this.bottleneckConfig.retryDelayMax
        );

        console.warn(`[UploadProcessor] Job ${jobId} failed on attempt ${attempt}, retrying in ${delay}ms:`, lastError.message);
        await this.sleep(delay);
      }
    }

    throw lastError!;
  }

  /**
   * Get current Bottleneck status
   */
  private async getBottleneckStatus() {
    return {
      queued: await this.bottleneck.queued(),
      running: await this.bottleneck.running(),
      done: await this.bottleneck.done()
    };
  }

  /**
   * Get job metrics for monitoring
   */
  public getJobMetrics() {
    const metrics: Record<string, any> = {};
    this.jobMetrics.forEach((metric, jobId) => {
      metrics[jobId] = {
        ...metric,
        duration: metric.endTime ? metric.endTime - metric.startTime : undefined
      };
    });
    return metrics;
  }

  /**
   * Clear job metrics to prevent memory issues
   */
  public clearJobMetrics(): void {
    this.jobMetrics.clear();
  }

  /**
   * Sleep utility function
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Cleanup Bottleneck when done
   */
  public async cleanup(): Promise<void> {
    await this.bottleneck.stop();
  }

  /**
   * Process a single row with error handling and timing
   */
  private async processRowWithErrorHandling(
    rowData: Record<string, any>,
    structuredHeaderMappings: Record<string, string>,
    uploadId: string,
    rowIndex: number,
    batchId: string
  ): Promise<{
    success: boolean;
    error?: string;
    companiesCreated?: number;
    contactsCreated?: number;
    investmentCriteriaCreated?: number;
    hasConflicts?: boolean;
    rowIndex: number;
    batchId: string;
  }> {
    const startTime = Date.now();
    
    try {
      console.log(`\n🔄 [Batch ${batchId}] Processing Row ${rowIndex}`);
      
      // Call the existing processRow method
      const result = await this.processRow(rowData, structuredHeaderMappings, uploadId, rowIndex);
      
      const processingTime = Date.now() - startTime;
      console.log(`   ✅ [Batch ${batchId}] Row ${rowIndex} completed in ${processingTime}ms`);
      
      return {
        ...result,
        rowIndex,
        batchId
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error(`   ❌ [Batch ${batchId}] Row ${rowIndex} failed after ${processingTime}ms:`, error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        rowIndex,
        batchId
      };
    }
  }

  /**
   * Process multiple rows in parallel using bottleneck
   */
  private async processRowsBatch(
    dataRows: any[],
    structuredHeaderMappings: Record<string, string>,
    uploadId: string,
    batchId: string
  ): Promise<{
    batchResults: any[];
    batchStats: {
      totalRows: number;
      successfulRows: number;
      failedRows: number;
      conflictRows: number;
      companiesCreated: number;
      contactsCreated: number;
      investmentCriteriaCreated: number;
      processingTimeMs: number;
    };
  }> {
    const batchStartTime = Date.now();
    console.log(`\n🚀 === BATCH ${batchId} BOTTLENECK PROCESSING START ===`);
    console.log(`   📦 Processing ${dataRows.length} rows with Bottleneck concurrency control`);
    console.log(`   ⚙️  Max Concurrent: ${this.bottleneckConfig.maxConcurrent}, Min Time: ${this.bottleneckConfig.minTime}ms`);
    
    // Create processing jobs for all rows in the batch
    const processingJobs = dataRows.map((dataRow, index) => {
      const reconstructedRowData = this.reconstructRowData(dataRow);
      const jobId = `upload-${uploadId}-batch-${batchId}-row-${dataRow.row_number}`;
      
      return this.executeWithBottleneck(
        async () => {
          return await this.processRowWithErrorHandling(
            reconstructedRowData,
            structuredHeaderMappings,
            uploadId,
            dataRow.row_number,
            batchId
          );
        },
        jobId,
        undefined, // Use default priority
        true // Enable retry
      );
    });

    // Process all jobs with Bottleneck concurrency control
    const batchResults = await Promise.allSettled(processingJobs);
    
    // Process results and calculate statistics
    const batchStats = {
      totalRows: dataRows.length,
      successfulRows: 0,
      failedRows: 0,
      conflictRows: 0,
      companiesCreated: 0,
      contactsCreated: 0,
      investmentCriteriaCreated: 0,
      processingTimeMs: Date.now() - batchStartTime
    };

    const processedResults: any[] = [];

    batchResults.forEach((result, index) => {
      const dataRow = dataRows[index];
      
      if (result.status === 'fulfilled') {
        const rowResult = result.value;
        processedResults.push(rowResult);
        
        if (rowResult.success) {
          batchStats.successfulRows++;
          batchStats.companiesCreated += rowResult.companiesCreated || 0;
          batchStats.contactsCreated += rowResult.contactsCreated || 0;
          batchStats.investmentCriteriaCreated += rowResult.investmentCriteriaCreated || 0;
          
          if (rowResult.hasConflicts) {
            batchStats.conflictRows++;
          }
          
          // Mark data row as processed
          AsyncUploadService.markDataRowAsProcessed(dataRow.data_id).catch(err => {
            console.error(`Failed to mark row ${dataRow.row_number} as processed:`, err);
          });
        } else {
          batchStats.failedRows++;
        }
      } else {
        // Promise was rejected
        batchStats.failedRows++;
        const error = result.reason;
        console.error(`Row ${dataRow.row_number} promise rejected:`, error);
        
        processedResults.push({
          success: false,
          error: error instanceof Error ? error.message : 'Promise rejected',
          rowIndex: dataRow.row_number,
          batchId
        });
      }
    });

    // Final batch statistics
    const batchSuccessRate = batchStats.totalRows > 0 ? Math.round((batchStats.successfulRows / batchStats.totalRows) * 100) : 0;
    
    console.log(`\n✅ === BATCH ${batchId} BOTTLENECK PROCESSING COMPLETE ===`);
    console.log(`   📊 Batch Results:`);
    console.log(`      • Total: ${batchStats.totalRows} rows`);
    console.log(`      • Success: ${batchStats.successfulRows} (${batchSuccessRate}%)`);
    console.log(`      • Failed: ${batchStats.failedRows}`);
    console.log(`      • Conflicts: ${batchStats.conflictRows}`);
    console.log(`      • Companies: ${batchStats.companiesCreated}`);
    console.log(`      • Contacts: ${batchStats.contactsCreated}`);
    console.log(`      • Criteria: ${batchStats.investmentCriteriaCreated}`);
    console.log(`      • Time: ${(batchStats.processingTimeMs / 1000).toFixed(2)}s`);
    console.log(`      • Rate: ${batchStats.totalRows > 0 ? (batchStats.totalRows / (batchStats.processingTimeMs / 1000)).toFixed(1) : 0} rows/sec`);

    return {
      batchResults: processedResults,
      batchStats
    };
  }

  /**
   * Main processing method - now with bottleneck processing support
   */
  async processUpload(uploadId: string): Promise<ProcessingStats> {
    console.log(`🚀 === STARTING BOTTLENECK UPLOAD PROCESSING ===`);
    console.log(`📋 Upload ID: ${uploadId}`);
    console.log(`⚙️  Configuration: Batch Size: ${this.batchSize}, Max Concurrent: ${this.bottleneckConfig.maxConcurrent}, Min Time: ${this.bottleneckConfig.minTime}ms`);
    
    const startTime = Date.now();
    const stats: ProcessingStats = {
      uploadId,
      totalRows: 0,
      processedRows: 0,
      successfulRows: 0,
      failedRows: 0,
      conflictRows: 0,
      companiesCreated: 0,
      contactsCreated: 0,
      investmentCriteriaCreated: 0,
      startTime,
      endTime: 0,
      processingTimeMs: 0,
      errors: [],
      // Legacy properties
      companiesProcessed: 0,
      contactsProcessed: 0,
      investmentCriteriaProcessed: 0,
      conflictsDetected: 0,
      errorsEncountered: 0
    };

    try {
      // Mark upload as processing
      console.log(`📌 Marking upload ${uploadId} as processing...`);
      await AsyncUploadService.markUploadAsProcessing(parseInt(uploadId));

      // Get upload info and header mappings
      console.log(`📊 Fetching upload info for ${uploadId}...`);
      const uploadInfo = await AsyncUploadService.getUploadStatus(parseInt(uploadId));
      if (!uploadInfo) {
        throw new Error(`Upload ${uploadId} not found`);
      }

      // Fetch header mappings from upload_logs table
      console.log(`🗺️  Fetching header mappings for upload ${uploadId}...`);
      const structuredHeaderMappings = await this.getHeaderMappingsForUpload(parseInt(uploadId));
      if (!structuredHeaderMappings) {
        throw new Error(`Header mappings not found for upload ${uploadId}`);
      }

      stats.totalRows = uploadInfo.total_rows;
      console.log(`📈 UPLOAD SUMMARY:`);
      console.log(`   • File: ${uploadInfo.file_name}`);
      console.log(`   • Total Rows: ${uploadInfo.total_rows}`);
      console.log(`   • Status: ${uploadInfo.status}`);
      console.log(`   • Created: ${uploadInfo.created_at}`);

      // Process data in parallel batches
      let batchNumber = 0;
      const totalBatches = Math.ceil(stats.totalRows / this.batchSize);

      while (true) {
        const dataRows = await AsyncUploadService.getDataForProcessing(
          parseInt(uploadId), 
          this.batchSize
        );

        if (dataRows.length === 0) {
          console.log('✅ No more unprocessed rows found - all data processed');
          break;
        }

        batchNumber++;
        const batchId = `B${batchNumber.toString().padStart(3, '0')}`;
        const progressPercent = Math.round((stats.processedRows / stats.totalRows) * 100);
        
        console.log(`\n📦 === BATCH ${batchId} SETUP ===`);
        console.log(`   • Batch: ${batchNumber}/${totalBatches}`);
        console.log(`   • Size: ${dataRows.length} rows`);
        console.log(`   • Progress: ${stats.processedRows}/${stats.totalRows} (${progressPercent}%)`);
        console.log(`   • Success Rate: ${stats.processedRows > 0 ? Math.round((stats.successfulRows / stats.processedRows) * 100) : 0}%`);

        // Process batch in parallel
        const batchResult = await this.processRowsBatch(
          dataRows,
          structuredHeaderMappings,
          uploadId,
          batchId
        );

        // Update overall statistics
        stats.processedRows += batchResult.batchStats.totalRows;
        stats.successfulRows += batchResult.batchStats.successfulRows;
        stats.failedRows += batchResult.batchStats.failedRows;
        stats.conflictRows += batchResult.batchStats.conflictRows;
        stats.companiesCreated += batchResult.batchStats.companiesCreated;
        stats.contactsCreated += batchResult.batchStats.contactsCreated;
        stats.investmentCriteriaCreated += batchResult.batchStats.investmentCriteriaCreated;

        // Collect errors from failed results
        batchResult.batchResults.forEach(result => {
          if (!result.success && result.error) {
            stats.errors.push(`Row ${result.rowIndex}: ${result.error}`);
          }
        });

        // Update progress with enhanced batch info
        const currentProgressPercent = Math.round((stats.processedRows / stats.totalRows) * 100);
        console.log(`\n📊 === CUMULATIVE PROGRESS UPDATE ===`);
        console.log(`   • Processed: ${stats.processedRows}/${stats.totalRows} (${currentProgressPercent}%)`);
        console.log(`   • Success: ${stats.successfulRows} | Failed: ${stats.failedRows} | Conflicts: ${stats.conflictRows}`);
        console.log(`   • Companies Created: ${stats.companiesCreated}`);
        console.log(`   • Contacts Created: ${stats.contactsCreated}`);
        console.log(`   • Criteria Created: ${stats.investmentCriteriaCreated}`);
        console.log(`   • Batch Processing Time: ${(batchResult.batchStats.processingTimeMs / 1000).toFixed(2)}s`);
        console.log(`   • Batch Rate: ${(batchResult.batchStats.totalRows / (batchResult.batchStats.processingTimeMs / 1000)).toFixed(1)} rows/sec`);

        // Update database progress
        await AsyncUploadService.updateProgress(parseInt(uploadId), stats.processedRows, {
          companiesProcessed: stats.companiesCreated,
          contactsProcessed: stats.contactsCreated,
          conflictsDetected: stats.conflictRows
        });

      }
      
      // Final progress update
      console.log(`\n📊 === FINAL PROGRESS UPDATE ===`);
      await AsyncUploadService.updateProgress(parseInt(uploadId), stats.processedRows, {
        companiesProcessed: stats.companiesCreated,
        contactsProcessed: stats.contactsCreated,
        conflictsDetected: stats.conflictRows
      });

      // Mark upload as completed
      console.log(`✅ Marking upload ${uploadId} as completed...`);
      await AsyncUploadService.markUploadAsCompleted(parseInt(uploadId));
      
      stats.endTime = Date.now();
      stats.processingTimeMs = stats.endTime - stats.startTime;
      
      // Enhanced final logging with bottleneck processing statistics
      console.log(`\n🎉 === BOTTLENECK UPLOAD ${uploadId} COMPLETED ===`);
      console.log(`📊 FINAL STATISTICS:`);
      console.log(`   • Total Rows: ${stats.totalRows}`);
      console.log(`   • Processed: ${stats.processedRows} (${((stats.processedRows/stats.totalRows)*100).toFixed(1)}%)`);
      console.log(`   • Successful: ${stats.successfulRows} (${stats.processedRows > 0 ? ((stats.successfulRows/stats.processedRows)*100).toFixed(1) : 0}%)`);
      console.log(`   • Failed: ${stats.failedRows}`);
      console.log(`   • With Conflicts: ${stats.conflictRows}`);
      console.log(`📈 RECORDS CREATED:`);
      console.log(`   • Companies: ${stats.companiesCreated}`);
      console.log(`   • Contacts: ${stats.contactsCreated}`);
      console.log(`   • Investment Criteria: ${stats.investmentCriteriaCreated}`);
      console.log(`⏱️  PERFORMANCE (BOTTLENECK):`);
      console.log(`   • Total Time: ${(stats.processingTimeMs / 1000).toFixed(2)}s`);
      console.log(`   • Average per Row: ${stats.processedRows > 0 ? (stats.processingTimeMs / stats.processedRows).toFixed(0) : 0}ms`);
      console.log(`   • Rows per Second: ${stats.processedRows > 0 ? (stats.processedRows / (stats.processingTimeMs / 1000)).toFixed(1) : 0}`);
      console.log(`   • Max Concurrent: ${this.bottleneckConfig.maxConcurrent}`);
      console.log(`   • Min Time Between Jobs: ${this.bottleneckConfig.minTime}ms`);
      console.log(`   • Batches Processed: ${batchNumber}`);
      console.log(`   • Avg Batch Size: ${Math.round(stats.processedRows / batchNumber)}`);
      
      // Log Bottleneck metrics if enabled
      if (this.bottleneckConfig.enableJobMetrics) {
        const jobMetrics = this.getJobMetrics();
        const successfulJobs = Object.values(jobMetrics).filter(m => m.success).length;
        const failedJobs = Object.values(jobMetrics).filter(m => !m.success).length;
        const avgDuration = Object.values(jobMetrics)
          .filter(m => m.duration)
          .reduce((sum, m) => sum + (m.duration || 0), 0) / Object.keys(jobMetrics).length;
        
        console.log(`🔧 BOTTLENECK METRICS:`);
        console.log(`   • Total Jobs: ${Object.keys(jobMetrics).length}`);
        console.log(`   • Successful Jobs: ${successfulJobs}`);
        console.log(`   • Failed Jobs: ${failedJobs}`);
        console.log(`   • Average Job Duration: ${avgDuration ? avgDuration.toFixed(0) : 0}ms`);
      }
      
      if (stats.errors.length > 0) {
        console.log(`\n⚠️  ERRORS ENCOUNTERED (${stats.errors.length}):`);
        stats.errors.slice(0, 5).forEach((error, index) => {
          console.log(`   ${index + 1}. ${error}`);
        });
        if (stats.errors.length > 5) {
          console.log(`   ... and ${stats.errors.length - 5} more errors`);
        }
      }
      
      console.log(`🎯 === BOTTLENECK PROCESSING COMPLETED SUCCESSFULLY ===\n`);
      
      // Cleanup Bottleneck
      await this.cleanup();
      
      return stats;

    } catch (error) {
      console.error('❌ === BOTTLENECK UPLOAD PROCESSING FAILED ===');
      console.error(`Upload ID: ${uploadId}`);
      console.error(`Error:`, error);
      console.error(`Processed Rows: ${stats.processedRows}/${stats.totalRows}`);
      
      stats.endTime = Date.now();
      stats.processingTimeMs = stats.endTime - stats.startTime;
      
      // Mark upload as failed
      try {
        await AsyncUploadService.markUploadAsFailed(parseInt(uploadId), error instanceof Error ? error.message : 'Unknown error');
      } catch (markError) {
        console.error('Failed to mark upload as failed:', markError);
      }
      
      // Cleanup Bottleneck even on failure
      try {
        await this.cleanup();
      } catch (cleanupError) {
        console.error('Failed to cleanup Bottleneck:', cleanupError);
      }
      
      throw error;
    }
  }

  /**
   * Process multiple uploads that are pending with bottleneck processing
   */
  static async processAllPending(concurrency: number = 3): Promise<ProcessingStats[]> {
    console.log(`🚀 === PROCESSING ALL PENDING UPLOADS (BOTTLENECK) ===`);
    console.log(`⚙️  Upload Concurrency: ${concurrency}`);
    
    const pendingUploads = await AsyncUploadService.getPendingUploads();
    const results: ProcessingStats[] = [];

    console.log(`📋 Found ${pendingUploads.length} pending uploads to process`);

    // Process uploads with concurrency control
    const chunks: UploadLogRecord[][] = [];
    for (let i = 0; i < pendingUploads.length; i += concurrency) {
      chunks.push(pendingUploads.slice(i, i + concurrency));
    }

    // Process uploads in chunks
    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (upload: UploadLogRecord) => {
        const processor = new UploadProcessor();
        
        try {
          const result = await processor.processUpload(upload.upload_id.toString());
          console.log(`✅ Upload ${upload.upload_id} completed successfully`);
          return result;
        } catch (error) {
          console.error(`❌ Upload ${upload.upload_id} failed:`, error);
          const errorStats: ProcessingStats = {
            uploadId: upload.upload_id.toString(),
            totalRows: upload.total_rows || 0,
            processedRows: 0,
            successfulRows: 0,
            failedRows: upload.total_rows || 0,
            conflictRows: 0,
            companiesCreated: 0,
            contactsCreated: 0,
            investmentCriteriaCreated: 0,
            startTime: Date.now(),
            endTime: Date.now(),
            processingTimeMs: 0,
            errors: [error instanceof Error ? error.message : 'Unknown error']
          };
          return errorStats;
        }
      });

      const chunkResults = await Promise.allSettled(chunkPromises);
      
      chunkResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          const upload = chunk[index];
          console.error(`Upload ${upload?.upload_id} promise failed:`, result.reason);
          const errorStats: ProcessingStats = {
            uploadId: upload?.upload_id?.toString() || 'unknown',
            totalRows: upload?.total_rows || 0,
            processedRows: 0,
            successfulRows: 0,
            failedRows: upload?.total_rows || 0,
            conflictRows: 0,
            companiesCreated: 0,
            contactsCreated: 0,
            investmentCriteriaCreated: 0,
            startTime: Date.now(),
            endTime: Date.now(),
            processingTimeMs: 0,
            errors: [result.reason instanceof Error ? result.reason.message : 'Promise rejected']
          };
          results.push(errorStats);
        }
      });
    }
    
    console.log(`🎉 === ALL PENDING UPLOADS PROCESSED ===`);
    console.log(`📊 Total uploads processed: ${results.length}`);
    console.log(`✅ Successful: ${results.filter(r => r.errors.length === 0).length}`);
    console.log(`❌ Failed: ${results.filter(r => r.errors.length > 0).length}`);

    return results;
  }


  /**
   * Save company data with automatic conflict detection and handling
   */
  private async saveCompanyData(
    companyData: Record<string, any>,
    source: string,
    client: any
  ): Promise<{ success: boolean; id?: string; hasConflicts?: boolean; error?: string }> {
    try {
      // Check for conflicts first
      const conflicts = await this.detectCompanyConflicts(companyData, source, client);
      const hasConflicts = !!(conflicts && Object.keys(conflicts).length > 0);
      
      // Save with conflicts
      const companyId = await this.saveCompanyWithConflicts(companyData, conflicts, source, client);
      
      return {
        success: true,
        id: companyId || undefined,
        hasConflicts
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Save contact data with automatic conflict detection and handling
   */
  private async saveContactData(
    contactData: Record<string, any>,
    source: string,
    client: any
  ): Promise<{ success: boolean; id?: string; hasConflicts?: boolean; error?: string }> {
    try {
      // Check for conflicts first
      const conflicts = await this.detectContactConflicts(contactData, source, client);
      const hasConflicts = !!(conflicts && Object.keys(conflicts).length > 0);
      
      // Save with conflicts
      const contactId = await this.saveContactWithConflicts(contactData, conflicts, source, client);
      
      return {
        success: true,
        id: contactId || undefined,
        hasConflicts
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Detect company conflicts by checking against existing data
   */
  private async detectCompanyConflicts(
    companyData: Record<string, any>,
    source: string,
    client: any
  ): Promise<ConflictData | null> {
    try {
      // Check if company exists using multiple criteria
      const existingCompany = await this.findExistingCompany(companyData, client);
      
      if (!existingCompany) {
        return null; // No conflicts if company doesn't exist
      }

      // Compare fields to detect conflicts
      const conflicts: ConflictData = {};
      const timestamp = new Date().toISOString();

      const systemFields = ['created_at', 'updated_at', 'company_id', 'processing_error_count', 'conflict_status', 'conflicts', 'conflict_created_at', 'conflict_source'];
      const fieldsToCheck = Object.keys(companyData).filter(f => !systemFields.includes(f));
      fieldsToCheck.forEach(field => {
        const existingValue = existingCompany[field];
        const newValue = companyData[field];
        
        // Skip null/empty/undefined values - don't search for empty data
        if (!existingValue || !newValue || 
            existingValue.toString().trim() === '' || 
            newValue.toString().trim() === '') {
          return;
        }
        
        // Special handling for LinkedIn fields - normalize URLs before comparison
        if (field === 'company_linkedin' || field === 'linkedin' || field === 'linkedin_url') {
          const existingNormalized = normalizeLinkedInUrl(existingValue.toString().trim());
          const newNormalized = normalizeLinkedInUrl(newValue.toString().trim());
          
          // Only flag conflict if normalized LinkedIn URLs are different (not empty)
          if (existingNormalized && newNormalized && existingNormalized !== newNormalized) {
            conflicts[field] = {
              existing_value: existingValue,
              new_value: newValue,
              source,
              created_at: timestamp,
              field_type: 'company',
              match_reason: 'linkedin_comparison'
            };
          }
        } else if (field === 'website' || field === 'company_website' || field === 'web_site' || field === 'url') {
          // Special handling for website fields - compare domains instead of raw URLs
          const existingDomain = extractDomain(existingValue.toString().trim());
          const newDomain = extractDomain(newValue.toString().trim());
          
          // Only flag conflict if domains are different (not empty)
          if (existingDomain && newDomain && existingDomain !== newDomain) {
            conflicts[field] = {
              existing_value: existingValue,
              new_value: newValue,
              source,
              created_at: timestamp,
              field_type: 'company',
              match_reason: 'domain_comparison'
            };
          }
        } else {
          // Regular field comparison for non-website fields
          if (existingValue.toString().trim() !== newValue.toString().trim()) {
            conflicts[field] = {
              existing_value: existingValue,
              new_value: newValue,
              source,
              created_at: timestamp,
              field_type: 'company',
              match_reason: 'field_comparison'
            };
          }
        }
      });

      return Object.keys(conflicts).length > 0 ? conflicts : null;
    } catch (error) {
      console.error('Error detecting company conflicts:', error);
      return null;
    }
  }

  /**
   * Detect contact conflicts by checking against existing data
   */
  private async detectContactConflicts(
    contactData: Record<string, any>,
    source: string,
    client: any
  ): Promise<ConflictData | null> {
    try {
      // Check if contact exists using multiple criteria
      const existingContact = await this.findExistingContact(contactData, client);
      
      if (!existingContact) {
        return null; // No conflicts if contact doesn't exist
      }

      // Compare fields to detect conflicts
      const conflicts: ConflictData = {};
      const timestamp = new Date().toISOString();

      const contactSystemFields = ['created_at', 'updated_at', 'contact_id', 'company_id', 'processing_error_count', 'conflict_status', 'conflicts', 'conflict_created_at', 'conflict_source'];
      const contactFieldsToCheck = Object.keys(contactData).filter(f => !contactSystemFields.includes(f));
      contactFieldsToCheck.forEach(field => {
        const existingValue = existingContact[field];
        const newValue = contactData[field];
        
        // Skip null/empty/undefined values - don't search for empty data
        if (!existingValue || !newValue || 
            existingValue.toString().trim() === '' || 
            newValue.toString().trim() === '') {
          return;
        }
        
        // Special handling for LinkedIn fields - normalize URLs before comparison
        if (field === 'linkedin_url' || field === 'linkedin') {
          const existingNormalized = normalizeLinkedInUrl(existingValue.toString().trim());
          const newNormalized = normalizeLinkedInUrl(newValue.toString().trim());
          
          // Only flag conflict if normalized LinkedIn URLs are different (not empty)
          if (existingNormalized && newNormalized && existingNormalized !== newNormalized) {
            conflicts[field] = {
              existing_value: existingValue,
              new_value: newValue,
              source,
              created_at: timestamp,
              field_type: 'contact',
              match_reason: 'linkedin_comparison'
            };
          }
        } else {
          // Regular field comparison for non-LinkedIn fields
          if (existingValue.toString().trim() !== newValue.toString().trim()) {
            conflicts[field] = {
              existing_value: existingValue,
              new_value: newValue,
              source,
              created_at: timestamp,
              field_type: 'contact',
              match_reason: 'field_comparison'
            };
          }
        }
      });

      return Object.keys(conflicts).length > 0 ? conflicts : null;
    } catch (error) {
      console.error('Error detecting contact conflicts:', error);
      return null;
    }
  }

  /**
   * Find existing company by name, website, or LinkedIn
   */
  private async findExistingCompany(
    companyData: Record<string, any>,
    client: any
  ): Promise<DatabaseCompany | null> {
    try {
      // Check by website first
      if (companyData.company_website) {
        const result = await client.query(
          'SELECT * FROM companies WHERE LOWER(company_website) = LOWER($1) LIMIT 1',
          [companyData.company_website]
        );
        if (result.rows.length > 0) return result.rows[0];
      }

      // Check by LinkedIn
      if (companyData.company_linkedin) {
        const result = await client.query(
          'SELECT * FROM companies WHERE LOWER(company_linkedin) = LOWER($1) LIMIT 1',
          [companyData.company_linkedin]
        );
        if (result.rows.length > 0) return result.rows[0];
      }

      // Check by name
      if (companyData.company_name) {
        const result = await client.query(
          'SELECT * FROM companies WHERE LOWER(company_name) = LOWER($1) LIMIT 1',
          [companyData.company_name]
        );
        if (result.rows.length > 0) return result.rows[0];
      }

      return null;
    } catch (error) {
      console.error('Error finding existing company:', error);
      return null;
    }
  }

  /**
   * Find existing contact by email, LinkedIn, or name+company
   */
  private async findExistingContact(
    contactData: Record<string, any>,
    client: any
  ): Promise<DatabaseContact | null> {
    try {
      // Check by email first
      if (contactData.email) {
        const result = await client.query(
          'SELECT * FROM contacts WHERE LOWER(email) = LOWER($1) LIMIT 1',
          [contactData.email]
        );
        if (result.rows.length > 0) return result.rows[0];
      }

      // Check by LinkedIn
      if (contactData.linkedin_url) {
        const result = await client.query(
          'SELECT * FROM contacts WHERE LOWER(linkedin_url) = LOWER($1) LIMIT 1',
          [contactData.linkedin_url]
        );
        if (result.rows.length > 0) return result.rows[0];
      }

      // Check by name + company
      if (contactData.full_name && contactData.company_id) {
        const result = await client.query(
          'SELECT * FROM contacts WHERE LOWER(full_name) = LOWER($1) AND company_id = $2 LIMIT 1',
          [contactData.full_name, contactData.company_id]
        );
        if (result.rows.length > 0) return result.rows[0];
      }

      return null;
    } catch (error) {
      console.error('Error finding existing contact:', error);
      return null;
    }
  }

  /**
   * Save company data with conflict handling (existing method, now internal)
   */
  private async saveCompanyWithConflicts(
    companyData: Record<string, any>,
    conflicts: ConflictData | null,
    source: string,
    client: any
  ): Promise<string | null> {
    try {
      console.log(`\n   🏢 === COMPANY DATA TRANSFORMATION ===`);
      console.log(`   📝 Input CSV Data:`, companyData);
      
      // STEP 1: Sanitize integer fields to prevent PostgreSQL conversion errors
      console.log(`   🔧 Sanitizing integer fields...`);
      this.sanitizeCompanyIntegerFields(companyData);
      
      // Validate required fields - DO NOT generate company name if missing
      if (!companyData.company_name || companyData.company_name.trim() === '') {
        console.log('   ❌ No company name provided - skipping company creation');
        return null;
      }

      // Add standard fields
      console.log(`   📋 Adding metadata fields (source, timestamps, conflicts)...`);
      companyData.source = source;
      companyData.created_at = new Date().toISOString();
      companyData.updated_at = new Date().toISOString();

      // Handle conflicts
      if (conflicts && Object.keys(conflicts).length > 0) {
        console.log(`   ⚠️  Adding conflict data: ${Object.keys(conflicts).length} conflicting fields`);
        companyData.conflicts = JSON.stringify(conflicts);
        companyData.conflict_status = 'pending';
        companyData.conflict_created_at = new Date().toISOString();
        companyData.conflict_source = source;
      } else {
        companyData.conflict_status = 'none';
      }

      // Separate valid columns from extra attributes
      const validColumns = [
        'company_name', 'company_linkedin', 'company_address', 'company_city',
        'company_state', 'company_zip', 'company_website', 'industry', 
        'company_phone', 'founded_year', 'company_country', 'capital_position',
        'source', 'created_at', 'updated_at', 'conflicts', 'conflict_status',
        'conflict_created_at', 'conflict_source'
      ];

      const insertData: Record<string, any> = {};
      const extraAttrs: Record<string, any> = {};

      // console.log(`\n   🗂️  === FIELD MAPPING ===`);
      Object.keys(companyData).forEach(key => {
        if (validColumns.includes(key) && companyData[key] !== null && companyData[key] !== '') {
          // Handle arrays - convert to PostgreSQL array format
          if (Array.isArray(companyData[key])) {
            insertData[key] = companyData[key]; // Keep as JavaScript array for pg library
            // console.log(`   ✅ ${key}: [${companyData[key].join(', ')}] → companies.${key} (as array)`);
          } else {
            insertData[key] = companyData[key];
            // console.log(`   ✅ ${key}: "${companyData[key]}" → companies.${key}`);
          }
        } else if (companyData[key] !== null && companyData[key] !== '') {
          extraAttrs[key] = companyData[key];
          // console.log(`   📦 ${key}: "${companyData[key]}" → extra_attrs.${key}`);
        } else {
          // console.log(`   ⏭️  ${key}: [empty/null] → skipped`);
        }
      });
      
      // Log the final insertData to see what we're about to save
      const fs = require('fs');
      fs.appendFileSync('upload-processor-debug.log', JSON.stringify({
        timestamp: new Date().toISOString(),
        step: 'insert_data_ready',
        insertData: insertData,
        extraAttrs: extraAttrs
      }, null, 2) + '\n---\n');

      if (Object.keys(extraAttrs).length > 0) {
        // Debug: Check if any values in extraAttrs are already stringified JSON
        Object.keys(extraAttrs).forEach(key => {
          const value = extraAttrs[key];
          if (typeof value === 'string' && value.startsWith('{') && value.endsWith('}')) {
            try {
              const parsed = JSON.parse(value);
              extraAttrs[key] = parsed; // Replace string with parsed object
              console.log(`   🔧 Parsed JSON string in extraAttrs.${key}: "${value}" → object`);
            } catch (e) {
              // Not valid JSON, keep as string
              console.log(`   📝 extraAttrs.${key} is not valid JSON: "${value}"`);
            }
          }
        });
        
        insertData.extra_attrs = extraAttrs; // Store object directly for jsonb field
        console.log(`   📦 Extra attributes stored: ${Object.keys(extraAttrs).join(', ')}`);
      }

      // First, check if company already exists by name, website, or LinkedIn
      console.log(`\n   🔍 === CHECKING FOR EXISTING COMPANY ===`);
      let existingCompany: DatabaseCompany | null = null;
      
      if (insertData.company_website) {
        console.log(`   🌐 Searching by website: ${insertData.company_website}`);
        const websiteResult = await client.query(
          'SELECT * FROM companies WHERE LOWER(company_website) = LOWER($1) LIMIT 1',
          [insertData.company_website]
        );
        if (websiteResult.rows.length > 0) {
          existingCompany = websiteResult.rows[0];
          console.log(`   ✅ Found existing company by website - ID: ${existingCompany?.company_id}`);
        }
      }

      if (!existingCompany && insertData.company_linkedin) {
        console.log(`   🔗 Searching by LinkedIn: ${insertData.company_linkedin}`);
        const linkedinResult = await client.query(
          'SELECT * FROM companies WHERE LOWER(company_linkedin) = LOWER($1) LIMIT 1',
          [insertData.company_linkedin]
        );
        if (linkedinResult.rows.length > 0) {
          existingCompany = linkedinResult.rows[0];
          console.log(`   ✅ Found existing company by LinkedIn - ID: ${existingCompany?.company_id}`);
        }
      }

      if (!existingCompany) {
        console.log(`   🏢 Searching by company name: ${insertData.company_name}`);
        const nameResult = await client.query(
          'SELECT * FROM companies WHERE LOWER(company_name) = LOWER($1) LIMIT 1',
          [insertData.company_name]
        );
        if (nameResult.rows.length > 0) {
          existingCompany = nameResult.rows[0];
          console.log(`   ✅ Found existing company by name - ID: ${existingCompany?.company_id}`);
        }
      }

      let companyId: string;
      let action: string;

      if (existingCompany) {
        // UPDATE existing company
        console.log(`\n   🔄 === UPDATING EXISTING COMPANY ===`);
        console.log(`   🎯 Updating company ID: ${existingCompany?.company_id}`);
        
        // Check for conflicts during update - improved array comparison
        const updateConflicts: ConflictData = {};

        Object.keys(insertData).forEach(field => {
          // Only check conflicts for fields that will actually be updated (exclude system fields)
          if (field !== 'created_at' && 
            field !== 'updated_at' && 
            field !== 'company_id' && 
            field !== 'processing_error_count' && 
            field !== 'source' &&
            field !== 'conflicts' &&
            field !== 'conflict_status' &&
            field !== 'conflict_created_at' && field !== 'conflict_source') {
            const existingValue = existingCompany ? existingCompany[field] : null;
            const newValue = insertData[field];
            
            // Special handling for array fields
            if (Array.isArray(newValue) && Array.isArray(existingValue)) {
              // Compare arrays by their sorted string representation
              const existingSorted = existingValue.slice().sort().join(',');
              const newSorted = newValue.slice().sort().join(',');
              if (existingSorted !== newSorted) {
                updateConflicts[field] = {
                  existing_value: existingValue,
                  new_value: newValue,
                  source: source,
                  created_at: new Date().toISOString(),
                  field_type: 'company',
                  match_reason: 'update_conflict'
                };
                console.log(`   ⚠️  Array conflict in ${field}: [${existingValue.join(', ')}] → [${newValue.join(', ')}]`);
              }
            } else if (existingValue && newValue) {
              // Special handling for LinkedIn fields - normalize URLs before comparison
              if (field === 'company_linkedin' || field === 'linkedin' || field === 'linkedin_url') {
                const existingNormalized = normalizeLinkedInUrl(existingValue.toString().trim());
                const newNormalized = normalizeLinkedInUrl(newValue.toString().trim());
                
                // Only flag conflict if normalized LinkedIn URLs are different (not empty)
                if (existingNormalized && newNormalized && existingNormalized !== newNormalized) {
                  updateConflicts[field] = {
                    existing_value: existingValue,
                    new_value: newValue,
                    source: source,
                    created_at: new Date().toISOString(),
                    field_type: 'company',
                    match_reason: 'linkedin_update_conflict'
                  };
                  console.log(`   ⚠️  LinkedIn conflict in ${field}: "${existingValue}" → "${newValue}"`);
                }
              } else if (field === 'website' || field === 'company_website' || field === 'web_site' || field === 'url') {
                // Special handling for website fields - compare domains instead of raw URLs
                const existingDomain = extractDomain(existingValue.toString().trim());
                const newDomain = extractDomain(newValue.toString().trim());
                
                // Only flag conflict if domains are different (not empty)
                if (existingDomain && newDomain && existingDomain !== newDomain) {
                  updateConflicts[field] = {
                    existing_value: existingValue,
                    new_value: newValue,
                    source: source,
                    created_at: new Date().toISOString(),
                    field_type: 'company',
                    match_reason: 'domain_update_conflict'
                  };
                  console.log(`   ⚠️  Domain conflict in ${field}: "${existingValue}" → "${newValue}"`);
                }
              } else {
                // Regular field comparison for non-website fields
                if (existingValue.toString().toLowerCase() !== newValue.toString().toLowerCase()) {
                  updateConflicts[field] = {
                    existing_value: existingValue,
                    new_value: newValue,
                    source: source,
                    created_at: new Date().toISOString(),
                    field_type: 'company',
                    match_reason: 'update_conflict'
                  };
                  console.log(`   ⚠️  Conflict in ${field}: "${existingValue}" → "${newValue}"`);
                }
              }
            }
          }
        });

        // Update only empty fields, preserve conflicts
        const updateFields: string[] = [];
        const updateValues: any[] = [];
        let valueIndex = 1;

        Object.keys(insertData).forEach(field => {
          // Exclude system fields that should not be updated from CSV data
          // Also exclude 'source' since we force update it later
          if (field !== 'created_at' && 
              field !== 'updated_at' && 
              field !== 'company_id' && 
              field !== 'processing_error_count' && 
              field !== 'conflict_status' &&
              field !== 'conflicts' &&
              field !== 'conflict_created_at' &&
              field !== 'conflict_source' &&
              field !== 'source' &&
              insertData[field] !== null && insertData[field] !== '') {
            
            // Special handling for different field types
            const integerFields = ['founded_year', 'processing_error_count'];
            
            if (Array.isArray(insertData[field])) {
              // Array fields - check if existing field is null or empty array
              updateFields.push(`${field} = CASE WHEN ${field} IS NULL OR ${field} = '{}' THEN $${valueIndex} ELSE ${field} END`);
            } else if (integerFields.includes(field)) {
              // Integer fields - only check for NULL, not empty string and make the value an integer
              updateFields.push(`${field} = CASE WHEN ${field} IS NULL THEN $${valueIndex}::integer ELSE ${field} END`);
            } else {
              // Text/other fields - check for both NULL and empty strings
              updateFields.push(`${field} = CASE WHEN ${field} IS NULL OR ${field} = '' THEN $${valueIndex} ELSE ${field} END`);
            }
            updateValues.push(insertData[field]);
            valueIndex++;
          }
        });

        // Always update timestamp, source, and conflicts
        updateFields.push(`updated_at = $${valueIndex}`);
        updateValues.push(new Date().toISOString());
        valueIndex++;
        
        // Always update source field to track which upload this data came from
        updateFields.push(`source = $${valueIndex}`);
        updateValues.push(source);
        valueIndex++;

        if (Object.keys(updateConflicts).length > 0) {
          updateFields.push(`conflicts = $${valueIndex}::jsonb`);
          updateFields.push(`conflict_status = 'pending'`);
          updateFields.push(`conflict_created_at = $${valueIndex + 1}`);
          updateFields.push(`conflict_source = $${valueIndex + 2}`);
          updateValues.push(updateConflicts); // Store object directly for jsonb field
          updateValues.push(new Date().toISOString());
          updateValues.push(source);
          valueIndex += 3;
        } else {
          // If no conflicts, set conflict_status to 'none' (only if not already set)
          updateFields.push(`conflict_status = CASE WHEN conflict_status IS NULL OR conflict_status = '' THEN 'none' ELSE conflict_status END`);
        }

        updateValues.push(existingCompany?.company_id);

        // console.log(`   💾 Update values:`, updateValues);
        
        const updateQuery = `
          UPDATE companies 
          SET ${updateFields.join(', ')}
          WHERE company_id = $${valueIndex}
          RETURNING company_id
        `;

        // console.log(`   🔍 Executing update query:`, updateQuery);
        const result = await client.query(updateQuery, updateValues);
        companyId = result.rows[0].company_id.toString();
        action = 'updated';
        
        console.log(`   ✅ Updated existing company with ${Object.keys(updateConflicts).length} conflicts`);
      } else {
        // INSERT new company
        console.log(`\n   ➕ === INSERTING NEW COMPANY ===`);
        
        const columns = Object.keys(insertData);
        const placeholders = columns.map((_, index) => `$${index + 1}`);
        const values = columns.map(col => insertData[col]);

        console.log(`   📊 Inserting ${columns.length} fields into companies table`);
        console.log(`   🏗️  Columns: [${columns.join(', ')}]`);
        console.log(`   💾 Values being inserted:`, values);

        const insertQuery = `
          INSERT INTO companies (${columns.join(', ')})
          VALUES (${placeholders.join(', ')})
          RETURNING company_id
        `;

        console.log(`   🔍 Executing query:`, insertQuery);
        const result = await client.query(insertQuery, values);
        companyId = result.rows[0].company_id.toString();
        action = 'inserted';
        
        console.log(`   ✅ Inserted new company`);
      }
      
      console.log(`   🎉 Successfully ${action} company: "${insertData.company_name}" with ID: ${companyId}${conflicts ? ' (with conflicts)' : ''}`);
      console.log(`   🏢 === COMPANY TRANSFORMATION COMPLETE ===\n`);
      
      return companyId;

    } catch (error) {
      console.error('   ❌ Error saving company with conflicts:', error);
      throw error;
    }
  }

  /**
   * Sanitize integer fields to convert empty strings to null
   */
  private sanitizeContactIntegerFields(contactData: Record<string, any>): void {
    const integerFields = ['company_id', 'processing_error_count'];
    
    integerFields.forEach(field => {
      if (contactData[field] === '' || contactData[field] === 'null' || contactData[field] === 'undefined') {
        contactData[field] = null;
        console.log(`   🔧 Sanitized ${field}: "${contactData[field]}" → null`);
      } else if (contactData[field] && typeof contactData[field] === 'string') {
        const parsed = parseInt(contactData[field], 10);
        if (!isNaN(parsed)) {
          contactData[field] = parsed;
          console.log(`   🔧 Sanitized ${field}: "${contactData[field]}" → ${parsed}`);
        } else {
          contactData[field] = null;
          console.log(`   🔧 Sanitized invalid ${field}: "${contactData[field]}" → null`);
        }
      }
    });
  }

  /**
   * Sanitize company integer fields to convert empty strings to null
   */
  private sanitizeCompanyIntegerFields(companyData: Record<string, any>): void {
    const integerFields = ['founded_year', 'processing_error_count'];
    
    integerFields.forEach(field => {
      if (companyData[field] === '' || companyData[field] === 'null' || companyData[field] === 'undefined') {
        companyData[field] = null;
        console.log(`   🔧 Sanitized ${field}: "${companyData[field]}" → null`);
      } else if (companyData[field] && typeof companyData[field] === 'string') {
        const parsed = parseInt(companyData[field], 10);
        if (!isNaN(parsed)) {
          companyData[field] = parsed;
          console.log(`   🔧 Sanitized ${field}: "${companyData[field]}" → ${parsed}`);
        } else {
          companyData[field] = null;
          console.log(`   🔧 Sanitized invalid ${field}: "${companyData[field]}" → null`);
        }
      }
    });
  }

  /**
   * Save contact data with conflict handling
   */
  private async saveContactWithConflicts(
    contactData: Record<string, any>,
    conflicts: ConflictData | null,
    source: string,
    client: any
  ): Promise<string | null> {
    const updateConflicts: ConflictData = {};
    
    try {
      console.log(`\n   👤 === CONTACT DATA TRANSFORMATION ===`);
      console.log(`   📝 Input CSV Data:`, contactData);
      
      // STEP 1: Sanitize integer fields to prevent PostgreSQL conversion errors
      console.log(`   🔧 Sanitizing integer fields...`);
      this.sanitizeContactIntegerFields(contactData);
      
      // Validate required fields
      const firstName = contactData.first_name?.trim();
      const lastName = contactData.last_name?.trim();
      const email = contactData.email?.trim();
      const linkedinUrl = contactData.linkedin_url?.trim();
      
      // console.log(`   🔍 Validating required fields:`);
      // console.log(`     • First Name: "${firstName || '[none]'}"`);
      // console.log(`     • Last Name: "${lastName || '[none]'}"`);
      // console.log(`     • Email: "${email || '[none]'}"`);
      // console.log(`     • LinkedIn: "${linkedinUrl || '[none]'}"`);
      
      if (!firstName && !lastName && !email && !linkedinUrl) {
        throw new Error('Either first name, last name, email, or LinkedIn URL is required');
      }

      // Generate full_name if not provided
      if (!contactData.full_name) {
        console.log(`   🔄 Generating full_name from available data...`);
        if (firstName || lastName) {
          contactData.full_name = `${firstName || ''} ${lastName || ''}`.trim();
          console.log(`   ✅ Generated from name fields: "${contactData.full_name}"`);
        } else if (email) {
          const emailPrefix = email.split('@')[0];
          contactData.full_name = emailPrefix.replace(/[._-]/g, ' ').trim();
          console.log(`   ✅ Generated from email prefix: "${contactData.full_name}"`);
        } else if (linkedinUrl) {
          const linkedinMatch = linkedinUrl.match(/linkedin\.com\/in\/([^\/\?]+)/);
          if (linkedinMatch) {
            contactData.full_name = linkedinMatch[1].replace(/[-_]/g, ' ').trim();
            console.log(`   ✅ Generated from LinkedIn profile: "${contactData.full_name}"`);
          } else {
            contactData.full_name = 'LinkedIn Contact';
            console.log(`   ⚠️  Using fallback name: "${contactData.full_name}"`);
          }
        } else {
          contactData.full_name = 'Unknown Contact';
          console.log(`   ⚠️  Using fallback name: "${contactData.full_name}"`);
        }
      }

      // Add standard fields
      console.log(`   📋 Adding metadata fields (source, timestamps, conflicts)...`);
      contactData.source = source;
      contactData.created_at = new Date().toISOString();
      contactData.updated_at = new Date().toISOString();

      // Handle conflicts
      if (conflicts && Object.keys(conflicts).length > 0) {
        console.log(`   ⚠️  Adding conflict data: ${Object.keys(conflicts).length} conflicting fields`);
        contactData.conflicts = JSON.stringify(conflicts);
        contactData.conflict_status = 'pending';
        contactData.conflict_created_at = new Date().toISOString();
        contactData.conflict_source = source;
      } else {
        contactData.conflict_status = 'none';
      }

      // Separate valid columns from extra attributes
      const validColumns = [
        'company_id', 'first_name', 'last_name', 'full_name', 'title', 'headline', 'seniority',
        'email', 'personal_email', 'email_status', 'linkedin_url', 'contact_city', 'contact_state',
        'contact_country', 'category', 'source', 'region', 'phone_number', 'capital_type',
        'contact_category', 'notes', 'capital_position', 'company_type',
        'created_at', 'updated_at', 'conflicts', 'conflict_status',
        'conflict_created_at', 'conflict_source'
      ];

      const insertData: Record<string, any> = {};
      const extraAttrs: Record<string, any> = {};

      // console.log(`\n   🗂️  === FIELD MAPPING ===`);
      Object.keys(contactData).forEach(key => {
        if (validColumns.includes(key) && contactData[key] !== null && contactData[key] !== '') {
          // Handle arrays - keep as JavaScript array for pg library
          if (Array.isArray(contactData[key])) {
            insertData[key] = contactData[key]; // Keep as JavaScript array for pg library
            // console.log(`   ✅ ${key}: [${contactData[key].join(', ')}] → contacts.${key} (as array)`);
          } else {
            insertData[key] = contactData[key];
            // console.log(`   ✅ ${key}: "${contactData[key]}" → contacts.${key}`);
          }
        } else if (contactData[key] !== null && contactData[key] !== '') {
          extraAttrs[key] = contactData[key];
          // console.log (`   📦 ${key}: "${contactData[key]}" → extra_attrs.${key}`);
        } else {
          // console.log(`   ⏭️  ${key}: [empty/null] → skipped`);
        }
      });

      // TEMPORARILY DISABLED: Extra attributes saving to avoid JSON parsing issues
      // if (Object.keys(extraAttrs).length > 0) {
      //   insertData.extra_attrs = extraAttrs;
      //   console.log(`   📦 Extra attributes stored: ${Object.keys(extraAttrs).join(', ')}`);
      // }

      // STEP 2: Sanitize integer fields in insertData to prevent PostgreSQL conversion errors
      console.log(`   🔧 Re-sanitizing integer fields in insertData...`);
      const integerFields = ['company_id', 'processing_error_count'];
      let sanitizedFields = 0;
      integerFields.forEach(field => {
        if (insertData.hasOwnProperty(field)) {
          const originalValue = insertData[field];
          if (originalValue === '' || originalValue === 'null' || originalValue === 'undefined') {
            insertData[field] = null;
            console.log(`   🔧 Sanitized insertData.${field}: "${originalValue}" → null`);
            sanitizedFields++;
          } else if (originalValue && typeof originalValue === 'string') {
            const parsed = parseInt(originalValue, 10);
            if (!isNaN(parsed)) {
              insertData[field] = parsed;
              console.log(`   🔧 Sanitized insertData.${field}: "${originalValue}" → ${parsed}`);
              sanitizedFields++;
            } else {
              insertData[field] = null;
              console.log(`   🔧 Sanitized invalid insertData.${field}: "${originalValue}" → null`);
              sanitizedFields++;
            }
          }
        }
      });
      console.log(`   📊 Sanitized ${sanitizedFields} integer fields in insertData`);

      // First, check if contact already exists by email or LinkedIn
      console.log(`\n   🔍 === CHECKING FOR EXISTING CONTACT ===`);
      let existingContact: DatabaseContact | null = null;
      
      if (insertData.email) {
        console.log(`   📧 Searching by email: ${insertData.email}`);
        const emailResult = await client.query(
          'SELECT * FROM contacts WHERE LOWER(email) = LOWER($1) LIMIT 1',
          [insertData.email]
        );
        if (emailResult.rows.length > 0) {
          existingContact = emailResult.rows[0];
          console.log(`   ✅ Found existing contact by email - ID: ${existingContact?.contact_id}`);
        }
      }

      if (!existingContact && insertData.linkedin_url) {
        console.log(`   🔗 Searching by LinkedIn: ${insertData.linkedin_url}`);
        const linkedinResult = await client.query(
          'SELECT * FROM contacts WHERE LOWER(linkedin_url) = LOWER($1) LIMIT 1',
          [insertData.linkedin_url]
        );
        if (linkedinResult.rows.length > 0) {
          existingContact = linkedinResult.rows[0];
          console.log(`   ✅ Found existing contact by LinkedIn - ID: ${existingContact?.contact_id}`);
        }
      }

      let contactId: string;
      let action: string;

      if (existingContact) {
        // UPDATE existing contact
        console.log(`\n   🔄 === UPDATING EXISTING CONTACT ===`);
        console.log(`   🎯 Updating contact ID: ${existingContact?.contact_id}`);
        
        // Check for conflicts during update - improved array comparison
        const updateConflicts: ConflictData = {};

        Object.keys(insertData).forEach(field => {
          // Only check conflicts for fields that will actually be updated (align with update logic)
          if (field !== 'created_at' &&
              field !== 'updated_at' &&
              field !== 'company_id' &&
              field !== 'processing_error_count' &&
              field !== 'source' &&
              field !== 'conflicts' &&
              field !== 'conflict_status' &&
              field !== 'conflict_created_at' && field !== 'conflict_source') {
            const existingValue = existingContact ? existingContact[field] : null;
            const newValue = insertData[field];
            
            // Debug the values being compared
            console.log(`   🔍 Comparing ${field}: existing=${typeof existingValue} (${JSON.stringify(existingValue)}), new=${typeof newValue} (${JSON.stringify(newValue)})`);
            
            // Special handling for array fields
            if (Array.isArray(newValue) && Array.isArray(existingValue)) {
              // Compare arrays by their sorted string representation
              const existingSorted = existingValue.slice().sort().join(',');
              const newSorted = newValue.slice().sort().join(',');
              if (existingSorted !== newSorted) {
                updateConflicts[field] = {
                  existing_value: existingValue,
                  new_value: newValue,
                  source: source,
                  created_at: new Date().toISOString(),
                  field_type: 'contact',
                  match_reason: 'update_conflict'
                };
                console.log(`   ⚠️  Array conflict in ${field}: [${existingValue.join(', ')}] → [${newValue.join(', ')}]`);
              }
            } else if (existingValue && newValue) {
              // Special handling for LinkedIn fields - normalize URLs before comparison
              if (field === 'linkedin_url' || field === 'linkedin') {
                const existingNormalized = normalizeLinkedInUrl(existingValue.toString().trim());
                const newNormalized = normalizeLinkedInUrl(newValue.toString().trim());
                
                // Only flag conflict if normalized LinkedIn URLs are different (not empty)
                if (existingNormalized && newNormalized && existingNormalized !== newNormalized) {
                  updateConflicts[field] = {
                    existing_value: existingValue,
                    new_value: newValue,
                    source: source,
                    created_at: new Date().toISOString(),
                    field_type: 'contact',
                    match_reason: 'linkedin_update_conflict'
                  };
                  console.log(`   ⚠️  LinkedIn conflict in ${field}: "${existingValue}" → "${newValue}"`);
                }
              } else if (field === 'website' || field === 'url') {
                // Special handling for website fields - compare domains instead of raw URLs
                const existingDomain = extractDomain(existingValue.toString().trim());
                const newDomain = extractDomain(newValue.toString().trim());
                
                // Only flag conflict if domains are different (not empty)
                if (existingDomain && newDomain && existingDomain !== newDomain) {
                  updateConflicts[field] = {
                    existing_value: existingValue,
                    new_value: newValue,
                    source: source,
                    created_at: new Date().toISOString(),
                    field_type: 'contact',
                    match_reason: 'domain_update_conflict'
                  };
                  console.log(`   ⚠️  Domain conflict in ${field}: "${existingValue}" → "${newValue}"`);
                }
              // TEMPORARILY DISABLED: Extra attributes conflict detection to avoid JSON parsing issues
              // } else if (field === 'extra_attrs') {
              //   // Special handling for extra_attrs - compare JSON objects
              //   // ... conflict detection logic disabled
              } else {
                // Regular field comparison for non-URL fields
                if (existingValue.toString().toLowerCase() !== newValue.toString().toLowerCase()) {
                  updateConflicts[field] = {
                    existing_value: existingValue,
                    new_value: newValue,
                    source: source,
                    created_at: new Date().toISOString(),
                    field_type: 'contact',
                    match_reason: 'update_conflict'
                  };
                  console.log(`   ⚠️  Conflict in ${field}: "${existingValue}" → "${newValue}"`);
                }
              }
            }
          }
        });

        // Update only empty fields, preserve conflicts
        const updateFields: string[] = [];
        const updateValues: any[] = [];
        let valueIndex = 1;

        Object.keys(insertData).forEach(field => {
          // Exclude system fields that should not be updated from CSV data
          if (field !== 'created_at' && 
              field !== 'updated_at' && 
              field !== 'company_id' && 
              field !== 'processing_error_count' && 
              field !== 'conflict_status' &&
              field !== 'conflicts' &&
              field !== 'conflict_created_at' &&
              field !== 'conflict_source' &&
              field !== 'source' &&
              insertData[field] !== null && insertData[field] !== '') {
            
            // Special handling for different field types
            const integerFields = ['processing_error_count']; // contacts don't have founded_year
            
            if (Array.isArray(insertData[field])) {
              // Array fields - check if existing field is null or empty array
              updateFields.push(`${field} = CASE WHEN ${field} IS NULL OR ${field} = '{}' THEN $${valueIndex} ELSE ${field} END`);
            } else if (integerFields.includes(field)) {
              // Integer fields - only check for NULL, not empty string and make the value an integer
              updateFields.push(`${field} = CASE WHEN ${field} IS NULL THEN $${valueIndex}::integer ELSE ${field} END`);
            } else {
              // Text/other fields - check for both NULL and empty strings
              updateFields.push(`${field} = CASE WHEN ${field} IS NULL OR ${field} = '' THEN $${valueIndex} ELSE ${field} END`);
            }
            updateValues.push(insertData[field]);
            valueIndex++;
          }
        });

        // Always update timestamp, source, and conflicts
        updateFields.push(`updated_at = $${valueIndex}`);
        updateValues.push(new Date().toISOString());
        valueIndex++;
        
        // Always update source field to track which upload this data came from
        updateFields.push(`source = $${valueIndex}`);
        updateValues.push(source);
        valueIndex++;

        if (Object.keys(updateConflicts).length > 0) {
          updateFields.push(`conflicts = $${valueIndex}::jsonb`);
          updateFields.push(`conflict_status = 'pending'`);
          updateFields.push(`conflict_created_at = $${valueIndex + 1}`);
          updateFields.push(`conflict_source = $${valueIndex + 2}`);
          updateValues.push(updateConflicts); // Store object directly for jsonb field
          updateValues.push(new Date().toISOString());
          updateValues.push(source);
          valueIndex += 3;
        } else {
          // If no conflicts, set conflict_status to 'none' (only if not already set)
          updateFields.push(`conflict_status = CASE WHEN conflict_status IS NULL OR conflict_status = '' THEN 'none' ELSE conflict_status END`);
        }

        updateValues.push(existingContact?.contact_id);
        
        // ENHANCED: Add final validation of all updateValues before query execution
        // console.log(`   🔍 FINAL VALIDATION - All updateValues:`, updateValues.map((val, idx) => `[${idx}]=${typeof val}:"${val}"`));
        
        // Check for any empty strings in the final array
        const emptyStringIndices = updateValues.map((val, idx) => val === '' ? idx : -1).filter(idx => idx !== -1);
        if (emptyStringIndices.length > 0) {
          // console.log(`   🚨 FOUND EMPTY STRINGS AT INDICES:`, emptyStringIndices);
          // Convert any remaining empty strings to null
          emptyStringIndices.forEach(idx => {
            // console.log(`   🔧 Converting empty string at index ${idx} to null`);
            updateValues[idx] = null;
          });
        }

        const updateQuery = `
          UPDATE contacts 
          SET ${updateFields.join(', ')}
          WHERE contact_id = $${valueIndex}
          RETURNING contact_id
        `;
        
        // Add debugging for JSON conflicts
        if (Object.keys(updateConflicts).length > 0) {
          console.log(`   🔍 Final conflicts JSON:`, JSON.stringify(updateConflicts));
          console.log(`   📊 Conflicts JSON length:`, JSON.stringify(updateConflicts).length);
          
          // Validate JSON before query execution
          try {
            const testJson = JSON.stringify(updateConflicts);
            JSON.parse(testJson); // Test parsing
            console.log(`   ✅ JSON validation passed`);
          } catch (jsonError) {
            console.error(`   ❌ JSON validation failed:`, jsonError);
            throw new Error(`Invalid JSON in conflicts: ${jsonError.message}`);
          }
        }
        
        // CRITICAL FIX: Ensure all JSON values are properly stringified
        // The issue is that some values in updateConflicts might be objects that need to be stringified
        if (Object.keys(updateConflicts).length > 0) {
          const sanitizedUpdateConflicts = {};
          Object.keys(updateConflicts).forEach(key => {
            const conflict = updateConflicts[key];
            
            // Handle existing_value - if it's an object, stringify it
            let existingValue = conflict.existing_value;
            if (typeof existingValue === 'object' && existingValue !== null) {
              existingValue = JSON.stringify(existingValue);
            } else if (typeof existingValue === 'string' && existingValue.startsWith('{') && existingValue.endsWith('}')) {
              // If it's already a JSON string, validate it but don't double-stringify
              try {
                JSON.parse(existingValue);
                // Keep as is - it's already a valid JSON string
              } catch (e) {
                // If it's not valid JSON, treat it as a regular string
                existingValue = JSON.stringify(existingValue);
              }
            }
            
            // Handle new_value - if it's an object, stringify it
            let newValue = conflict.new_value;
            if (typeof newValue === 'object' && newValue !== null) {
              newValue = JSON.stringify(newValue);
            } else if (typeof newValue === 'string' && newValue.startsWith('{') && newValue.endsWith('}')) {
              // If it's already a JSON string, validate it but don't double-stringify
              try {
                JSON.parse(newValue);
                // Keep as is - it's already a valid JSON string
              } catch (e) {
                // If it's not valid JSON, treat it as a regular string
                newValue = JSON.stringify(newValue);
              }
            }
            
            sanitizedUpdateConflicts[key] = {
              existing_value: existingValue,
              new_value: newValue,
              source: conflict.source,
              created_at: conflict.created_at,
              field_type: conflict.field_type,
              match_reason: conflict.match_reason
            };
          });
          
          // Find the conflicts parameter in the query and replace it with sanitized version
          const conflictsParamIndex = updateQuery.match(/conflicts\s*=\s*\$(\d+)/);
          if (conflictsParamIndex) {
            const paramIndex = parseInt(conflictsParamIndex[1]) - 1; // Convert to 0-based index
            console.log(`   🔍 Found conflicts parameter at index ${paramIndex} in SQL query`);
            
            if (paramIndex >= 0 && paramIndex < updateValues.length) {
              // For PostgreSQL jsonb, we need to pass the actual object, not a stringified version
              updateValues[paramIndex] = sanitizedUpdateConflicts;
              console.log(`   🔧 Sanitized conflicts object placed at parameter index ${paramIndex}`);
              
              // Validate the object can be stringified (for debugging)
              try {
                const testString = JSON.stringify(sanitizedUpdateConflicts);
                console.log(`   ✅ Conflicts object validation passed, length: ${testString.length}`);
              } catch (jsonError) {
                console.error(`   ❌ Conflicts object validation failed:`, jsonError);
                throw new Error(`Invalid conflicts object: ${jsonError.message}`);
              }
            } else {
              console.error(`   ❌ Invalid parameter index: ${paramIndex}, array length: ${updateValues.length}`);
            }
          } else {
            console.error(`   ❌ Could not find conflicts parameter in SQL query`);
          }
        }
        
        console.log(`   📝 Final SQL query:`, updateQuery);
        console.log(`   📊 Total parameters: ${updateValues.length}, Expected: ${valueIndex}`);
        
        // Find the actual conflicts parameter index
        const conflictsParamIndex = updateQuery.match(/conflicts\s*=\s*\$(\d+)/);
        if (conflictsParamIndex) {
          const actualIndex = parseInt(conflictsParamIndex[1]) - 1;
          console.log(`   🔍 updateValues[${actualIndex}] (conflicts):`, updateValues[actualIndex]);
          console.log(`   🔍 updateValues[${actualIndex}] type:`, typeof updateValues[actualIndex]);
        }

        const result = await client.query(updateQuery, updateValues);
        contactId = result.rows[0].contact_id.toString();
        action = 'updated';
        
        console.log(`   ✅ Updated existing contact with ${Object.keys(updateConflicts).length} conflicts`);
      } else {
        // INSERT new contact
        console.log(`\n   ➕ === INSERTING NEW CONTACT ===`);
        
        const columns = Object.keys(insertData);
        const placeholders = columns.map((_, index) => `$${index + 1}`);
        const values = columns.map(col => insertData[col]);

        console.log(`   📊 Inserting ${columns.length} fields into contacts table`);
        console.log(`   🏗️  Columns: [${columns.join(', ')}]`);

        const insertQuery = `
          INSERT INTO contacts (${columns.join(', ')})
          VALUES (${placeholders.join(', ')})
          RETURNING contact_id
        `;

        const result = await client.query(insertQuery, values);
        contactId = result.rows[0].contact_id.toString();
        action = 'inserted';
        
        console.log(`   ✅ Inserted new contact`);
      }
      
      console.log(`   🎉 Successfully ${action} contact: "${insertData.full_name}" with ID: ${contactId}${conflicts ? ' (with conflicts)' : ''}`);
      console.log(`   👤 === CONTACT TRANSFORMATION COMPLETE ===\n`);
      
      return contactId;

    } catch (error) {
      console.error('   ❌ Error saving contact with conflicts:', error);
      
      // Additional debugging for JSON-related errors
      if (error.message && error.message.includes('json')) {
        console.error('   🔍 JSON Error Details:');
        console.error('   📋 updateConflicts:', updateConflicts);
        console.error('   📊 updateConflicts type:', typeof updateConflicts);
        console.error('   📋 updateConflicts keys:', Object.keys(updateConflicts || {}));
        
        if (updateConflicts) {
          try {
            const testJson = JSON.stringify(updateConflicts);
            console.error('   ✅ JSON.stringify test passed, length:', testJson.length);
          } catch (jsonError) {
            console.error('   ❌ JSON.stringify test failed:', jsonError);
          }
        }
      }
      
      throw error;
    }
  }

  /**
   * Process a single row - orchestrates the flow following SOLID principles
   */
  private async processRow(
    rowData: Record<string, any>, 
    structuredHeaderMappings: Record<string, string>, 
    uploadId: string, 
    rowIndex: number
  ): Promise<{ 
    success: boolean; 
    error?: string; 
    companiesCreated?: number;
    contactsCreated?: number;
    investmentCriteriaCreated?: number;
    hasConflicts?: boolean;
  }> {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');

      // Validate inputs
      if (!rowData || typeof rowData !== 'object') {
        return { success: false, error: 'Invalid row data provided' };
      }

      if (!structuredHeaderMappings || typeof structuredHeaderMappings !== 'object') {
        return { success: false, error: 'Invalid header mappings provided' };
      }

      const source = `Upload ${uploadId}`;

      // STEP 1: Use InvestmentCriteriaProcessor to get properly separated data
      console.log(`\n   🔄 === PROCESSING DATA WITH ENHANCED INVESTMENT CRITERIA PROCESSOR ===`);
      const rawHeaderMappings = await this.getRawHeaderMappingsForUpload(parseInt(uploadId));
      if (!rawHeaderMappings) {
        return { success: false, error: 'Raw header mappings not found' };
      }

      // Enhanced central mapping retrieval with additional equity/debt categorization
      const centralMappings = await this.getEnhancedCentralMappings();
      console.log(`   🗺️  Enhanced central mappings loaded: ${Object.keys(centralMappings.capitalPositions).length} capital positions, ${Object.keys(centralMappings.propertyTypes).length} property types`);
      
      const processingInput: ProcessingInput = {
        csvRow: rowData,
        rawHeaderMappings: rawHeaderMappings,
        centralMappings: centralMappings
      };

      const processingResult = await InvestmentCriteriaProcessor.processEnhanced(processingInput);
      console.log(`   📊 Enhanced processing result: ${processingResult.errors.length} errors, ${processingResult.investmentCriteriaData.length} criteria records`);

      if (processingResult.errors.length > 0) {
        console.log(`   ⚠️  Processing errors:`, processingResult.errors);
        return { success: false, error: processingResult.errors.join('; ') };
      }

      // STEP 1.5: Extract company name from website if missing & handle capital position properly
      console.log(`   🔍 Checking company data:`, {
        hasCompanyData: !!processingResult.companyData,
        companyName: processingResult.companyData?.company_name,
        companyWebsite: processingResult.companyData?.company_website,
        companyCapitalPosition: processingResult.companyData?.capital_position
      });
      
      // Write debug info to file
      const fs = require('fs');
      fs.appendFileSync('upload-processor-debug.log', JSON.stringify({
        timestamp: new Date().toISOString(),
        step: 'enhanced_data_check',
        hasCompanyData: !!processingResult.companyData,
        companyData: processingResult.companyData,
        contactData: processingResult.contactData,
        investmentCriteriaCount: processingResult.investmentCriteriaData.length
      }, null, 2) + '\n---\n');
      
      // Handle capital position for both company and contact data - CRITICAL FIX from memory
      console.log(`   💼 === HANDLING CAPITAL POSITION DATA ===`);
      const hasCompanyCapitalPosition = processingResult.companyData?.capital_position && Array.isArray(processingResult.companyData.capital_position) && processingResult.companyData.capital_position.length > 0;
      const hasContactCapitalPosition = processingResult.contactData?.capital_position && Array.isArray(processingResult.contactData.capital_position) && processingResult.contactData.capital_position.length > 0;
      
      // Extract capital position from either company or contact data for IC processing
      let capitalPositionForIC = [];
      if (hasCompanyCapitalPosition) {
        capitalPositionForIC = processingResult.companyData.capital_position;
        console.log(`   🏢 Using company capital position: ${capitalPositionForIC.join(', ')}`);
      } else if (hasContactCapitalPosition) {
        capitalPositionForIC = processingResult.contactData.capital_position;
        console.log(`   👤 Using contact capital position: ${capitalPositionForIC.join(', ')}`);
      }
      
      // Ensure investment criteria data includes capital position
      if (capitalPositionForIC.length > 0 && processingResult.investmentCriteriaData.length > 0) {
        processingResult.investmentCriteriaData.forEach(criteria => {
          if (!criteria.capital_position || criteria.capital_position.length === 0) {
            criteria.capital_position = capitalPositionForIC;
            console.log(`   🔧 Added capital position to investment criteria: ${capitalPositionForIC.join(', ')}`);
          }
        });
      }
      
      if (processingResult.companyData && processingResult.companyData.company_website) {
        if (!processingResult.companyData.company_name || processingResult.companyData.company_name.trim() === '') {
          console.log(`   🔧 Company name is missing, extracting from website...`);
          const extractedName = this.extractCompanyNameFromWebsite(processingResult.companyData.company_website);
          processingResult.companyData.company_name = extractedName;
          console.log(`   🌐 Extracted company name from website: "${extractedName}" from "${processingResult.companyData.company_website}"`);
          
          // Log the updated company data
          fs.appendFileSync('upload-processor-debug.log', JSON.stringify({
            timestamp: new Date().toISOString(),
            step: 'company_name_extraction',
            website: processingResult.companyData.company_website,
            extractedName: extractedName,
            updatedCompanyData: processingResult.companyData
          }, null, 2) + '\n---\n');
        } else {
          console.log(`   ✅ Company name already exists: "${processingResult.companyData.company_name}"`);
        }
      } else {
        console.log(`   ⚠️  Missing company data or website for extraction`);
      }

      // STEP 2: Save company data (using savepoint for error isolation)
      let companiesCreated = 0;
      let companyId: string | null = null;
      let hasConflicts = false;
      let companyCreationFailed = false;

      if (processingResult.companyData && Object.keys(processingResult.companyData).length > 0) {
        console.log(`\n   🚀 Starting company data save operation...`);
        try {
          await client.query('SAVEPOINT sp_company');
          console.log(`   🏢 Calling saveCompanyData with:`, {
            companyDataKeys: Object.keys(processingResult.companyData),
            companyName: processingResult.companyData.company_name,
            capitalPosition: processingResult.companyData.capital_position
          });
          
          const companyResult = await this.saveCompanyData(processingResult.companyData, source, client);
          
          console.log(`   📊 Company save result:`, {
            success: companyResult.success,
            id: companyResult.id,
            hasConflicts: companyResult.hasConflicts,
            error: companyResult.error
          });
          
          // Log to file for debugging
          const fs = require('fs');
          fs.appendFileSync('upload-processor-debug.log', JSON.stringify({
            timestamp: new Date().toISOString(),
            step: 'company_save_result',
            companyResult: companyResult
          }, null, 2) + '\n---\n');
          
          if (companyResult.success && companyResult.id) {
            companyId = companyResult.id;
            companiesCreated = 1;
            hasConflicts = hasConflicts || (companyResult.hasConflicts ?? false);
            console.log(`   ✅ Company save completed successfully - ID: ${companyId}`);
            await client.query('RELEASE SAVEPOINT sp_company');
          } else {
            companyCreationFailed = true;
            console.log(`   ⚠️  Company save failed: ${companyResult.error} - Rolling back company savepoint...`);
            await client.query('ROLLBACK TO SAVEPOINT sp_company');
          }
        } catch (error) {
          companyCreationFailed = true;
          console.log(`   ⚠️  Company save error: ${error} - Rolling back company savepoint...`);
          await client.query('ROLLBACK TO SAVEPOINT sp_company');
          
          // Log error to file
          const fs = require('fs');
          fs.appendFileSync('upload-processor-debug.log', JSON.stringify({
            timestamp: new Date().toISOString(),
            step: 'company_save_error',
            error: error instanceof Error ? error.message : String(error)
          }, null, 2) + '\n---\n');
        }
      }

      // STEP 3: Save contact data (using savepoint for error isolation)
      let contactsCreated = 0;
      let contactId: string | null = null;
      let contactCreationFailed = false;

      if (processingResult.contactData && Object.keys(processingResult.contactData).length > 0) {
        console.log(`\n   🚀 Starting contact data save operation...`);
        
        // Only set company_id if company was successfully created
        if (companyId) {
          processingResult.contactData.company_id = parseInt(companyId);
        }
        
        try {
          await client.query('SAVEPOINT sp_contact');
          const contactResult = await this.saveContactData(processingResult.contactData, source, client);
          if (contactResult.success && contactResult.id) {
            contactId = contactResult.id;
            contactsCreated = 1;
            hasConflicts = hasConflicts || (contactResult.hasConflicts ?? false);
            console.log(`   ✅ Contact save completed successfully - ID: ${contactId}`);
            await client.query('RELEASE SAVEPOINT sp_contact');
          } else {
            contactCreationFailed = true;
            console.log(`   ⚠️  Contact save failed: ${contactResult.error} - Rolling back contact savepoint...`);
            await client.query('ROLLBACK TO SAVEPOINT sp_contact');
          }
        } catch (error) {
          contactCreationFailed = true;
          console.log(`   ⚠️  Contact save error: ${error} - Rolling back contact savepoint...`);
          await client.query('ROLLBACK TO SAVEPOINT sp_contact');
        }
      }

      // STEP 4: Save investment criteria data with enhanced error handling and processing continuity
      let investmentCriteriaCreated = 0;
      const hasCapitalPosition = capitalPositionForIC.length > 0 || 
        processingResult.investmentCriteriaData.some(criteria => 
          criteria.capital_position && Array.isArray(criteria.capital_position) && criteria.capital_position.length > 0
        );

      const atLeastOneEntityCreated = companyId || contactId;

      console.log(`\n   💼 === ENHANCED INVESTMENT CRITERIA PROCESSING ===`);
      console.log(`   📊 Assessment: ${processingResult.investmentCriteriaData.length} criteria records, hasCapitalPosition: ${hasCapitalPosition}, entitiesCreated: ${atLeastOneEntityCreated}`);

      if (processingResult.investmentCriteriaData.length > 0) {
        if (!hasCapitalPosition) {
          console.log(`   ⚠️  Warning: No capital position found, but will attempt to process investment criteria anyway`);
        }
        
        if (!atLeastOneEntityCreated) {
          console.log(`   ⚠️  Warning: No entities created, but investment criteria processing will continue for debugging`);
        }

        console.log(`   💾 Processing ${processingResult.investmentCriteriaData.length} investment criteria records...`);
        
        // Enhanced processing: Continue even with warnings
        if (companyId) {
          console.log(`   🏢 === SAVING INVESTMENT CRITERIA FOR COMPANY ===`);
          console.log(`   🏢 Company ID: ${companyId}`);
          const companyName = processingResult.companyData?.company_name || 'Unknown Company';
          
          for (const [index, criteriaData] of processingResult.investmentCriteriaData.entries()) {
            try {
              console.log(`   📋 Processing criteria record ${index + 1}/${processingResult.investmentCriteriaData.length} for Company...`);
              
              const insertData: any = {
                ...criteriaData,
                entity_type: 'Company',
                entity_id: companyId,
                entity_name: companyName,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                is_active: true
              };

              // Check for existing investment criteria
              const existingCriteria = await this.findExistingInvestmentCriteria(insertData, client);
              
              if (existingCriteria) {
                console.log(`   ⚠️  Skipping duplicate investment criteria for Company "${companyName}" (existing ID: ${existingCriteria.criteria_id})`);
                continue;
              }

              const savedId = await this.saveInvestmentCriteriaToTable(insertData, client);
              if (savedId) {
                investmentCriteriaCreated++;
                console.log(`   ✅ Saved investment criteria record ${investmentCriteriaCreated} for Company "${companyName}" with ID: ${savedId}`);
              } else {
                console.log(`   ⚠️  Investment criteria save returned null for Company - continuing anyway`);
              }
            } catch (error) {
              console.error(`   ❌ Error saving investment criteria record ${index + 1} for Company (continuing processing):`, error);
              // Continue processing even if one record fails
            }
          }
        }

        // Enhanced contact processing: Continue even if company processing had issues
        if (contactId) {
          console.log(`   👤 === SAVING INVESTMENT CRITERIA FOR CONTACT ===`);
          console.log(`   👤 Contact ID: ${contactId}`);
          const firstName = processingResult.contactData?.first_name || '';
          const lastName = processingResult.contactData?.last_name || '';
          const contactName = processingResult.contactData?.full_name || `${firstName} ${lastName}`.trim() || 'Unknown Contact';
          
          for (const [index, criteriaData] of processingResult.investmentCriteriaData.entries()) {
            try {
              console.log(`   📋 Processing criteria record ${index + 1}/${processingResult.investmentCriteriaData.length} for Contact...`);
              
              const insertData: any = {
                ...criteriaData,
                entity_type: 'Contact',
                entity_id: contactId,
                entity_name: contactName,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString(),
                is_active: true
              };

              // Check for existing investment criteria
              const existingCriteria = await this.findExistingInvestmentCriteria(insertData, client);
              
              if (existingCriteria) {
                console.log(`   ⚠️  Skipping duplicate investment criteria for Contact "${contactName}" (existing ID: ${existingCriteria.criteria_id})`);
                continue;
              }

              const savedId = await this.saveInvestmentCriteriaToTable(insertData, client);
              if (savedId) {
                investmentCriteriaCreated++;
                console.log(`   ✅ Saved investment criteria record ${investmentCriteriaCreated} for Contact "${contactName}" with ID: ${savedId}`);
              } else {
                console.log(`   ⚠️  Investment criteria save returned null for Contact - continuing anyway`);
              }
            } catch (error) {
              console.error(`   ❌ Error saving investment criteria record ${index + 1} for Contact (continuing processing):`, error);
              // Continue processing even if one record fails
            }
          }
        }

        console.log(`   📊 === INVESTMENT CRITERIA PROCESSING COMPLETE ===`);
        console.log(`   ✅ Successfully saved ${investmentCriteriaCreated} investment criteria records`);
        console.log(`   📈 Success rate: ${processingResult.investmentCriteriaData.length > 0 ? ((investmentCriteriaCreated / processingResult.investmentCriteriaData.length) * 100).toFixed(1) : 0}%`);
      } else {
        console.log(`   ⚠️  No investment criteria data found to process`);
      }

      await client.query('COMMIT');

      return { 
        success: true, 
        companiesCreated,
        contactsCreated,
        investmentCriteriaCreated,
        hasConflicts
      };

    } catch (error) {
      await client.query('ROLLBACK');
      console.error(`Error processing row ${rowIndex}:`, error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    } finally {
      client.release();
    }
  }

  /**
   * Extract company name from website URL
   */
  private extractCompanyNameFromWebsite(website: string): string {
    try {
      if (!website || typeof website !== 'string') {
        return 'Unknown Company';
      }

      // Clean the URL and extract domain
      let url = website.trim();
      
      // Add protocol if missing
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url;
      }

      const domain = new URL(url).hostname;
      
      // Remove common prefixes
      let companyName = domain.replace(/^www\./, '');
      
      // Remove top-level domain
      companyName = companyName.split('.')[0];
      
      // Capitalize first letter and clean up
      companyName = companyName.charAt(0).toUpperCase() + companyName.slice(1).toLowerCase();
      
      // Handle common patterns like camelCase or hyphens
      companyName = companyName
        .replace(/([a-z])([A-Z])/g, '$1 $2') // camelCase to spaces
        .replace(/[-_]/g, ' ') // hyphens/underscores to spaces
        .replace(/\s+/g, ' ') // multiple spaces to single
        .trim();

      return companyName || 'Unknown Company';
    } catch (error) {
      console.warn(`Failed to extract company name from website: ${website}`, error);
      return 'Unknown Company';
    }
  }

  /**
   * Fetch header mappings for a specific upload from upload_logs table
   * Handles the actual stored format: { companies: {dbField: [csvHeader]}, contacts: {...}, investment_criteria_central: {...}, investment_criteria_debt: {...}, investment_criteria_equity: {...} }
   */
  private async getHeaderMappingsForUpload(uploadId: number): Promise<Record<string, string> | null> {
    const client = await pool.connect();
    
    try {
      const result = await client.query(`
        SELECT header_mappings
        FROM upload_logs 
        WHERE upload_id = $1
      `, [uploadId]);

      if (result.rows.length === 0) {
        console.error(`No upload found with ID: ${uploadId}`);
        return null;
      }

      const rawHeaderMappings = result.rows[0].header_mappings;
      
      if (!rawHeaderMappings || typeof rawHeaderMappings !== 'object') {
        console.error(`Invalid header_mappings for upload ${uploadId}:`, rawHeaderMappings);
        return null;
      }

      const headerMappings: Record<string, string> = {};
      
      // Process actual stored format: { companies: {dbField: [csvHeader]}, contacts: {...}, investment_criteria_central: {...}, investment_criteria_debt: {...}, investment_criteria_equity: {...} }
      const tables = ['companies', 'contacts', 'investment_criteria_central', 'investment_criteria_debt', 'investment_criteria_equity'];
      
      tables.forEach(tableKey => {
        const tableMappings = rawHeaderMappings[tableKey] || {};
        for (const [dbField, csvHeaders] of Object.entries(tableMappings)) {
          if (Array.isArray(csvHeaders) && csvHeaders.length > 0) {
            // Map each csvHeader -> dbField (handle multiple CSV headers mapping to same DB field)
            csvHeaders.forEach((csvHeader: string) => {
              if (csvHeader && csvHeader.trim()) {
                headerMappings[csvHeader.trim()] = dbField;
              }
            });
          } else if (typeof csvHeaders === 'string' && csvHeaders.trim()) {
            // Map csvHeader -> dbField
            headerMappings[csvHeaders.trim()] = dbField;
          }
        }
      });

      console.log(`[DEBUG] Converted stored mappings for upload ${uploadId}:`, Object.keys(headerMappings).length, 'mappings');
      console.log(`[DEBUG] Sample header mappings (csvHeader -> dbField):`, Object.fromEntries(Object.entries(headerMappings).slice(0, 10)));
      
      return headerMappings;

    } catch (error) {
      console.error('Error fetching header mappings:', error);
      return null;
    } finally {
      client.release();
    }
  }

  /**
   * Fetch raw header mappings for a specific upload from upload_logs table
   * Returns the original format: { companies: {dbField: [csvHeader]}, contacts: {...}, investment_criteria_central: {...}, investment_criteria_debt: {...}, investment_criteria_equity: {...} }
   */
  private async getRawHeaderMappingsForUpload(uploadId: number): Promise<{
    companies: Record<string, string[]>;
    contacts: Record<string, string[]>;
    investment_criteria_central: Record<string, string[]>;
    investment_criteria_debt: Record<string, string[]>;
    investment_criteria_equity: Record<string, string[]>;
  } | null> {
    const client = await pool.connect();
    
    try {
      const result = await client.query(`
        SELECT header_mappings
        FROM upload_logs 
        WHERE upload_id = $1
      `, [uploadId]);

      if (result.rows.length === 0) {
        console.error(`No upload found with ID: ${uploadId}`);
        return null;
      }

      const rawHeaderMappings = result.rows[0].header_mappings;
      
      if (!rawHeaderMappings || typeof rawHeaderMappings !== 'object') {
        console.error(`Invalid header_mappings for upload ${uploadId}:`, rawHeaderMappings);
        return null;
      }

      // Return the raw format as stored in the database
      return {
        companies: rawHeaderMappings.companies || {},
        contacts: rawHeaderMappings.contacts || {},
        investment_criteria_central: rawHeaderMappings.investment_criteria_central || {},
        investment_criteria_debt: rawHeaderMappings.investment_criteria_debt || {},
        investment_criteria_equity: rawHeaderMappings.investment_criteria_equity || {}
      };

    } catch (error) {
      console.error('Error fetching raw header mappings:', error);
      return null;
    } finally {
      client.release();
    }
  }

  /**
   * Reconstruct row data from key1-key50 columns using headers_map
   */
  private reconstructRowData(dataRow: any): Record<string, any> {
    const reconstructed: Record<string, any> = {};
    const headersMap = dataRow.headers_map || {};

    // Iterate through key1-key50 and map back to original headers
    for (let i = 1; i <= 50; i++) {
      const keyName = `key${i}`;
      const originalHeader = headersMap[keyName];
      const value = dataRow[keyName];

      if (originalHeader && value !== null && value !== undefined && value !== '') {
        reconstructed[originalHeader] = value;
      }
    }

    return reconstructed;
  }
    /**
   * Parses delimited values (supports both '/' and ',' delimiters)
   */
    private parseDelimitedValues(value: string | string[]): string[] {
      if (!value) return [];
      
      // If it's already an array, return it as-is
      if (Array.isArray(value)) {
        return value.filter(v => v && v.trim().length > 0);
      }
      
      // Split by both '/' and ',' and clean up
      const values = value.split(/[\/,]/)
        .map(v => v.trim())
        .filter(v => v.length > 0);
      
      return values;
    }

  /**
   * Check if investment criteria with same entity_id + deal_size + property_type + loan_type + capital_position already exists
   */
  private async findExistingInvestmentCriteria(
    criteriaData: Record<string, any>,
    client: any
  ): Promise<any | null> {
    try {
      // Build conditions for duplicate detection
      const conditions: string[] = [];
      const values: any[] = [];
      let paramIndex = 1;

      // Entity ID is required
      if (!criteriaData.entity_id) {
        return null;
      }
      conditions.push(`entity_id = $${paramIndex++}`);
      values.push(criteriaData.entity_id);

      // Check deal size (minimum and maximum)
      if (criteriaData.minimum_deal_size !== undefined && criteriaData.minimum_deal_size !== null) {
        conditions.push(`minimum_deal_size = $${paramIndex++}`);
        values.push(criteriaData.minimum_deal_size);
      }
      if (criteriaData.maximum_deal_size !== undefined && criteriaData.maximum_deal_size !== null) {
        conditions.push(`maximum_deal_size = $${paramIndex++}`);
        values.push(criteriaData.maximum_deal_size);
      }

      // Check property types (array field)
      if (criteriaData.property_types && Array.isArray(criteriaData.property_types) && criteriaData.property_types.length > 0) {
        conditions.push(`property_types = $${paramIndex++}`);
        values.push(criteriaData.property_types);
      }

      // Check loan type (array field)
      if (criteriaData.loan_type && Array.isArray(criteriaData.loan_type) && criteriaData.loan_type.length > 0) {
        conditions.push(`loan_type = $${paramIndex++}`);
        values.push(criteriaData.loan_type);
      }

      // Check capital position (array field)
      if (criteriaData.capital_position && Array.isArray(criteriaData.capital_position) && criteriaData.capital_position.length > 0) {
        conditions.push(`capital_position = $${paramIndex++}`);
        values.push(criteriaData.capital_position);
      }

      // Need at least entity_id and one other field for meaningful duplicate detection
      if (conditions.length < 2) {
        return null;
      }

      // Check all three investment criteria tables for duplicates
      const tables = ['investment_criteria_central', 'investment_criteria_debt', 'investment_criteria_equity'];
      
      for (const table of tables) {
        const query = `
          SELECT criteria_id, entity_id, entity_name, minimum_deal_size, maximum_deal_size, 
                 property_types, loan_type, capital_position, created_at
          FROM ${table} 
          WHERE ${conditions.join(' AND ')} 
          AND is_active = true
          LIMIT 1
        `;

        console.log(`   🔍 Checking for existing investment criteria in ${table} with query: ${query}`);
        console.log(`   📊 Values: ${JSON.stringify(values)}`);

        const result = await client.query(query, values);
        
        if (result.rows.length > 0) {
          console.log(`   ⚠️  Found existing investment criteria in ${table}: ${result.rows[0].criteria_id}`);
          return result.rows[0];
        }
      }

      return null;
    } catch (error) {
      console.error('   ❌ Error checking existing investment criteria:', error);
      return null;
    }
  }

  /**
   * Get enhanced central mappings that include additional categorization for equity/debt
   */
  private async getEnhancedCentralMappings(): Promise<CentralMappings> {
    try {
      // Get base central mappings
      const baseMappings = await InvestmentCriteriaProcessor.fetchCentralMappingsFromDatabase();
      
      // Enhance with additional equity/debt categorization
      console.log(`   🔧 Enhancing central mappings with equity/debt categorization...`);
      
      // Add enhanced categorization logic here if needed
      // This could query additional mapping tables or enhance existing mappings
      
      return baseMappings;
    } catch (error) {
      console.warn(`   ⚠️  Failed to get enhanced central mappings, falling back to basic mappings:`, error);
      return await InvestmentCriteriaProcessor.fetchCentralMappingsFromDatabase();
    }
  }

  /**
   * Classify capital position as equity vs debt for enhanced processing
   */
  private classifyCapitalPosition(capitalPositions: string[]): {
    isEquity: boolean;
    isDebt: boolean;
    category: string;
    subcategory?: string;
  } {
    const debtTerms = ['debt', 'loan', 'bridge', 'construction', 'senior', 'junior', 'subordinate', 'mezzanine'];
    const equityTerms = ['equity', 'common', 'preferred', 'investment', 'capital'];
    
    let isEquity = false;
    let isDebt = false;
    let category = 'Mixed';
    let subcategory: string | undefined;
    
    for (const position of capitalPositions) {
      const lowerPosition = position.toLowerCase();
      
      // Check for debt indicators
      if (debtTerms.some(term => lowerPosition.includes(term))) {
        isDebt = true;
        if (lowerPosition.includes('senior')) {
          category = 'Senior Debt';
          subcategory = 'Senior';
        } else if (lowerPosition.includes('mezzanine')) {
          category = 'Mezzanine Debt';
          subcategory = 'Mezzanine';
        } else if (lowerPosition.includes('junior') || lowerPosition.includes('subordinate')) {
          category = 'Junior Debt';
          subcategory = 'Junior';
        } else {
          category = 'Debt';
        }
      }
      
      // Check for equity indicators
      if (equityTerms.some(term => lowerPosition.includes(term))) {
        isEquity = true;
        if (lowerPosition.includes('preferred')) {
          category = 'Preferred Equity';
          subcategory = 'Preferred';
        } else if (lowerPosition.includes('common')) {
          category = 'Common Equity';
          subcategory = 'Common';
        } else {
          category = 'Equity';
        }
      }
    }
    
    // If both equity and debt indicators are found
    if (isEquity && isDebt) {
      category = 'Hybrid';
    }
    
    console.log(`   🏷️  Capital position classification: ${capitalPositions.join(', ')} → ${category}${subcategory ? ` (${subcategory})` : ''}`);
    
    return { isEquity, isDebt, category, subcategory };
  }

  /**
   * Save investment criteria data to appropriate table based on classification
   */
  private async saveInvestmentCriteriaToTable(
    criteriaData: Record<string, any>,
    client: any
  ): Promise<string | null> {
    try {
      console.log(`   💼 Saving investment criteria to appropriate table...`);
      
      // Enhanced capital position classification
      if (criteriaData.capital_position && Array.isArray(criteriaData.capital_position)) {
        const classification = this.classifyCapitalPosition(criteriaData.capital_position);
        
        // Add classification metadata to the criteria data
        criteriaData.capital_category = classification.category;
        criteriaData.capital_subcategory = classification.subcategory;
        criteriaData.is_equity_focused = classification.isEquity;
        criteriaData.is_debt_focused = classification.isDebt;
        
        console.log(`   📊 Enhanced classification: Category=${classification.category}, Equity=${classification.isEquity}, Debt=${classification.isDebt}`);
      }
      
      // Process all the new field mappings and calculations
      this.processRecourseArray(criteriaData);
      this.processTearSheetData(criteriaData, criteriaData); // Pass the same data as both args
      
      // Determine which table to use based on classification
      let targetTable = 'investment_criteria_central'; // default
      if (criteriaData.is_debt_focused && !criteriaData.is_equity_focused) {
        targetTable = 'investment_criteria_debt';
      } else if (criteriaData.is_equity_focused && !criteriaData.is_debt_focused) {
        targetTable = 'investment_criteria_equity';
      }
      
      console.log(`   🎯 Target table: ${targetTable}`);
      
      // Valid columns for investment criteria tables (updated with new fields)
      const validColumns = [
        'entity_type', 'entity_id', 'entity_name', 'target_return', 'property_types', 'property_sub_categories',
        'strategies', 'minimum_deal_size', 'maximum_deal_size', 'min_hold_period', 'max_hold_period',
        'financial_products', 'historical_irr', 'historical_em', 'country', 'region', 'state', 'city',
        'loan_program', 'loan_type', 'capital_source', 'structured_loan_tranche', 'min_loan_term',
        'max_loan_term', 'interest_rate', 'interest_rate_sofr', 'interest_rate_wsj', 'interest_rate_prime',
        'loan_to_value_max', 'loan_to_cost_max', 'loan_origination_fee_max', 'loan_exit_fee_max',
        'min_loan_dscr', 'max_loan_dscr', 'recourse_loan', 'extra_fields', 'created_at', 'updated_at',
        'created_by', 'updated_by', 'is_active', 'capital_position', 'loan_type_normalized',
        'loan_to_value_min', 'loan_to_cost_min', 'loan_origination_fee_min', 'loan_exit_fee_min',
        'closing_time_weeks', 'min_closing_time_weeks', 'max_closing_time_weeks', 
        'interest_rate_libor', 'interest_rate_5yt', 'interest_rate_10yt',
        'loan_term_string', 'location_focus', 'notes', 'tear_sheet'
      ];

      const insertData: Record<string, any> = {};
      const extraFields: Record<string, any> = {};
      const arrayFields = ['property_types', 'property_sub_categories', 'strategies', 'country', 'region', 'state', 'city', 'loan_program', 'loan_type', 'structured_loan_tranche', 'recourse_loan', 'capital_position', 'loan_type_normalized', 'location_focus'];
      

      // The data is already in flat database format, so we can use it directly
      Object.keys(criteriaData).forEach(key => {
        if (criteriaData[key] !== null && criteriaData[key] !== undefined && criteriaData[key] !== '') {
          if (arrayFields.includes(key)) {
            // Handle array fields - parse delimited values if it's a string, or use as-is if already an array
            if (Array.isArray(criteriaData[key])) {
              insertData[key] = criteriaData[key];
            } else {
              insertData[key] = this.parseDelimitedValues(criteriaData[key]);
            }
            // console.log(`   🔢 Array field ${key}: ${JSON.stringify(insertData[key])}`);
          } else if (validColumns.includes(key)) {
            insertData[key] = criteriaData[key];
            // console.log(`   ✅ ${key}: ${JSON.stringify(criteriaData[key])}`);
          } else {
            extraFields[key] = criteriaData[key];
            // console.log(`   📦 Extra field ${key}: ${JSON.stringify(criteriaData[key])}`);
          }
        }
      });

      // Store extra fields as JSONB
      if (Object.keys(extraFields).length > 0) {
        insertData.extra_fields = JSON.stringify(extraFields);
        // console.log(`   📦 Stored ${Object.keys(extraFields).length} extra fields`);
      }

      if (Object.keys(insertData).length === 0) {
        // console.log(`   ⚠️  No valid investment criteria data to save`);
        return null;
      }

      // Apply numeric field sanitization
      this.sanitizeNumericFields(insertData);

      const columns = Object.keys(insertData);
      const placeholders = columns.map((_, index) => `$${index + 1}`);
      const values = columns.map(col => insertData[col]);

      console.log(`   📋 Criteria data:`, JSON.stringify(insertData, null, 2));
      // console.log(`   📊 Inserting ${columns.length} fields: [${columns.join(', ')}]`);

      const insertQuery = `
        INSERT INTO ${targetTable} (${columns.join(', ')})
        VALUES (${placeholders.join(', ')})
        RETURNING criteria_id
      `;

      const result = await client.query(insertQuery, values);
      const criteriaId = result.rows[0].criteria_id;
      
      console.log(`   ✅ Saved investment criteria to ${targetTable} with ID: ${criteriaId}`);
      return criteriaId.toString();

    } catch (error) {
      console.error('   ❌ Error saving investment criteria:', error);
      throw error;
    }
  }

  /**
   * Sanitize numeric fields to ensure they're compatible with database types
   */
  private sanitizeNumericFields(criteriaData: Record<string, any>): void {
    // Fields with numeric(10,4) precision for percentages and rates
    const percentageFields = [
      'target_return', 'historical_irr', 'historical_em',
      'interest_rate', 'interest_rate_sofr', 'interest_rate_wsj', 
      'interest_rate_prime', 'interest_rate_libor', 'interest_rate_5yt', 'interest_rate_10yt',
      'loan_to_value_max', 'loan_to_value_min', 'loan_to_cost_max', 'loan_to_cost_min',
      'loan_origination_fee_max', 'loan_origination_fee_min', 
      'loan_exit_fee_max', 'loan_exit_fee_min',
      'min_loan_dscr', 'max_loan_dscr'
    ];

    // Decimal fields that can handle fractional values
    const decimalFields = [
              'min_loan_term', 'max_loan_term', 'min_hold_period', 'max_hold_period',
        'closing_time_weeks', 'min_closing_time_weeks', 'max_closing_time_weeks'
    ];

    // Other numeric fields (can handle larger values)
    const standardNumericFields = [
      'minimum_deal_size', 'maximum_deal_size'
    ];

    // Process percentage fields (convert if > 1, handle ranges)
    percentageFields.forEach(field => {
      if (criteriaData[field] !== undefined && criteriaData[field] !== null) {
        const rawValue = criteriaData[field];
        
        // Handle range values (e.g., "5-10", "up to 75", "0-5")
        if (typeof rawValue === 'string') {
          const processedValue = this.processRangeValue(rawValue, field);
          if (processedValue !== null) {
            // For range values, set both min and max
            if (field.includes('_max') || field.includes('_min')) {
              criteriaData[field] = processedValue;
            } else {
              // For single fields, use the average or single value
              criteriaData[field] = processedValue;
            }
          } else {
            delete criteriaData[field];
          }
        } else if (typeof rawValue === 'number') {
          // FIXED: Interest rate fields are already parsed correctly as percentages
          // Don't convert interest rate fields from percentage to decimal
          const isInterestRateField = field.includes('interest_rate');
          
          // Convert percentage values > 1 to decimal (except for interest rate fields)
          const convertedValue = isInterestRateField ? rawValue : (rawValue > 1 ? rawValue / 100 : rawValue);
          
          // Validate against database constraint
          if (convertedValue >= 10 && !isInterestRateField) {
            console.warn(`   ⚠️  Value too large for ${field}, skipping: ${convertedValue}`);
            delete criteriaData[field];
            return;
          }
          
          criteriaData[field] = Math.round(convertedValue * 10000) / 10000;
        }
      }
    });

    // Process decimal fields (loan terms, hold periods, closing times)
    decimalFields.forEach(field => {
      if (criteriaData[field] !== undefined && criteriaData[field] !== null) {
        const rawValue = criteriaData[field];
        let numValue: number;

        if (typeof rawValue === 'string') {
          // Handle range values and "up to" logic
          const processedValue = this.processRangeValue(rawValue, field);
          if (processedValue !== null) {
            criteriaData[field] = processedValue;
          } else {
            delete criteriaData[field];
          }
        } else if (typeof rawValue === 'number') {
          criteriaData[field] = Math.round(rawValue * 100) / 100; // 2 decimal places
        }
      }
    });

    // Process closing time calculations
    this.processClosingTimeCalculations(criteriaData);

    // Process interest rate averages for multi-value fields
    this.processInterestRateAverages(criteriaData);

    // Process standard numeric fields
    standardNumericFields.forEach(field => {
      if (criteriaData[field] !== undefined && criteriaData[field] !== null) {
        const rawValue = criteriaData[field];
        let numValue: number;

        if (typeof rawValue === 'string') {
          numValue = parseFloat(rawValue);
        } else if (typeof rawValue === 'number') {
          numValue = rawValue;
        } else {
          delete criteriaData[field];
          return;
        }

        if (isNaN(numValue)) {
          delete criteriaData[field];
          return;
        }

        criteriaData[field] = numValue;
      }
    });
  }

  /**
   * Process range values and convert percentages appropriately
   */
  private processRangeValue(value: string, fieldName: string): number | null {
    if (!value || typeof value !== 'string') return null;

    const cleanValue = value.trim().toLowerCase();
    
    // FIXED: Interest rate fields are already parsed correctly as percentages
    // Don't apply automatic percentage conversion to interest rate fields
    const isInterestRateField = fieldName.includes('interest_rate');
    
    // Handle "up to X" or "upto X" patterns
    const upToMatch = cleanValue.match(/(?:up\s*to|upto)\s*([0-9.,]+)/);
    if (upToMatch) {
      const maxValue = parseFloat(upToMatch[1].replace(/,/g, ''));
      if (!isNaN(maxValue)) {
        // For "up to" values, set min to 0 and max to the value
        if (fieldName.includes('_min')) {
          return 0;
        } else if (fieldName.includes('_max')) {
          return isInterestRateField ? maxValue : (maxValue > 1 ? maxValue / 100 : maxValue);
        } else {
          return isInterestRateField ? maxValue : (maxValue > 1 ? maxValue / 100 : maxValue);
        }
      }
    }

    // Handle range patterns like "5-10", "0-75", "5 to 10"
    const rangeMatch = cleanValue.match(/([0-9.,]+)\s*(?:-|to)\s*([0-9.,]+)/);
    if (rangeMatch) {
      const minValue = parseFloat(rangeMatch[1].replace(/,/g, ''));
      const maxValue = parseFloat(rangeMatch[2].replace(/,/g, ''));
      
      if (!isNaN(minValue) && !isNaN(maxValue)) {
        if (fieldName.includes('_min')) {
          return isInterestRateField ? minValue : (minValue > 1 ? minValue / 100 : minValue);
        } else if (fieldName.includes('_max')) {
          return isInterestRateField ? maxValue : (maxValue > 1 ? maxValue / 100 : maxValue);
        } else {
          // Return average for single fields
          const avg = (minValue + maxValue) / 2;
          return isInterestRateField ? avg : (avg > 1 ? avg / 100 : avg);
        }
      }
    }

    // Handle single values with + suffix (e.g., "5+")
    const plusMatch = cleanValue.match(/([0-9.,]+)\+/);
    if (plusMatch) {
      const baseValue = parseFloat(plusMatch[1].replace(/,/g, ''));
      if (!isNaN(baseValue)) {
        if (fieldName.includes('_min')) {
          return isInterestRateField ? baseValue : (baseValue > 1 ? baseValue / 100 : baseValue);
        } else if (fieldName.includes('_max')) {
          return isInterestRateField ? 100 : 1; // Use 100% for interest rates, 1 for other percentage fields
        } else {
          return isInterestRateField ? baseValue : (baseValue > 1 ? baseValue / 100 : baseValue);
        }
      }
    }

    // Handle single numeric values
    const numMatch = cleanValue.match(/([0-9.,]+)/);
    if (numMatch) {
      const numValue = parseFloat(numMatch[1].replace(/,/g, ''));
      if (!isNaN(numValue)) {
        return isInterestRateField ? numValue : (numValue > 1 ? numValue / 100 : numValue);
      }
    }

    return null;
  }

  /**
   * Process closing time calculations (weeks and days)
   */
  private processClosingTimeCalculations(criteriaData: Record<string, any>): void {
    const closingTimeFields = ['closing_time_weeks', 'min_closing_time_weeks', 'max_closing_time_weeks'];
    
    closingTimeFields.forEach(field => {
      if (criteriaData[field] !== undefined && criteriaData[field] !== null) {
        const weeks = criteriaData[field];
        // No days conversion needed - database only has *_weeks columns
        
        // Just ensure it's a proper number with 2 decimal places
        // Format the weeks value properly
        criteriaData[field] = Math.round(weeks * 100) / 100;
      }
    });

    // Min and max closing time are already in weeks from InvestmentCriteriaProcessor
    // No conversion needed since they're already in the correct units
  }

  /**
   * Process interest rate averages for multi-value fields
   */
  private processInterestRateAverages(criteriaData: Record<string, any>): void {
    const interestRateFields = [
      'interest_rate_sofr', 'interest_rate_wsj', 'interest_rate_prime',
      'interest_rate_libor', 'interest_rate_5yt', 'interest_rate_10yt'
    ];

    interestRateFields.forEach(field => {
      if (criteriaData[field] !== undefined && criteriaData[field] !== null) {
        const rawValue = criteriaData[field];
        
        if (typeof rawValue === 'string') {
          // Handle multiple values separated by commas or other delimiters
          const values = rawValue.split(/[,;\/]/).map(v => v.trim()).filter(v => v);
          
          if (values.length > 1) {
            // Multiple values - calculate average
            const numValues = values.map(v => {
              const num = parseFloat(v);
              return isNaN(num) ? null : (num > 100 ? num / 100 : num);
            }).filter(v => v !== null);
            
            if (numValues.length > 0) {
              const average = numValues.reduce((a, b) => a + b, 0) / numValues.length;
              criteriaData[field] = Math.round(average * 10000) / 10000;
            }
          } else if (values.length === 1) {
            // Single value
            const num = parseFloat(values[0]);
            if (!isNaN(num)) {
              criteriaData[field] = num > 100 ? num / 100 : num;
            }
          }
        } else if (typeof rawValue === 'number') {
          // Single numeric value
          criteriaData[field] = rawValue > 100 ? rawValue / 100 : rawValue;
        }
      }
    });
  }

  
  /**
   * Process recourse as array of strings
   */
  private processRecourseArray(criteriaData: Record<string, any>): void {
    if (criteriaData.recourse_loan !== undefined && criteriaData.recourse_loan !== null) {
      const recourseValue = criteriaData.recourse_loan;
      
      if (typeof recourseValue === 'boolean') {
        criteriaData.recourse_loan = recourseValue ? ['Full Recourse'] : ['Non-Recourse'];
      } else if (typeof recourseValue === 'string') {
        const lowerValue = recourseValue.toLowerCase();
        if (lowerValue.includes('non-recourse') || lowerValue.includes('nonrecourse')) {
          criteriaData.recourse_loan = ['Non-Recourse'];
        } else if (lowerValue.includes('full recourse') || lowerValue === 'recourse') {
          criteriaData.recourse_loan = ['Full Recourse'];
        } else if (lowerValue.includes('limited recourse')) {
          criteriaData.recourse_loan = ['Limited Recourse'];
        } else {
          // Parse as comma-separated values
          criteriaData.recourse_loan = recourseValue.split(',').map(v => v.trim()).filter(v => v);
        }
      } else if (!Array.isArray(recourseValue)) {
        criteriaData.recourse_loan = [String(recourseValue)];
      }
    }
  }

  /**
   * Process tear sheet data
   */
  private processTearSheetData(criteriaData: Record<string, any>, rowData: Record<string, any>): void {
    // Collect tear sheet related fields
    const tearSheetFields = [
      'company_overview', 'investment_strategy', 'portfolio_highlights',
      'track_record', 'team_info', 'contact_information', 'financial_highlights',
      'deal_criteria', 'geographic_focus', 'sector_focus'
    ];
    
    const tearSheetData: Record<string, any> = {};
    
    tearSheetFields.forEach(field => {
      if (rowData[field] && rowData[field].trim() !== '') {
        tearSheetData[field] = rowData[field];
      }
    });
    
    // Add any additional fields that might be tear sheet related
    Object.keys(rowData).forEach(key => {
      if (key.includes('tear_sheet') || key.includes('overview') || key.includes('highlights')) {
        tearSheetData[key] = rowData[key];
      }
    });
    
    if (Object.keys(tearSheetData).length > 0) {
      criteriaData.tear_sheet = tearSheetData;
    }
  }
}

export default UploadProcessor 