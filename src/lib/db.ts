import { Pool } from "pg";
import * as dotenv from "dotenv";

dotenv.config();

const pool = new Pool({
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT || "5432"),
  database: process.env.DB_DATABASE,
  // Improved connection resilience settings
  connectionTimeoutMillis: 10000, // 10 seconds (increased from 5)
  idleTimeoutMillis: 30000, // 30 seconds
  max: 15, // Maximum number of clients in the pool (reduced from 20)
  allowExitOnIdle: true, // Allow pool to exit when no connections
});

// Add error handling for the pool
pool.on("error", (err) => {
  console.error("Unexpected error on idle client", err);
  process.exit(-1); // Exit process on pool errors
});

pool.on("connect", () => {
  console.log("Database connection established");
});

// pool.on('remove', () => {
//   console.log('Database connection removed from pool')
// })

// Optional: log to verify the connection details (remove in production)
// console.log("DB Config:", {
//   host: process.env.DB_HOST,
//   port: process.env.DB_PORT,
//   database: process.env.DB_DATABASE,
//   user: process.env.DB_USER,
//   // Don't log the actual password
//   hasPassword: !!process.env.DB_PASSWORD,
// });

export { pool };
