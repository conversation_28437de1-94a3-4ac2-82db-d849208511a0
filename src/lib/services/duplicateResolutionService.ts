import { pool } from '@/lib/db'
import { DataNormalizationService } from './dataNormalizationService'

export interface MergePreview {
  primaryRecord: any
  duplicateRecord: any
  mergedRecord: any
  conflictingFields: string[]
  relatedRecords: {
    contacts?: number
    companies?: number
    investmentCriteria?: number
  }
}

export interface MergeOptions {
  primaryRecordId: number
  duplicateRecordId: number
  recordType: 'company' | 'contact'
  fieldSelections: Record<string, 'primary' | 'duplicate' | 'custom'>
  customValues: Record<string, any>
  mergeRelatedRecords: boolean
}

export class DuplicateResolutionService {
  /**
   * Generate a preview of what the merged record would look like
   */
  static async generateMergePreview(
    primaryId: number,
    duplicateId: number,
    recordType: 'company' | 'contact'
  ): Promise<MergePreview> {
    const client = await pool.connect()

    try {
      let primaryRecord: any
      let duplicateRecord: any
      let relatedRecords: any = {}

      if (recordType === 'company') {
        const [primaryResult, duplicateResult] = await Promise.all([
          client.query('SELECT * FROM companies WHERE company_id = $1', [primaryId]),
          client.query('SELECT * FROM companies WHERE company_id = $1', [duplicateId])
        ])

        primaryRecord = primaryResult.rows[0]
        duplicateRecord = duplicateResult.rows[0]

        // Count related records
        const [contactCount, criteriaCount] = await Promise.all([
          client.query('SELECT COUNT(*) as count FROM contacts WHERE company_id IN ($1, $2)', [primaryId, duplicateId]),
          client.query('SELECT COUNT(*) as count FROM investment_criteria WHERE entity_type = \'Company\' AND entity_id IN ($1, $2)', [primaryId.toString(), duplicateId.toString()])
        ])

        relatedRecords = {
          contacts: parseInt(contactCount.rows[0].count),
          investmentCriteria: parseInt(criteriaCount.rows[0].count)
        }

      } else if (recordType === 'contact') {
        const [primaryResult, duplicateResult] = await Promise.all([
          client.query(`
            SELECT c.*, comp.company_name 
            FROM contacts c 
            LEFT JOIN companies comp ON c.company_id = comp.company_id 
            WHERE c.contact_id = $1
          `, [primaryId]),
          client.query(`
            SELECT c.*, comp.company_name 
            FROM contacts c 
            LEFT JOIN companies comp ON c.company_id = comp.company_id 
            WHERE c.contact_id = $1
          `, [duplicateId])
        ])

        primaryRecord = primaryResult.rows[0]
        duplicateRecord = duplicateResult.rows[0]
      }

      if (!primaryRecord || !duplicateRecord) {
        throw new Error('One or both records not found')
      }

      // Identify conflicting fields
      const conflictingFields: string[] = []
      const mergedRecord: any = { ...primaryRecord }

      for (const [key, value] of Object.entries(duplicateRecord)) {
        if (key.includes('_id') || key.includes('created_at') || key.includes('updated_at')) {
          continue // Skip ID and timestamp fields
        }

        const primaryValue = primaryRecord[key]
        const duplicateValue = value

        if (primaryValue && duplicateValue && primaryValue !== duplicateValue) {
          conflictingFields.push(key)
        } else if (!primaryValue && duplicateValue) {
          mergedRecord[key] = duplicateValue // Fill empty fields
        }
      }

      return {
        primaryRecord,
        duplicateRecord,
        mergedRecord,
        conflictingFields,
        relatedRecords
      }

    } finally {
      client.release()
    }
  }

  /**
   * Execute a merge operation with detailed field selection
   */
  static async executeMerge(options: MergeOptions): Promise<{
    success: boolean
    mergedRecordId: number
    deletedRecordId: number
    mergedData: any
    error?: string
  }> {
    const client = await pool.connect()

    try {
      await client.query('BEGIN')

      const { primaryRecordId, duplicateRecordId, recordType, fieldSelections, customValues, mergeRelatedRecords } = options

      let mergedData: any

      if (recordType === 'company') {
        mergedData = await this.mergeCompaniesDetailed(
          client,
          primaryRecordId,
          duplicateRecordId,
          fieldSelections,
          customValues,
          mergeRelatedRecords
        )
      } else if (recordType === 'contact') {
        mergedData = await this.mergeContactsDetailed(
          client,
          primaryRecordId,
          duplicateRecordId,
          fieldSelections,
          customValues
        )
      } else {
        throw new Error(`Unsupported record type: ${recordType}`)
      }

      // Update normalized data for the merged record
      if (recordType === 'company') {
        await DataNormalizationService.normalizeCompany(primaryRecordId)
      } else {
        await DataNormalizationService.normalizeContact(primaryRecordId)
      }

      // Mark all duplicate records involving these two records as merged
      await client.query(`
        UPDATE duplicate_records 
        SET status = 'merged', resolved_at = CURRENT_TIMESTAMP
        WHERE record_type = $1 
        AND ((primary_record_id = $2 AND duplicate_record_id = $3) 
             OR (primary_record_id = $3 AND duplicate_record_id = $2))
      `, [recordType, primaryRecordId, duplicateRecordId])

      await client.query('COMMIT')

      return {
        success: true,
        mergedRecordId: primaryRecordId,
        deletedRecordId: duplicateRecordId,
        mergedData
      }

    } catch (error) {
      await client.query('ROLLBACK')
      console.error('Error executing merge:', error)
      return {
        success: false,
        mergedRecordId: 0,
        deletedRecordId: 0,
        mergedData: null,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    } finally {
      client.release()
    }
  }

  /**
   * Merge companies with detailed field selection
   */
  private static async mergeCompaniesDetailed(
    client: any,
    primaryId: number,
    duplicateId: number,
    fieldSelections: Record<string, 'primary' | 'duplicate' | 'custom'>,
    customValues: Record<string, any>,
    mergeRelatedRecords: boolean
  ): Promise<any> {
    // Get both company records
    const [primaryResult, duplicateResult] = await Promise.all([
      client.query('SELECT * FROM companies WHERE company_id = $1', [primaryId]),
      client.query('SELECT * FROM companies WHERE company_id = $1', [duplicateId])
    ])

    const primaryData = primaryResult.rows[0]
    const duplicateData = duplicateResult.rows[0]

    // Build the merged data based on field selections
    const mergedData: any = { ...primaryData }

    for (const [field, selection] of Object.entries(fieldSelections)) {
      if (selection === 'duplicate' && duplicateData[field] !== undefined) {
        mergedData[field] = duplicateData[field]
      } else if (selection === 'custom' && customValues[field] !== undefined) {
        mergedData[field] = customValues[field]
      }
    }

    // Apply any additional custom values
    for (const [field, value] of Object.entries(customValues)) {
      if (!fieldSelections[field]) {
        mergedData[field] = value
      }
    }

    // Update the primary company with merged data
    const updateFields = Object.keys(mergedData)
      .filter(key => !key.includes('_id') && !key.includes('created_at'))
      .map((key, index) => `${key} = $${index + 2}`)

    const updateValues = Object.keys(mergedData)
      .filter(key => !key.includes('_id') && !key.includes('created_at'))
      .map(key => mergedData[key])

    if (updateFields.length > 0) {
      await client.query(`
        UPDATE companies 
        SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE company_id = $1
      `, [primaryId, ...updateValues])
    }

    if (mergeRelatedRecords) {
      // Move all contacts from duplicate to primary company
      await client.query(`
        UPDATE contacts 
        SET company_id = $1, updated_at = CURRENT_TIMESTAMP
        WHERE company_id = $2
      `, [primaryId, duplicateId])

      // Move investment criteria
      await client.query(`
        UPDATE investment_criteria 
        SET entity_id = $1, updated_at = CURRENT_TIMESTAMP
        WHERE entity_type = 'Company' AND entity_id = $2
      `, [primaryId.toString(), duplicateId.toString()])
    }

    // Delete the duplicate company
    await client.query('DELETE FROM companies WHERE company_id = $1', [duplicateId])

    // Return the updated company data
    const updatedResult = await client.query('SELECT * FROM companies WHERE company_id = $1', [primaryId])
    return updatedResult.rows[0]
  }

  /**
   * Merge contacts with detailed field selection
   */
  private static async mergeContactsDetailed(
    client: any,
    primaryId: number,
    duplicateId: number,
    fieldSelections: Record<string, 'primary' | 'duplicate' | 'custom'>,
    customValues: Record<string, any>
  ): Promise<any> {
    // Get both contact records
    const [primaryResult, duplicateResult] = await Promise.all([
      client.query('SELECT * FROM contacts WHERE contact_id = $1', [primaryId]),
      client.query('SELECT * FROM contacts WHERE contact_id = $1', [duplicateId])
    ])

    const primaryData = primaryResult.rows[0]
    const duplicateData = duplicateResult.rows[0]

    // Build the merged data based on field selections
    const mergedData: any = { ...primaryData }

    for (const [field, selection] of Object.entries(fieldSelections)) {
      if (selection === 'duplicate' && duplicateData[field] !== undefined) {
        mergedData[field] = duplicateData[field]
      } else if (selection === 'custom' && customValues[field] !== undefined) {
        mergedData[field] = customValues[field]
      }
    }

    // Apply any additional custom values
    for (const [field, value] of Object.entries(customValues)) {
      if (!fieldSelections[field]) {
        mergedData[field] = value
      }
    }

    // Update the primary contact with merged data
    const updateFields = Object.keys(mergedData)
      .filter(key => !key.includes('_id') && !key.includes('created_at'))
      .map((key, index) => `${key} = $${index + 2}`)

    const updateValues = Object.keys(mergedData)
      .filter(key => !key.includes('_id') && !key.includes('created_at'))
      .map(key => mergedData[key])

    if (updateFields.length > 0) {
      await client.query(`
        UPDATE contacts 
        SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE contact_id = $1
      `, [primaryId, ...updateValues])
    }

    // Delete the duplicate contact
    await client.query('DELETE FROM contacts WHERE contact_id = $1', [duplicateId])

    // Return the updated contact data
    const updatedResult = await client.query('SELECT * FROM contacts WHERE contact_id = $1', [primaryId])
    return updatedResult.rows[0]
  }

  /**
   * Batch resolve multiple duplicates
   */
  static async batchResolve(
    duplicateIds: number[],
    action: 'merge' | 'keep_separate' | 'mark_false_positive',
    resolvedBy?: string
  ): Promise<{
    success: boolean
    processed: number
    errors: number
    results: any[]
  }> {
    let processed = 0
    let errors = 0
    const results: any[] = []

    for (const duplicateId of duplicateIds) {
      try {
        // This would call the resolve endpoint logic
        // For now, just update the status
        const client = await pool.connect()
        
        try {
          const status = action === 'merge' ? 'merged' : 
                        action === 'keep_separate' ? 'confirmed' : 'false_positive'
          
          await client.query(`
            UPDATE duplicate_records 
            SET status = $1, resolved_at = CURRENT_TIMESTAMP, resolved_by = $2
            WHERE id = $3
          `, [status, resolvedBy, duplicateId])
          
          processed++
          results.push({ duplicateId, success: true })
        } finally {
          client.release()
        }
      } catch (error) {
        errors++
        results.push({ duplicateId, success: false, error: error instanceof Error ? error.message : 'Unknown error' })
      }
    }

    return {
      success: errors === 0,
      processed,
      errors,
      results
    }
  }
}
