import { pool } from '@/lib/db'

export interface CompanyNormalizationData {
  company_id: number
  normalized_name: string
  normalized_domain: string | null
  name_tokens: string[]
  industry_normalized: string | null
  phone_normalized: string | null
}

export interface ContactNormalizationData {
  contact_id: number
  full_name_normalized: string
  email_normalized: string | null
  linkedin_handle: string | null
  phone_normalized: string | null
  name_tokens: string[]
}

export class DataNormalizationService {
  /**
   * Normalize company name by removing common suffixes and converting to lowercase
   */
  static normalizeCompanyName(name: string | null): string {
    if (!name) return ''
    
    return name
      .toLowerCase()
      .trim()
      .replace(/\s+(inc\.?|llc\.?|ltd\.?|corp\.?|corporation|company|co\.?)$/gi, '')
      .replace(/\s+/g, ' ')
      .trim()
  }

  /**
   * Extract domain from website URL
   */
  static extractDomain(website: string | null): string | null {
    if (!website) return null
    
    try {
      const cleaned = website
        .toLowerCase()
        .replace(/^https?:\/\/(www\.)?/, '')
        .replace(/\/.*$/, '')
        .trim()
      
      return cleaned || null
    } catch {
      return null
    }
  }

  /**
   * Normalize phone number by keeping only digits
   */
  static normalizePhone(phone: string | null): string | null {
    if (!phone) return null
    
    const digits = phone.replace(/[^0-9]/g, '')
    return digits.length >= 10 ? digits : null
  }

  /**
   * Extract LinkedIn handle from LinkedIn URL
   */
  static extractLinkedInHandle(url: string | null): string | null {
    if (!url) return null
    
    try {
      const match = url.match(/linkedin\.com\/in\/([^\/\?]+)/i)
      return match ? match[1].toLowerCase() : null
    } catch {
      return null
    }
  }

  /**
   * Tokenize name into searchable parts
   */
  static tokenizeName(name: string | null): string[] {
    if (!name) return []
    
    return name
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(token => token.length > 1)
  }

  /**
   * Normalize email to lowercase
   */
  static normalizeEmail(email: string | null): string | null {
    if (!email) return null
    return email.toLowerCase().trim()
  }

  /**
   * Populate normalized data for a single company
   */
  static async normalizeCompany(companyId: number): Promise<void> {
    const client = await pool.connect()
    
    try {
      // Get company data
      const companyResult = await client.query(`
        SELECT company_id, company_name, company_website, industry, company_phone
        FROM companies 
        WHERE company_id = $1
      `, [companyId])
      
      if (companyResult.rows.length === 0) {
        throw new Error(`Company with ID ${companyId} not found`)
      }
      
      const company = companyResult.rows[0]
      
      const normalizedData: CompanyNormalizationData = {
        company_id: company.company_id,
        normalized_name: this.normalizeCompanyName(company.company_name),
        normalized_domain: this.extractDomain(company.company_website),
        name_tokens: this.tokenizeName(company.company_name),
        industry_normalized: company.industry ? company.industry.toLowerCase().trim() : null,
        phone_normalized: this.normalizePhone(company.company_phone)
      }
      
      // Insert or update normalized data
      await client.query(`
        INSERT INTO company_normalized_data (
          company_id, normalized_name, normalized_domain, name_tokens, 
          industry_normalized, phone_normalized, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ON CONFLICT (company_id) DO UPDATE SET
          normalized_name = EXCLUDED.normalized_name,
          normalized_domain = EXCLUDED.normalized_domain,
          name_tokens = EXCLUDED.name_tokens,
          industry_normalized = EXCLUDED.industry_normalized,
          phone_normalized = EXCLUDED.phone_normalized,
          updated_at = CURRENT_TIMESTAMP
      `, [
        normalizedData.company_id,
        normalizedData.normalized_name,
        normalizedData.normalized_domain,
        normalizedData.name_tokens,
        normalizedData.industry_normalized,
        normalizedData.phone_normalized
      ])
      
    } finally {
      client.release()
    }
  }

  /**
   * Populate normalized data for a single contact
   */
  static async normalizeContact(contactId: number): Promise<void> {
    const client = await pool.connect()
    
    try {
      // Get contact data
      const contactResult = await client.query(`
        SELECT contact_id, full_name, email, linkedin_url, phone
        FROM contacts 
        WHERE contact_id = $1
      `, [contactId])
      
      if (contactResult.rows.length === 0) {
        throw new Error(`Contact with ID ${contactId} not found`)
      }
      
      const contact = contactResult.rows[0]
      
      const normalizedData: ContactNormalizationData = {
        contact_id: contact.contact_id,
        full_name_normalized: contact.full_name ? contact.full_name.toLowerCase().trim() : '',
        email_normalized: this.normalizeEmail(contact.email),
        linkedin_handle: this.extractLinkedInHandle(contact.linkedin_url),
        phone_normalized: this.normalizePhone(contact.phone),
        name_tokens: this.tokenizeName(contact.full_name)
      }
      
      // Insert or update normalized data
      await client.query(`
        INSERT INTO contact_normalized_data (
          contact_id, full_name_normalized, email_normalized, linkedin_handle,
          phone_normalized, name_tokens, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ON CONFLICT (contact_id) DO UPDATE SET
          full_name_normalized = EXCLUDED.full_name_normalized,
          email_normalized = EXCLUDED.email_normalized,
          linkedin_handle = EXCLUDED.linkedin_handle,
          phone_normalized = EXCLUDED.phone_normalized,
          name_tokens = EXCLUDED.name_tokens,
          updated_at = CURRENT_TIMESTAMP
      `, [
        normalizedData.contact_id,
        normalizedData.full_name_normalized,
        normalizedData.email_normalized,
        normalizedData.linkedin_handle,
        normalizedData.phone_normalized,
        normalizedData.name_tokens
      ])
      
    } finally {
      client.release()
    }
  }

  /**
   * Batch normalize all companies
   */
  static async normalizeAllCompanies(batchSize: number = 100): Promise<{ processed: number, errors: number }> {
    const client = await pool.connect()
    let processed = 0
    let errors = 0
    
    try {
      // Get total count
      const countResult = await client.query('SELECT COUNT(*) as total FROM companies')
      const total = parseInt(countResult.rows[0].total)
      
      console.log(`Starting normalization of ${total} companies in batches of ${batchSize}`)
      
      for (let offset = 0; offset < total; offset += batchSize) {
        const companiesResult = await client.query(`
          SELECT company_id FROM companies 
          ORDER BY company_id 
          LIMIT $1 OFFSET $2
        `, [batchSize, offset])
        
        for (const company of companiesResult.rows) {
          try {
            await this.normalizeCompany(company.company_id)
            processed++
          } catch (error) {
            console.error(`Error normalizing company ${company.company_id}:`, error)
            errors++
          }
        }
        
        console.log(`Processed ${Math.min(offset + batchSize, total)}/${total} companies`)
      }
      
    } finally {
      client.release()
    }
    
    return { processed, errors }
  }

  /**
   * Batch normalize all contacts
   */
  static async normalizeAllContacts(batchSize: number = 100): Promise<{ processed: number, errors: number }> {
    const client = await pool.connect()
    let processed = 0
    let errors = 0
    
    try {
      // Get total count
      const countResult = await client.query('SELECT COUNT(*) as total FROM contacts')
      const total = parseInt(countResult.rows[0].total)
      
      console.log(`Starting normalization of ${total} contacts in batches of ${batchSize}`)
      
      for (let offset = 0; offset < total; offset += batchSize) {
        const contactsResult = await client.query(`
          SELECT contact_id FROM contacts 
          ORDER BY contact_id 
          LIMIT $1 OFFSET $2
        `, [batchSize, offset])
        
        for (const contact of contactsResult.rows) {
          try {
            await this.normalizeContact(contact.contact_id)
            processed++
          } catch (error) {
            console.error(`Error normalizing contact ${contact.contact_id}:`, error)
            errors++
          }
        }
        
        console.log(`Processed ${Math.min(offset + batchSize, total)}/${total} contacts`)
      }
      
    } finally {
      client.release()
    }
    
    return { processed, errors }
  }
}
