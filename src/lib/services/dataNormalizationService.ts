import { pool } from '@/lib/db'
import Bottleneck from 'bottleneck'

export interface CompanyNormalizationData {
  company_id: number
  normalized_name: string
  normalized_domain: string | null
  name_tokens: string[]
  industry_normalized: string | null
  phone_normalized: string | null
}

export interface ContactNormalizationData {
  contact_id: number
  full_name_normalized: string
  email_normalized: string | null
  linkedin_handle: string | null
  phone_normalized: string | null
  name_tokens: string[]
}

export class DataNormalizationService {
  // Create a bottleneck limiter for database operations
  private static limiter = new Bottleneck({
    maxConcurrent: 10, // Allow 10 concurrent operations
    minTime: 10 // Minimum 10ms between operations
  })
  /**
   * Normalize company name by removing common suffixes and converting to lowercase
   */
  static normalizeCompanyName(name: string | null): string {
    if (!name) return ''
    
    return name
      .toLowerCase()
      .trim()
      .replace(/\s+(inc\.?|llc\.?|ltd\.?|corp\.?|corporation|company|co\.?)$/gi, '')
      .replace(/\s+/g, ' ')
      .trim()
  }

  /**
   * Extract domain from website URL
   */
  static extractDomain(website: string | null): string | null {
    if (!website) return null
    
    try {
      const cleaned = website
        .toLowerCase()
        .replace(/^https?:\/\/(www\.)?/, '')
        .replace(/\/.*$/, '')
        .trim()
      
      return cleaned || null
    } catch {
      return null
    }
  }

  /**
   * Normalize phone number by keeping only digits
   */
  static normalizePhone(phone: string | null): string | null {
    if (!phone) return null
    
    const digits = phone.replace(/[^0-9]/g, '')
    return digits.length >= 10 ? digits : null
  }

  /**
   * Extract LinkedIn handle from LinkedIn URL
   */
  static extractLinkedInHandle(url: string | null): string | null {
    if (!url) return null
    
    try {
      const match = url.match(/linkedin\.com\/in\/([^\/\?]+)/i)
      return match ? match[1].toLowerCase() : null
    } catch {
      return null
    }
  }

  /**
   * Tokenize name into searchable parts
   */
  static tokenizeName(name: string | null): string[] {
    if (!name) return []
    
    return name
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(token => token.length > 1)
  }

  /**
   * Normalize email to lowercase
   */
  static normalizeEmail(email: string | null): string | null {
    if (!email) return null
    return email.toLowerCase().trim()
  }

  /**
   * Populate normalized data for a single company
   */
  static async normalizeCompany(companyId: number): Promise<void> {
    const client = await pool.connect()
    
    try {
      // Get company data
      const companyResult = await client.query(`
        SELECT company_id, company_name, company_website, industry, company_phone
        FROM companies 
        WHERE company_id = $1
      `, [companyId])
      
      if (companyResult.rows.length === 0) {
        throw new Error(`Company with ID ${companyId} not found`)
      }
      
      const company = companyResult.rows[0]
      
      const normalizedData: CompanyNormalizationData = {
        company_id: company.company_id,
        normalized_name: this.normalizeCompanyName(company.company_name),
        normalized_domain: this.extractDomain(company.company_website),
        name_tokens: this.tokenizeName(company.company_name),
        industry_normalized: company.industry ? company.industry.toLowerCase().trim() : null,
        phone_normalized: this.normalizePhone(company.company_phone)
      }
      
      // Check if record exists and update or insert accordingly
      const existingResult = await client.query(`
        SELECT id FROM company_normalized_data WHERE company_id = $1
      `, [normalizedData.company_id])

      if (existingResult.rows.length > 0) {
        // Update existing record
        await client.query(`
          UPDATE company_normalized_data SET
            normalized_name = $2,
            normalized_domain = $3,
            name_tokens = $4,
            industry_normalized = $5,
            phone_normalized = $6,
            updated_at = CURRENT_TIMESTAMP
          WHERE company_id = $1
        `, [
          normalizedData.company_id,
          normalizedData.normalized_name,
          normalizedData.normalized_domain,
          normalizedData.name_tokens,
          normalizedData.industry_normalized,
          normalizedData.phone_normalized
        ])
      } else {
        // Insert new record
        await client.query(`
          INSERT INTO company_normalized_data (
            company_id, normalized_name, normalized_domain, name_tokens,
            industry_normalized, phone_normalized, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `, [
          normalizedData.company_id,
          normalizedData.normalized_name,
          normalizedData.normalized_domain,
          normalizedData.name_tokens,
          normalizedData.industry_normalized,
          normalizedData.phone_normalized
        ])
      }
      
    } finally {
      client.release()
    }
  }

  /**
   * Populate normalized data for a single contact
   */
  static async normalizeContact(contactId: number): Promise<void> {
    const client = await pool.connect()
    
    try {
      // Get contact data
      const contactResult = await client.query(`
        SELECT contact_id, full_name, email, linkedin_url, phone
        FROM contacts 
        WHERE contact_id = $1
      `, [contactId])
      
      if (contactResult.rows.length === 0) {
        throw new Error(`Contact with ID ${contactId} not found`)
      }
      
      const contact = contactResult.rows[0]
      
      const normalizedData: ContactNormalizationData = {
        contact_id: contact.contact_id,
        full_name_normalized: contact.full_name ? contact.full_name.toLowerCase().trim() : '',
        email_normalized: this.normalizeEmail(contact.email),
        linkedin_handle: this.extractLinkedInHandle(contact.linkedin_url),
        phone_normalized: this.normalizePhone(contact.phone),
        name_tokens: this.tokenizeName(contact.full_name)
      }
      
      // Check if record exists and update or insert accordingly
      const existingResult = await client.query(`
        SELECT id FROM contact_normalized_data WHERE contact_id = $1
      `, [normalizedData.contact_id])

      if (existingResult.rows.length > 0) {
        // Update existing record
        await client.query(`
          UPDATE contact_normalized_data SET
            full_name_normalized = $2,
            email_normalized = $3,
            linkedin_handle = $4,
            phone_normalized = $5,
            name_tokens = $6,
            updated_at = CURRENT_TIMESTAMP
          WHERE contact_id = $1
        `, [
          normalizedData.contact_id,
          normalizedData.full_name_normalized,
          normalizedData.email_normalized,
          normalizedData.linkedin_handle,
          normalizedData.phone_normalized,
          normalizedData.name_tokens
        ])
      } else {
        // Insert new record
        await client.query(`
          INSERT INTO contact_normalized_data (
            contact_id, full_name_normalized, email_normalized, linkedin_handle,
            phone_normalized, name_tokens, created_at, updated_at
          ) VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `, [
          normalizedData.contact_id,
          normalizedData.full_name_normalized,
          normalizedData.email_normalized,
          normalizedData.linkedin_handle,
          normalizedData.phone_normalized,
          normalizedData.name_tokens
        ])
      }
      
    } finally {
      client.release()
    }
  }

  /**
   * Batch normalize companies using bulk operations - MUCH FASTER
   */
  static async normalizeAllCompaniesFast(batchSize: number = 1000): Promise<{ processed: number, errors: number }> {
    const client = await pool.connect()
    let processed = 0
    let errors = 0

    try {
      // Get total count
      const countResult = await client.query('SELECT COUNT(*) as total FROM companies')
      const total = parseInt(countResult.rows[0].total)

      console.log(`Starting FAST normalization of ${total} companies in batches of ${batchSize}`)

      for (let offset = 0; offset < total; offset += batchSize) {
        try {
          // Process entire batch in one query
          const result = await client.query(`
            INSERT INTO company_normalized_data (
              company_id, normalized_name, normalized_domain, name_tokens,
              industry_normalized, phone_normalized, created_at, updated_at
            )
            SELECT
              c.company_id,
              LOWER(TRIM(REGEXP_REPLACE(
                COALESCE(c.company_name, ''),
                '\\s+(inc\\.?|llc\\.?|ltd\\.?|corp\\.?|corporation|company|co\\.?)$',
                '',
                'gi'
              ))) as normalized_name,
              CASE
                WHEN c.company_website IS NOT NULL THEN
                  LOWER(REGEXP_REPLACE(
                    REGEXP_REPLACE(c.company_website, '^https?://(www\\.)?', ''),
                    '/.*$', ''
                  ))
                ELSE NULL
              END as normalized_domain,
              STRING_TO_ARRAY(
                LOWER(TRIM(REGEXP_REPLACE(
                  COALESCE(c.company_name, ''),
                  '\\s+(inc\\.?|llc\\.?|ltd\\.?|corp\\.?|corporation|company|co\\.?)$',
                  '',
                  'gi'
                ))), ' '
              ) as name_tokens,
              LOWER(COALESCE(c.industry, '')) as industry_normalized,
              SUBSTRING(REGEXP_REPLACE(COALESCE(c.company_phone, ''), '[^0-9]', '', 'g'), 1, 20) as phone_normalized,
              CURRENT_TIMESTAMP as created_at,
              CURRENT_TIMESTAMP as updated_at
            FROM companies c
            WHERE c.company_id NOT IN (SELECT company_id FROM company_normalized_data WHERE company_id IS NOT NULL)
            ORDER BY c.company_id
            LIMIT $1 OFFSET $2
          `, [batchSize, offset])

          processed += result.rowCount || 0
          console.log(`Processed batch ${Math.floor(offset/batchSize) + 1}: ${result.rowCount} new records, total: ${processed}/${total}`)

        } catch (error) {
          console.error(`Error processing batch at offset ${offset}:`, error)
          errors++
        }
      }

      // Update existing records that might have changed
      console.log('Updating existing records...')
      const updateResult = await client.query(`
        UPDATE company_normalized_data
        SET
          normalized_name = LOWER(TRIM(REGEXP_REPLACE(
            COALESCE(c.company_name, ''),
            '\\s+(inc\\.?|llc\\.?|ltd\\.?|corp\\.?|corporation|company|co\\.?)$',
            '',
            'gi'
          ))),
          normalized_domain = CASE
            WHEN c.company_website IS NOT NULL THEN
              LOWER(REGEXP_REPLACE(
                REGEXP_REPLACE(c.company_website, '^https?://(www\\.)?', ''),
                '/.*$', ''
              ))
            ELSE NULL
          END,
          name_tokens = STRING_TO_ARRAY(
            LOWER(TRIM(REGEXP_REPLACE(
              COALESCE(c.company_name, ''),
              '\\s+(inc\\.?|llc\\.?|ltd\\.?|corp\\.?|corporation|company|co\\.?)$',
              '',
              'gi'
            ))), ' '
          ),
          industry_normalized = LOWER(COALESCE(c.industry, '')),
          phone_normalized = SUBSTRING(REGEXP_REPLACE(COALESCE(c.company_phone, ''), '[^0-9]', '', 'g'), 1, 20),
          updated_at = CURRENT_TIMESTAMP
        FROM companies c
        WHERE company_normalized_data.company_id = c.company_id
      `)

      console.log(`Updated ${updateResult.rowCount} existing records`)

    } finally {
      client.release()
    }

    return { processed, errors }
  }

  /**
   * Batch normalize all companies (legacy method - slower)
   */
  static async normalizeAllCompanies(batchSize: number = 100): Promise<{ processed: number, errors: number }> {
    // Use the fast method instead
    return this.normalizeAllCompaniesFast(batchSize * 10)
  }

  /**
   * Batch normalize contacts using bulk operations - MUCH FASTER
   */
  static async normalizeAllContactsFast(batchSize: number = 1000): Promise<{ processed: number, errors: number }> {
    const client = await pool.connect()
    let processed = 0
    let errors = 0

    try {
      // Get total count
      const countResult = await client.query('SELECT COUNT(*) as total FROM contacts')
      const total = parseInt(countResult.rows[0].total)

      console.log(`Starting FAST normalization of ${total} contacts in batches of ${batchSize}`)

      for (let offset = 0; offset < total; offset += batchSize) {
        try {
          // Process entire batch in one query
          const result = await client.query(`
            INSERT INTO contact_normalized_data (
              contact_id, full_name_normalized, email_normalized, linkedin_handle,
              phone_normalized, name_tokens, created_at, updated_at
            )
            SELECT
              c.contact_id,
              LOWER(TRIM(COALESCE(c.full_name, ''))) as full_name_normalized,
              LOWER(TRIM(c.email)) as email_normalized,
              CASE
                WHEN c.linkedin_url IS NOT NULL THEN
                  LOWER(SUBSTRING(c.linkedin_url FROM 'linkedin\\.com/in/([^/\\?]+)'))
                ELSE NULL
              END as linkedin_handle,
              SUBSTRING(REGEXP_REPLACE(COALESCE(c.phone_number, ''), '[^0-9]', '', 'g'), 1, 20) as phone_normalized,
              STRING_TO_ARRAY(
                LOWER(REGEXP_REPLACE(COALESCE(c.full_name, ''), '[^\\w\\s]', ' ', 'g')),
                ' '
              ) as name_tokens,
              CURRENT_TIMESTAMP as created_at,
              CURRENT_TIMESTAMP as updated_at
            FROM contacts c
            WHERE c.contact_id NOT IN (SELECT contact_id FROM contact_normalized_data WHERE contact_id IS NOT NULL)
            ORDER BY c.contact_id
            LIMIT $1 OFFSET $2
          `, [batchSize, offset])

          processed += result.rowCount || 0
          console.log(`Processed batch ${Math.floor(offset/batchSize) + 1}: ${result.rowCount} new records, total: ${processed}/${total}`)

        } catch (error) {
          console.error(`Error processing batch at offset ${offset}:`, error)
          errors++
        }
      }

      // Update existing records that might have changed
      console.log('Updating existing contact records...')
      const updateResult = await client.query(`
        UPDATE contact_normalized_data
        SET
          full_name_normalized = LOWER(TRIM(COALESCE(c.full_name, ''))),
          email_normalized = LOWER(TRIM(c.email)),
          linkedin_handle = CASE
            WHEN c.linkedin_url IS NOT NULL THEN
              LOWER(SUBSTRING(c.linkedin_url FROM 'linkedin\\.com/in/([^/\\?]+)'))
            ELSE NULL
          END,
          phone_normalized = SUBSTRING(REGEXP_REPLACE(COALESCE(c.phone_number, ''), '[^0-9]', '', 'g'), 1, 20),
          name_tokens = STRING_TO_ARRAY(
            LOWER(REGEXP_REPLACE(COALESCE(c.full_name, ''), '[^\\w\\s]', ' ', 'g')),
            ' '
          ),
          updated_at = CURRENT_TIMESTAMP
        FROM contacts c
        WHERE contact_normalized_data.contact_id = c.contact_id
      `)

      console.log(`Updated ${updateResult.rowCount} existing contact records`)

    } finally {
      client.release()
    }

    return { processed, errors }
  }

  /**
   * Batch normalize all contacts (legacy method - slower)
   */
  static async normalizeAllContacts(batchSize: number = 100): Promise<{ processed: number, errors: number }> {
    // Use the fast method instead
    return this.normalizeAllContactsFast(batchSize * 10)
  }
}
