import { pool } from '@/lib/db'
import { DuplicateRecord, DuplicateMatchType, DuplicateStatus } from '@/types/duplicate'

export interface CompanyMatchCriteria {
  exactDomain: boolean
  exactName: boolean
  phoneMatch: boolean
  nameSimilarity: number // 0.0 to 1.0
  minimumConfidence: number
}

export interface ContactMatchCriteria {
  exactEmail: boolean
  linkedinMatch: boolean
  emailDomainNameSimilarity: boolean
  nameCompanyMatch: boolean
  phoneMatch: boolean
  minimumConfidence: number
}

export interface DuplicateMatch {
  primaryRecordId: number
  duplicateRecordId: number
  matchType: DuplicateMatchType
  confidence: number
  matchDetails: Record<string, any>
}

export interface CompanyDuplicateResult {
  primaryCompany: any
  duplicates: Array<{
    company: any
    matchType: DuplicateMatchType
    confidence: number
    matchDetails: Record<string, any>
  }>
}

export interface ContactDuplicateResult {
  primaryContact: any
  duplicates: Array<{
    contact: any
    matchType: DuplicateMatchType
    confidence: number
    matchDetails: Record<string, any>
  }>
}

export class DuplicateDetectionService {
  /**
   * Calculate string similarity using Levenshtein distance
   */
  static calculateSimilarity(str1: string, str2: string): number {
    if (!str1 || !str2) return 0
    if (str1 === str2) return 1

    const matrix = []
    const len1 = str1.length
    const len2 = str2.length

    for (let i = 0; i <= len2; i++) {
      matrix[i] = [i]
    }

    for (let j = 0; j <= len1; j++) {
      matrix[0][j] = j
    }

    for (let i = 1; i <= len2; i++) {
      for (let j = 1; j <= len1; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }

    const maxLen = Math.max(len1, len2)
    return (maxLen - matrix[len2][len1]) / maxLen
  }

  /**
   * Detect company duplicates using various matching strategies
   */
  static async detectCompanyDuplicates(
    companyId: number,
    criteria: CompanyMatchCriteria = {
      exactDomain: true,
      exactName: true,
      phoneMatch: true,
      nameSimilarity: 0.8,
      minimumConfidence: 0.7
    }
  ): Promise<DuplicateMatch[]> {
    const client = await pool.connect()
    const matches: DuplicateMatch[] = []

    try {
      // Get the company's normalized data
      const companyResult = await client.query(`
        SELECT c.*, cn.normalized_name, cn.normalized_domain, cn.phone_normalized, cn.name_tokens
        FROM companies c
        LEFT JOIN company_normalized_data cn ON c.company_id = cn.company_id
        WHERE c.company_id = $1
      `, [companyId])

      if (companyResult.rows.length === 0) {
        return matches
      }

      const company = companyResult.rows[0]

      // 1. Exact domain match (highest confidence)
      if (criteria.exactDomain && company.normalized_domain) {
        const domainMatches = await client.query(`
          SELECT c.*, cn.normalized_name, cn.normalized_domain, cn.phone_normalized
          FROM companies c
          JOIN company_normalized_data cn ON c.company_id = cn.company_id
          WHERE cn.normalized_domain = $1 AND c.company_id != $2
        `, [company.normalized_domain, companyId])

        for (const match of domainMatches.rows) {
          matches.push({
            primaryRecordId: companyId,
            duplicateRecordId: match.company_id,
            matchType: 'exact_domain',
            confidence: 0.95,
            matchDetails: {
              domain: company.normalized_domain,
              primaryName: company.company_name,
              duplicateName: match.company_name
            }
          })
        }
      }

      // 2. Exact name match
      if (criteria.exactName && company.normalized_name) {
        const nameMatches = await client.query(`
          SELECT c.*, cn.normalized_name, cn.normalized_domain, cn.phone_normalized
          FROM companies c
          JOIN company_normalized_data cn ON c.company_id = cn.company_id
          WHERE cn.normalized_name = $1 AND c.company_id != $2
        `, [company.normalized_name, companyId])

        for (const match of nameMatches.rows) {
          // Skip if already matched by domain
          if (matches.some(m => m.duplicateRecordId === match.company_id)) continue

          matches.push({
            primaryRecordId: companyId,
            duplicateRecordId: match.company_id,
            matchType: 'exact_name',
            confidence: 0.90,
            matchDetails: {
              name: company.normalized_name,
              primaryDomain: company.normalized_domain,
              duplicateDomain: match.normalized_domain
            }
          })
        }
      }

      // 3. Phone number match
      if (criteria.phoneMatch && company.phone_normalized) {
        const phoneMatches = await client.query(`
          SELECT c.*, cn.normalized_name, cn.normalized_domain, cn.phone_normalized
          FROM companies c
          JOIN company_normalized_data cn ON c.company_id = cn.company_id
          WHERE cn.phone_normalized = $1 AND c.company_id != $2
        `, [company.phone_normalized, companyId])

        for (const match of phoneMatches.rows) {
          // Skip if already matched
          if (matches.some(m => m.duplicateRecordId === match.company_id)) continue

          matches.push({
            primaryRecordId: companyId,
            duplicateRecordId: match.company_id,
            matchType: 'phone_match',
            confidence: 0.85,
            matchDetails: {
              phone: company.phone_normalized,
              primaryName: company.company_name,
              duplicateName: match.company_name
            }
          })
        }
      }

      // 4. Fuzzy name similarity
      if (criteria.nameSimilarity > 0 && company.normalized_name) {
        const similarMatches = await client.query(`
          SELECT c.*, cn.normalized_name, cn.normalized_domain, cn.phone_normalized
          FROM companies c
          JOIN company_normalized_data cn ON c.company_id = cn.company_id
          WHERE c.company_id != $1 AND cn.normalized_name IS NOT NULL
          LIMIT 1000
        `, [companyId])

        for (const match of similarMatches.rows) {
          // Skip if already matched
          if (matches.some(m => m.duplicateRecordId === match.company_id)) continue

          const similarity = this.calculateSimilarity(company.normalized_name, match.normalized_name)
          
          if (similarity >= criteria.nameSimilarity) {
            const confidence = Math.min(0.85, 0.6 + (similarity - criteria.nameSimilarity) * 0.5)
            
            matches.push({
              primaryRecordId: companyId,
              duplicateRecordId: match.company_id,
              matchType: 'similarity',
              confidence,
              matchDetails: {
                similarity,
                primaryName: company.company_name,
                duplicateName: match.company_name,
                normalizedPrimary: company.normalized_name,
                normalizedDuplicate: match.normalized_name
              }
            })
          }
        }
      }

      // Filter by minimum confidence
      return matches.filter(match => match.confidence >= criteria.minimumConfidence)

    } finally {
      client.release()
    }
  }

  /**
   * Detect contact duplicates using various matching strategies
   */
  static async detectContactDuplicates(
    contactId: number,
    criteria: ContactMatchCriteria = {
      exactEmail: true,
      linkedinMatch: true,
      emailDomainNameSimilarity: true,
      nameCompanyMatch: true,
      phoneMatch: true,
      minimumConfidence: 0.7
    }
  ): Promise<DuplicateMatch[]> {
    const client = await pool.connect()
    const matches: DuplicateMatch[] = []

    try {
      // Get the contact's normalized data
      const contactResult = await client.query(`
        SELECT c.*, cn.full_name_normalized, cn.email_normalized, cn.linkedin_handle, 
               cn.phone_normalized, cn.name_tokens, comp.company_name
        FROM contacts c
        LEFT JOIN contact_normalized_data cn ON c.contact_id = cn.contact_id
        LEFT JOIN companies comp ON c.company_id = comp.company_id
        WHERE c.contact_id = $1
      `, [contactId])

      if (contactResult.rows.length === 0) {
        return matches
      }

      const contact = contactResult.rows[0]

      // 1. Exact email match (highest confidence)
      if (criteria.exactEmail && contact.email_normalized) {
        const emailMatches = await client.query(`
          SELECT c.*, cn.full_name_normalized, cn.email_normalized, cn.linkedin_handle, 
                 cn.phone_normalized, comp.company_name
          FROM contacts c
          JOIN contact_normalized_data cn ON c.contact_id = cn.contact_id
          LEFT JOIN companies comp ON c.company_id = comp.company_id
          WHERE cn.email_normalized = $1 AND c.contact_id != $2
        `, [contact.email_normalized, contactId])

        for (const match of emailMatches.rows) {
          matches.push({
            primaryRecordId: contactId,
            duplicateRecordId: match.contact_id,
            matchType: 'exact_email',
            confidence: 0.98,
            matchDetails: {
              email: contact.email_normalized,
              primaryName: contact.full_name,
              duplicateName: match.full_name,
              primaryCompany: contact.company_name,
              duplicateCompany: match.company_name
            }
          })
        }
      }

      // 2. LinkedIn URL match
      if (criteria.linkedinMatch && contact.linkedin_handle) {
        const linkedinMatches = await client.query(`
          SELECT c.*, cn.full_name_normalized, cn.email_normalized, cn.linkedin_handle, 
                 cn.phone_normalized, comp.company_name
          FROM contacts c
          JOIN contact_normalized_data cn ON c.contact_id = cn.contact_id
          LEFT JOIN companies comp ON c.company_id = comp.company_id
          WHERE cn.linkedin_handle = $1 AND c.contact_id != $2
        `, [contact.linkedin_handle, contactId])

        for (const match of linkedinMatches.rows) {
          // Skip if already matched by email
          if (matches.some(m => m.duplicateRecordId === match.contact_id)) continue

          matches.push({
            primaryRecordId: contactId,
            duplicateRecordId: match.contact_id,
            matchType: 'linkedin_match',
            confidence: 0.95,
            matchDetails: {
              linkedinHandle: contact.linkedin_handle,
              primaryName: contact.full_name,
              duplicateName: match.full_name,
              primaryEmail: contact.email_normalized,
              duplicateEmail: match.email_normalized
            }
          })
        }
      }

      // 3. Phone number match
      if (criteria.phoneMatch && contact.phone_normalized) {
        const phoneMatches = await client.query(`
          SELECT c.*, cn.full_name_normalized, cn.email_normalized, cn.linkedin_handle, 
                 cn.phone_normalized, comp.company_name
          FROM contacts c
          JOIN contact_normalized_data cn ON c.contact_id = cn.contact_id
          LEFT JOIN companies comp ON c.company_id = comp.company_id
          WHERE cn.phone_normalized = $1 AND c.contact_id != $2
        `, [contact.phone_normalized, contactId])

        for (const match of phoneMatches.rows) {
          // Skip if already matched
          if (matches.some(m => m.duplicateRecordId === match.contact_id)) continue

          matches.push({
            primaryRecordId: contactId,
            duplicateRecordId: match.contact_id,
            matchType: 'phone_match',
            confidence: 0.75,
            matchDetails: {
              phone: contact.phone_normalized,
              primaryName: contact.full_name,
              duplicateName: match.full_name,
              primaryCompany: contact.company_name,
              duplicateCompany: match.company_name
            }
          })
        }
      }

      // Filter by minimum confidence
      return matches.filter(match => match.confidence >= criteria.minimumConfidence)

    } finally {
      client.release()
    }
  }

  /**
   * Store duplicate detection results in the database
   */
  static async storeDuplicateRecord(match: DuplicateMatch, recordType: 'company' | 'contact'): Promise<number> {
    const client = await pool.connect()

    try {
      const result = await client.query(`
        INSERT INTO duplicate_records (
          record_type, primary_record_id, duplicate_record_id, match_type,
          confidence_score, match_details, status, created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, 'pending', CURRENT_TIMESTAMP)
        ON CONFLICT (record_type, primary_record_id, duplicate_record_id) 
        DO UPDATE SET
          match_type = EXCLUDED.match_type,
          confidence_score = EXCLUDED.confidence_score,
          match_details = EXCLUDED.match_details,
          status = 'pending',
          created_at = CURRENT_TIMESTAMP
        RETURNING id
      `, [
        recordType,
        match.primaryRecordId,
        match.duplicateRecordId,
        match.matchType,
        match.confidence,
        JSON.stringify(match.matchDetails)
      ])

      return result.rows[0].id
    } finally {
      client.release()
    }
  }
}
