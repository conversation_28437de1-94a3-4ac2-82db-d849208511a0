#!/usr/bin/env tsx

/**
 * <PERSON><PERSON>t to run duplicate detection on all records
 * Run with: npx tsx src/scripts/runDuplicateDetection.ts
 */

import { DuplicateDetectionService } from '../lib/services/duplicateDetectionService'
import { DataNormalizationService } from '../lib/services/dataNormalizationService'
import { pool } from '../lib/db'

interface DetectionStats {
  companiesScanned: number
  contactsScanned: number
  duplicatesFound: number
  processingTime: number
}

async function runCompanyDuplicateDetection(): Promise<{ scanned: number; duplicatesFound: number }> {
  console.log('Starting company duplicate detection...')
  
  const client = await pool.connect()
  let scanned = 0
  let duplicatesFound = 0
  
  try {
    // Get all company IDs
    const result = await client.query('SELECT company_id FROM companies ORDER BY company_id')
    const companyIds = result.rows.map(row => row.company_id)
    
    console.log(`Found ${companyIds.length} companies to scan`)
    
    for (const companyId of companyIds) {
      try {
        const matches = await DuplicateDetectionService.detectCompanyDuplicates(companyId)
        
        for (const match of matches) {
          await DuplicateDetectionService.storeDuplicateRecord(match, 'company')
          duplicatesFound++
        }
        
        scanned++
        
        // Log progress every 50 records
        if (scanned % 50 === 0) {
          console.log(`  Scanned ${scanned}/${companyIds.length} companies, found ${duplicatesFound} duplicates`)
        }
      } catch (error) {
        console.error(`Error scanning company ${companyId}:`, error)
      }
    }
    
    console.log(`Company scan completed: ${scanned} scanned, ${duplicatesFound} duplicates found`)
    return { scanned, duplicatesFound }
    
  } finally {
    client.release()
  }
}

async function runContactDuplicateDetection(): Promise<{ scanned: number; duplicatesFound: number }> {
  console.log('Starting contact duplicate detection...')
  
  const client = await pool.connect()
  let scanned = 0
  let duplicatesFound = 0
  
  try {
    // Get all contact IDs
    const result = await client.query('SELECT contact_id FROM contacts ORDER BY contact_id')
    const contactIds = result.rows.map(row => row.contact_id)
    
    console.log(`Found ${contactIds.length} contacts to scan`)
    
    for (const contactId of contactIds) {
      try {
        const matches = await DuplicateDetectionService.detectContactDuplicates(contactId)
        
        for (const match of matches) {
          await DuplicateDetectionService.storeDuplicateRecord(match, 'contact')
          duplicatesFound++
        }
        
        scanned++
        
        // Log progress every 50 records
        if (scanned % 50 === 0) {
          console.log(`  Scanned ${scanned}/${contactIds.length} contacts, found ${duplicatesFound} duplicates`)
        }
      } catch (error) {
        console.error(`Error scanning contact ${contactId}:`, error)
      }
    }
    
    console.log(`Contact scan completed: ${scanned} scanned, ${duplicatesFound} duplicates found`)
    return { scanned, duplicatesFound }
    
  } finally {
    client.release()
  }
}

async function printDuplicateStats() {
  console.log('\n=== Duplicate Detection Statistics ===')
  
  const client = await pool.connect()
  
  try {
    // Get overall stats
    const statsResult = await client.query(`
      SELECT 
        record_type,
        status,
        match_type,
        COUNT(*) as count,
        AVG(confidence_score) as avg_confidence,
        MIN(confidence_score) as min_confidence,
        MAX(confidence_score) as max_confidence
      FROM duplicate_records 
      GROUP BY record_type, status, match_type
      ORDER BY record_type, status, match_type
    `)
    
    console.log('\nDetailed Statistics:')
    console.table(statsResult.rows)
    
    // Get summary by type
    const summaryResult = await client.query(`
      SELECT 
        record_type,
        COUNT(*) as total_duplicates,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN status = 'confirmed' THEN 1 END) as confirmed,
        COUNT(CASE WHEN status = 'false_positive' THEN 1 END) as false_positives,
        COUNT(CASE WHEN status = 'merged' THEN 1 END) as merged,
        AVG(confidence_score) as avg_confidence
      FROM duplicate_records 
      GROUP BY record_type
      ORDER BY record_type
    `)
    
    console.log('\nSummary by Record Type:')
    console.table(summaryResult.rows)
    
  } finally {
    client.release()
  }
}

async function main() {
  const startTime = Date.now()
  
  console.log('=== Duplicate Detection System ===')
  console.log('Starting comprehensive duplicate detection...\n')
  
  try {
    // Step 1: Normalize data first
    console.log('Step 1: Normalizing data...')
    const companyNormResults = await DataNormalizationService.normalizeAllCompanies(50)
    const contactNormResults = await DataNormalizationService.normalizeAllContacts(50)
    
    console.log(`Normalization completed:`)
    console.log(`  Companies: ${companyNormResults.processed} processed, ${companyNormResults.errors} errors`)
    console.log(`  Contacts: ${contactNormResults.processed} processed, ${contactNormResults.errors} errors`)
    
    // Step 2: Run duplicate detection
    console.log('\nStep 2: Running duplicate detection...')
    
    const companyResults = await runCompanyDuplicateDetection()
    const contactResults = await runContactDuplicateDetection()
    
    // Step 3: Print statistics
    await printDuplicateStats()
    
    const totalTime = Date.now() - startTime
    
    console.log('\n=== Final Summary ===')
    console.log(`Total processing time: ${totalTime}ms (${(totalTime / 1000).toFixed(2)}s)`)
    console.log(`Companies scanned: ${companyResults.scanned}`)
    console.log(`Contacts scanned: ${contactResults.scanned}`)
    console.log(`Total duplicates found: ${companyResults.duplicatesFound + contactResults.duplicatesFound}`)
    console.log(`  Company duplicates: ${companyResults.duplicatesFound}`)
    console.log(`  Contact duplicates: ${contactResults.duplicatesFound}`)
    
    console.log('\nDuplicate detection completed successfully!')
    console.log('You can now view and manage duplicates in the dashboard at /dashboard/duplicates')
    
  } catch (error) {
    console.error('Error during duplicate detection:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}
