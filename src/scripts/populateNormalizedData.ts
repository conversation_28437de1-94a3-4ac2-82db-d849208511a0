#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to populate normalized data tables for duplicate detection
 *
 * Usage:
 *   npx tsx src/scripts/populateNormalizedData.ts           # Incremental update
 *   npx tsx src/scripts/populateNormalizedData.ts --clean   # Clean and rebuild all
 *   npx tsx src/scripts/populateNormalizedData.ts -c        # Clean and rebuild all
 */

import { DataNormalizationService } from '../lib/services/dataNormalizationService'

async function main() {
  console.log('Starting data normalization...')

  // Check command line arguments for clean option
  const shouldClean = process.argv.includes('--clean') || process.argv.includes('-c')

  if (shouldClean) {
    console.log('\n🧹 CLEANING NORMALIZED TABLES...')
    await cleanNormalizedTables()
  }

  try {
    // Normalize companies
    console.log('\n=== Normalizing Companies ===')
    const companyResults = await DataNormalizationService.normalizeAllCompanies(50)
    console.log(`Companies processed: ${companyResults.processed}`)
    console.log(`Companies with errors: ${companyResults.errors}`)

    // Normalize contacts
    console.log('\n=== Normalizing Contacts ===')
    const contactResults = await DataNormalizationService.normalizeAllContacts(50)
    console.log(`Contacts processed: ${contactResults.processed}`)
    console.log(`Contacts with errors: ${contactResults.errors}`)

    console.log('\n=== Summary ===')
    console.log(`Total records processed: ${companyResults.processed + contactResults.processed}`)
    console.log(`Total errors: ${companyResults.errors + contactResults.errors}`)
    console.log('Data normalization completed!')

  } catch (error) {
    console.error('Error during normalization:', error)
    process.exit(1)
  }
}

async function cleanNormalizedTables() {
  const { pool } = await import('../lib/db')
  const client = await pool.connect()

  try {
    console.log('Truncating company_normalized_data...')
    await client.query('TRUNCATE TABLE company_normalized_data')

    console.log('Truncating contact_normalized_data...')
    await client.query('TRUNCATE TABLE contact_normalized_data')

    console.log('Truncating duplicate_records...')
    await client.query('TRUNCATE TABLE duplicate_records')

    console.log('✅ All normalized tables cleaned!')

  } catch (error) {
    console.error('Error cleaning tables:', error)
    throw error
  } finally {
    client.release()
  }
}

// Run the script if executed directly
main().catch(console.error)
