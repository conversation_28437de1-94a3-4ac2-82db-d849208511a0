#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to populate normalized data tables for duplicate detection
 * Run with: npx tsx src/scripts/populateNormalizedData.ts
 */

import { DataNormalizationService } from '../lib/services/dataNormalizationService'

async function main() {
  console.log('Starting data normalization...')
  
  try {
    // Normalize companies
    console.log('\n=== Normalizing Companies ===')
    const companyResults = await DataNormalizationService.normalizeAllCompanies(50)
    console.log(`Companies processed: ${companyResults.processed}`)
    console.log(`Companies with errors: ${companyResults.errors}`)
    
    // Normalize contacts
    console.log('\n=== Normalizing Contacts ===')
    const contactResults = await DataNormalizationService.normalizeAllContacts(50)
    console.log(`Contacts processed: ${contactResults.processed}`)
    console.log(`Contacts with errors: ${contactResults.errors}`)
    
    console.log('\n=== Summary ===')
    console.log(`Total records processed: ${companyResults.processed + contactResults.processed}`)
    console.log(`Total errors: ${companyResults.errors + contactResults.errors}`)
    console.log('Data normalization completed!')
    
  } catch (error) {
    console.error('Error during normalization:', error)
    process.exit(1)
  }
}

// Run the script if executed directly
main().catch(console.error)
