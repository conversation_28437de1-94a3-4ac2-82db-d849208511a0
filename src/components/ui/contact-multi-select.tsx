"use client";

import React, { useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { User, X, Search, ChevronDown, Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import ContactCreationModal from "@/components/dashboard/deals/ContactCreationModal";
import { toast } from "sonner";

interface Contact {
  contact_id: number;
  first_name: string | null;
  last_name: string | null;
  email: string | null;
  personal_email: string | null;
  company: string | null;
  title: string | null;
}

interface ContactMultiSelectProps {
  selectedContacts: Contact[];
  onContactsChange: (contacts: Contact[]) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  onCreateNewContact?: (email: string) => void;
  mode?: 'standalone' | 'deal-attachment';
  dealId?: string;
  dealVersion?: 'v1' | 'v2';
  onContactAddedToDeal?: (contact: Contact) => void;
}

export function ContactMultiSelect({
  selectedContacts,
  onContactsChange,
  placeholder = "Search for contacts...",
  className,
  disabled = false,
  onCreateNewContact,
  mode = 'standalone',
  dealId,
  dealVersion = 'v1',
  onContactAddedToDeal,
}: ContactMultiSelectProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [suggestions, setSuggestions] = useState<Contact[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [showContactModal, setShowContactModal] = useState(false);
  const [contactModalData, setContactModalData] = useState<{ email?: string }>({});

  // Search contacts by name, email, title, or company
  const searchContacts = async (searchQuery: string) => {
    if (searchQuery.length < 2) {
      setSuggestions([]);
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/contacts/search-deals?q=${encodeURIComponent(searchQuery)}&limit=10`
      );
      if (response.ok) {
        const contacts = await response.json();
        // Filter out already selected contacts
        const filteredContacts = contacts.filter(
          (contact: Contact) =>
            !selectedContacts.some(
              (selected) => selected.contact_id === contact.contact_id
            )
        );
        setSuggestions(filteredContacts);
      }
    } catch (error) {
      console.error("Error searching contacts:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchContacts(searchTerm);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, selectedContacts]);

  // Handle contact selection
  const handleContactSelect = (contact: Contact) => {
    onContactsChange([...selectedContacts, contact]);
    setSearchTerm("");
    setSuggestions([]);
    inputRef.current?.focus();
  };

  // Handle contact removal
  const handleContactRemove = (contactId: number) => {
    onContactsChange(
      selectedContacts.filter((contact) => contact.contact_id !== contactId)
    );
  };

  // Handle input focus
  const handleInputFocus = () => {
    setIsOpen(true);
    if (searchTerm.length >= 2) {
      searchContacts(searchTerm);
    }
  };

  // Handle input blur
  const handleInputBlur = () => {
    // Delay closing to allow for clicks on suggestions
    setTimeout(() => {
      setIsOpen(false);
    }, 200);
  };

  // Handle key navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Backspace" && searchTerm === "" && selectedContacts.length > 0) {
      // Remove last contact on backspace when input is empty
      const lastContact = selectedContacts[selectedContacts.length - 1];
      handleContactRemove(lastContact.contact_id);
    }
    
    // Handle Enter key for creating new contact
    if (e.key === "Enter" && searchTerm.trim()) {
      e.preventDefault();
      
      // Check if search term looks like an email
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const isEmail = emailRegex.test(searchTerm.trim());
      
      if (isEmail && suggestions.length === 0 && !isLoading) {
        // If it's an email and no suggestions found, open contact creation modal
        setContactModalData({ email: searchTerm.trim() });
        setShowContactModal(true);
        setSearchTerm("");
        setIsOpen(false);
      }
    }
  };

  // Click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className={cn("space-y-3", className)}>
      <div className="relative" ref={containerRef}>
        <div className="min-h-[40px] border border-input bg-background rounded-md px-3 py-2 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2">
          <div className="flex flex-wrap gap-1">
            {/* Selected contact chips */}
            {selectedContacts.map((contact) => (
              <Badge
                key={contact.contact_id}
                variant="secondary"
                className="flex items-center gap-1 px-2 py-1 text-xs"
              >
                <User className="h-3 w-3" />
                <span>
                  {contact.first_name} {contact.last_name}
                </span>
                <button
                  type="button"
                  className="ml-1 hover:text-red-600 transition-colors"
                  onClick={() => handleContactRemove(contact.contact_id)}
                  disabled={disabled}
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}

            {/* Search input */}
            <div className="flex-1 min-w-[120px]">
              <Input
                ref={inputRef}
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onFocus={handleInputFocus}
                onBlur={handleInputBlur}
                onKeyDown={handleKeyDown}
                placeholder={
                  selectedContacts.length === 0 ? placeholder : "Add more contacts..."
                }
                className="border-0 p-0 h-auto text-sm focus-visible:ring-0 focus-visible:ring-offset-0"
                disabled={disabled}
              />
            </div>
          </div>
        </div>

        {/* Dropdown with suggestions */}
        {isOpen && (suggestions.length > 0 || isLoading) && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
            {isLoading ? (
              <div className="px-4 py-2 text-sm text-gray-500">
                Searching contacts...
              </div>
            ) : suggestions.length > 0 ? (
              suggestions.map((contact) => (
                <div
                  key={contact.contact_id}
                  className="px-4 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                  onClick={() => handleContactSelect(contact)}
                >
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-gray-400" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">
                        {contact.first_name} {contact.last_name}
                      </div>
                      <div className="text-sm text-gray-600">
                        {contact.email || contact.personal_email}
                      </div>
                      {contact.company && (
                        <div className="text-xs text-gray-500">
                          {contact.company}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            ) : searchTerm.length >= 2 ? (
              <div className="px-4 py-2">
                {/* Check if search term looks like an email */}
                {(() => {
                  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                  const isEmail = emailRegex.test(searchTerm.trim());
                  
                  if (isEmail) {
                    return (
                      <div 
                        className="flex items-center gap-2 hover:bg-gray-50 cursor-pointer py-2 px-2 rounded"
                        onClick={() => {
                          setContactModalData({ email: searchTerm.trim() });
                          setShowContactModal(true);
                          setSearchTerm("");
                          setIsOpen(false);
                        }}
                      >
                        <User className="h-4 w-4 text-green-600" />
                        <div className="flex-1">
                          <div className="font-medium text-green-700">
                            Create new contact
                          </div>
                          <div className="text-sm text-gray-600">
                            {searchTerm.trim()}
                          </div>
                        </div>
                      </div>
                    );
                  } else {
                    return (
                      <div className="text-sm text-gray-500">
                        No contacts found
                      </div>
                    );
                  }
                })()}
              </div>
            ) : null}
          </div>
        )}

        {/* Dropdown arrow */}
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
          <ChevronDown className="h-4 w-4 text-gray-400" />
        </div>
      </div>

      {/* Create New Contact Button */}
      <div className="flex items-center justify-center">
        <ContactCreationModal
          onContactCreated={async (newContact) => {
            if (mode === 'deal-attachment' && dealId) {
              // Add contact to deal
              try {
                const apiEndpoint = dealVersion === 'v2' 
                  ? `/api/v2/deals/${dealId}/contacts`
                  : `/api/deals/${dealId}/contacts`;
                
                console.log(`Adding contact to ${dealVersion} deal using endpoint:`, apiEndpoint);
                
                const response = await fetch(apiEndpoint, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    contactIds: [newContact.contact_id]
                  }),
                });

                if (response.ok) {
                  toast.success('Contact created and added to deal!');
                  onContactAddedToDeal?.(newContact);
                } else {
                  const error = await response.json();
                  console.error('Failed to add contact to deal:', error);
                  toast.error('Contact created but failed to add to deal. Please try again.');
                }
              } catch (error) {
                console.error('Error adding contact to deal:', error);
                toast.error('Contact created but failed to add to deal. Please try again.');
              }
            } else {
              // Just add to multi-select
              // The API only returns contact_id, so we need to fetch the full contact data
              try {
                const contactResponse = await fetch(`/api/contacts/${newContact.contact_id}`);
                if (contactResponse.ok) {
                  const fullContactData = await contactResponse.json();
                  
                  // Ensure the contact object has the correct structure
                  const contactToAdd: Contact = {
                    contact_id: fullContactData.contact_id,
                    first_name: fullContactData.first_name || 'Unknown',
                    last_name: fullContactData.last_name || 'Contact',
                    email: fullContactData.email || fullContactData.personal_email || '',
                    personal_email: fullContactData.personal_email || fullContactData.email || '',
                    company: fullContactData.company_name || fullContactData.company || '',
                    title: fullContactData.title || fullContactData.job_title || '',
                  };
                  
                  onContactsChange([...selectedContacts, contactToAdd]);
                  
                  // Create a display name for the toast
                  const displayName = [contactToAdd.first_name, contactToAdd.last_name]
                    .filter(Boolean)
                    .join(' ') || 'New Contact';
                  
                  toast.success(`Contact "${displayName}" created.`);
                } else {
                  console.error('Failed to fetch contact data:', contactResponse.status);
                  toast.error('Contact created but failed to load details. Please refresh.');
                }
              } catch (error) {
                console.error('Error fetching contact data:', error);
                toast.error('Contact created but failed to load details. Please refresh.');
              }
            }
            setShowContactModal(false);
            setContactModalData({});
          }}
          initialData={contactModalData}
          open={showContactModal}
          onOpenChange={setShowContactModal}
          trigger={
            <Button variant="outline" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create New Contact
            </Button>
          }
        />
      </div>

    </div>
  );
} 