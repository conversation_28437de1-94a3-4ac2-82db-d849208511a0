'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  AlertTriangle, 
  Building2, 
  User, 
  Calendar, 
  Filter,
  Settings,
  CheckCircle,
  XCircle,
  RefreshCw,
  Download,
  Trash2,
  Search,
  Play,
  Merge
} from 'lucide-react'
import { DuplicateRecord, DuplicateStatus, DuplicateRecordType } from '@/types/duplicate'
import DuplicateResolutionModal from './DuplicateResolutionModal'

interface DuplicateDashboardProps {
  initialType?: 'company' | 'contact'
}

interface DuplicateWithDetails extends DuplicateRecord {
  primary_name: string
  duplicate_name: string
  primary_data?: any
  duplicate_data?: any
}

interface PaginationInfo {
  page: number
  pageSize: number
  total: number
  totalPages: number
}

export default function DuplicateDashboard({ initialType }: DuplicateDashboardProps) {
  const [duplicates, setDuplicates] = useState<DuplicateWithDetails[]>([])
  const [loading, setLoading] = useState(false)
  const [scanning, setScanning] = useState(false)
  const [selectedDuplicates, setSelectedDuplicates] = useState<Set<number>>(new Set())
  const [selectedDuplicate, setSelectedDuplicate] = useState<DuplicateWithDetails | null>(null)
  const [showResolutionModal, setShowResolutionModal] = useState(false)
  const [filter, setFilter] = useState<DuplicateRecordType | 'all'>(initialType || 'all')
  const [statusFilter, setStatusFilter] = useState<DuplicateStatus>('pending')
  const [confidenceFilter, setConfidenceFilter] = useState({ min: 0, max: 1 })
  const [matchTypeFilter, setMatchTypeFilter] = useState<string>('')
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0
  })
  const [stats, setStats] = useState<any[]>([])

  useEffect(() => {
    fetchDuplicates()
  }, [pagination.page, pagination.pageSize, filter, statusFilter, confidenceFilter, matchTypeFilter])

  const fetchDuplicates = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        pageSize: pagination.pageSize.toString(),
        status: statusFilter
      })
      
      if (filter !== 'all') {
        params.append('type', filter)
      }
      
      if (confidenceFilter.min > 0) {
        params.append('minConfidence', confidenceFilter.min.toString())
      }
      
      if (confidenceFilter.max < 1) {
        params.append('maxConfidence', confidenceFilter.max.toString())
      }
      
      if (matchTypeFilter) {
        params.append('matchType', matchTypeFilter)
      }

      const response = await fetch(`/api/duplicates?${params}`)
      const data = await response.json()

      if (data.success) {
        setDuplicates(data.data)
        setPagination(data.pagination)
        setStats(data.stats)
      }
    } catch (error) {
      console.error('Error fetching duplicates:', error)
    } finally {
      setLoading(false)
    }
  }

  const startScan = async (type: 'company' | 'contact' | 'both' = 'both') => {
    setScanning(true)
    try {
      const response = await fetch('/api/duplicates/scan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type, normalizeFirst: true })
      })
      
      const data = await response.json()
      
      if (data.success) {
        await fetchDuplicates() // Refresh the list
        alert(`Scan completed! Found ${data.stats.duplicatesFound} duplicates in ${data.stats.processingTime}ms`)
      } else {
        alert('Scan failed: ' + data.error)
      }
    } catch (error) {
      console.error('Error starting scan:', error)
      alert('Error starting scan')
    } finally {
      setScanning(false)
    }
  }

  const handleDuplicateSelect = (duplicateId: number, checked: boolean) => {
    const newSelected = new Set(selectedDuplicates)
    if (checked) {
      newSelected.add(duplicateId)
    } else {
      newSelected.delete(duplicateId)
    }
    setSelectedDuplicates(newSelected)
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedDuplicates(new Set(duplicates.map(d => d.id)))
    } else {
      setSelectedDuplicates(new Set())
    }
  }

  const handleBatchAction = async (action: 'mark_false_positive' | 'keep_separate') => {
    if (selectedDuplicates.size === 0) return

    try {
      const promises = Array.from(selectedDuplicates).map(duplicateId =>
        fetch('/api/duplicates', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            duplicateId,
            status: action === 'mark_false_positive' ? 'false_positive' : 'confirmed',
            resolvedBy: 'user' // You might want to get this from auth context
          })
        })
      )

      await Promise.all(promises)
      setSelectedDuplicates(new Set())
      await fetchDuplicates()
    } catch (error) {
      console.error('Error in batch action:', error)
    }
  }

  const openResolutionModal = (duplicate: DuplicateWithDetails) => {
    setSelectedDuplicate(duplicate)
    setShowResolutionModal(true)
  }

  const handleResolutionComplete = async () => {
    setShowResolutionModal(false)
    setSelectedDuplicate(null)
    await fetchDuplicates()
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.9) return 'bg-red-100 text-red-800'
    if (confidence >= 0.8) return 'bg-orange-100 text-orange-800'
    if (confidence >= 0.7) return 'bg-yellow-100 text-yellow-800'
    return 'bg-gray-100 text-gray-800'
  }

  const getMatchTypeLabel = (matchType: string) => {
    const labels: Record<string, string> = {
      exact_domain: 'Exact Domain',
      exact_name: 'Exact Name',
      exact_email: 'Exact Email',
      linkedin_match: 'LinkedIn',
      phone_match: 'Phone',
      similarity: 'Name Similarity',
      email_domain_name: 'Email Domain + Name',
      name_company: 'Name + Company'
    }
    return labels[matchType] || matchType
  }

  const totalDuplicates = stats.reduce((sum, stat) => sum + parseInt(stat.count), 0)
  const companyDuplicates = stats.filter(s => s.record_type === 'company').reduce((sum, stat) => sum + parseInt(stat.count), 0)
  const contactDuplicates = stats.filter(s => s.record_type === 'contact').reduce((sum, stat) => sum + parseInt(stat.count), 0)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Duplicate Management</h2>
          <p className="text-muted-foreground">
            Detect and resolve duplicate companies and contacts
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => fetchDuplicates()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button 
            onClick={() => startScan('both')} 
            disabled={scanning}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {scanning ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Scanning...
              </>
            ) : (
              <>
                <Search className="h-4 w-4 mr-2" />
                Start Scan
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Duplicates</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalDuplicates}</div>
            <p className="text-xs text-muted-foreground">
              Pending resolution
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Company Duplicates</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{companyDuplicates}</div>
            <p className="text-xs text-muted-foreground">
              Companies with duplicates
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Contact Duplicates</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{contactDuplicates}</div>
            <p className="text-xs text-muted-foreground">
              Contacts with duplicates
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Record Type</label>
              <Select value={filter} onValueChange={(value) => setFilter(value as any)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="company">Companies</SelectItem>
                  <SelectItem value="contact">Contacts</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Status</label>
              <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as DuplicateStatus)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="false_positive">False Positive</SelectItem>
                  <SelectItem value="merged">Merged</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Min Confidence</label>
              <Input
                type="number"
                min="0"
                max="1"
                step="0.1"
                value={confidenceFilter.min}
                onChange={(e) => setConfidenceFilter(prev => ({ ...prev, min: parseFloat(e.target.value) || 0 }))}
              />
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">Match Type</label>
              <Select value={matchTypeFilter} onValueChange={setMatchTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All match types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Types</SelectItem>
                  <SelectItem value="exact_domain">Exact Domain</SelectItem>
                  <SelectItem value="exact_name">Exact Name</SelectItem>
                  <SelectItem value="exact_email">Exact Email</SelectItem>
                  <SelectItem value="linkedin_match">LinkedIn</SelectItem>
                  <SelectItem value="phone_match">Phone</SelectItem>
                  <SelectItem value="similarity">Similarity</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Batch Actions */}
      {selectedDuplicates.size > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                {selectedDuplicates.size} duplicate{selectedDuplicates.size > 1 ? 's' : ''} selected
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => handleBatchAction('keep_separate')}
                  size="sm"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Keep Separate
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleBatchAction('mark_false_positive')}
                  size="sm"
                >
                  <XCircle className="h-4 w-4 mr-2" />
                  Mark False Positive
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Duplicates List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Duplicate Records</CardTitle>
            <div className="flex items-center gap-2">
              <Checkbox
                checked={selectedDuplicates.size === duplicates.length && duplicates.length > 0}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm text-muted-foreground">Select All</span>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : duplicates.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No duplicates found. Try running a scan or adjusting your filters.
            </div>
          ) : (
            <div className="space-y-4">
              {duplicates.map((duplicate) => (
                <div
                  key={duplicate.id}
                  className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      <Checkbox
                        checked={selectedDuplicates.has(duplicate.id)}
                        onCheckedChange={(checked) => handleDuplicateSelect(duplicate.id, checked as boolean)}
                      />
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          {duplicate.record_type === 'company' ? (
                            <Building2 className="h-4 w-4 text-blue-600" />
                          ) : (
                            <User className="h-4 w-4 text-green-600" />
                          )}
                          <span className="font-medium">
                            {duplicate.primary_name} ↔ {duplicate.duplicate_name}
                          </span>
                          <Badge className={getConfidenceColor(duplicate.confidence_score)}>
                            {Math.round(duplicate.confidence_score * 100)}% confidence
                          </Badge>
                          <Badge variant="outline">
                            {getMatchTypeLabel(duplicate.match_type)}
                          </Badge>
                        </div>

                        <div className="text-sm text-muted-foreground">
                          <div>Primary: {duplicate.primary_name}</div>
                          <div>Duplicate: {duplicate.duplicate_name}</div>
                          {duplicate.match_details && (
                            <div className="mt-1">
                              Match details: {JSON.stringify(duplicate.match_details, null, 2).slice(0, 100)}...
                            </div>
                          )}
                        </div>

                        <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                          <span>Created: {new Date(duplicate.created_at).toLocaleDateString()}</span>
                          <span>Status: {duplicate.status}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openResolutionModal(duplicate)}
                      >
                        <Merge className="h-4 w-4 mr-2" />
                        Resolve
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-muted-foreground">
                Showing {((pagination.page - 1) * pagination.pageSize) + 1} to{' '}
                {Math.min(pagination.page * pagination.pageSize, pagination.total)} of{' '}
                {pagination.total} results
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                  disabled={pagination.page === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                  disabled={pagination.page === pagination.totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Resolution Modal */}
      {selectedDuplicate && (
        <DuplicateResolutionModal
          isOpen={showResolutionModal}
          onClose={() => setShowResolutionModal(false)}
          duplicate={selectedDuplicate}
          onResolve={handleResolutionComplete}
        />
      )}
    </div>
  )
}
