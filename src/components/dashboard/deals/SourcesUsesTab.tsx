import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Square, Plus, Trash2 } from "lucide-react";
import { DealNsfFieldV2 } from "./shared/types-v2";
import { ConflictIndicator } from "./ConflictIndicator";
import { toast } from "sonner";

interface SourcesUsesTabProps {
  nsfFields: DealNsfFieldV2[];
  isEditing: boolean;
  isNsfSyncing: boolean;
  onNsfFieldChange: (id: string, field: string, value: any) => void;
  hasConflict: (fieldName: string) => boolean;
  getConflictData: (fieldName: string) => any;
  hasNsfConflict: (context: string, type: string, field: string) => boolean;
  getNsfConflictData: (context: string, type: string, field: string) => any;
  formatNumber: (value: number | null, unit?: string) => string;
  formatRawCurrency: (value: number | null) => string;
  formatPercentage: (value: number | null) => string;
  getTotalNsf: (fields: DealNsfFieldV2[]) => number;
  calculateAmountByContext: (fields: DealNsfFieldV2[], context: string) => number;
  dealId?: number;
  property?: {
    gsfGrossSquareFoot?: number;
    zfaZoningFloorArea?: number;
    totalNsfNetSquareFoot?: number;
  };
  centralMappings?: any;
  isLoadingMappings?: boolean;
  onPropertyFieldChange?: (field: string, value: any) => void;
  onRefreshData?: () => Promise<void>; // New prop for refreshing data
}

const SourcesUsesTab: React.FC<SourcesUsesTabProps> = ({
  nsfFields,
  isEditing,
  isNsfSyncing,
  onNsfFieldChange,
  hasConflict,
  getConflictData,
  hasNsfConflict,
  getNsfConflictData,
  formatNumber,
  formatRawCurrency,
  formatPercentage,
  getTotalNsf,
  calculateAmountByContext,
  dealId,
  property,
  centralMappings,
  isLoadingMappings,
  onPropertyFieldChange,
  onRefreshData,
}) => {
  const [isAddingSource, setIsAddingSource] = useState(false);
  const [isAddingUse, setIsAddingUse] = useState(false);
  const [isDeleting, setIsDeleting] = useState<number | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [percentageInputs, setPercentageInputs] = useState<{ [key: string]: string }>({});
  
  // Batch adding states
  const [batchSources, setBatchSources] = useState<Array<{
    id: string;
    sourceType: string;
    amount: number;
    percentage: string;
    amountPerGsf?: number;
    amountPerNsf?: number;
    amountPerZfa?: number;
    calculatedPercentage?: number;
  }>>([]);
  
  const [batchUses, setBatchUses] = useState<Array<{
    id: string;
    useType: string;
    amount: number;
    percentage: string;
    amountPerGsf?: number;
    amountPerNsf?: number;
    amountPerZfa?: number;
    calculatedPercentage?: number;
  }>>([]);
  
  const [newSourceData, setNewSourceData] = useState<{
    sourceType: string;
    amount: number;
    percentage: string;
  }>({
    sourceType: '',
    amount: 0,
    percentage: ''
  });
  
  const [newUseData, setNewUseData] = useState<{
    useType: string;
    amount: number;
    percentage: string;
  }>({
    useType: '',
    amount: 0,
    percentage: ''
  });



  // Utility function to calculate per-square-foot values in real-time
  const calculatePerSqFtValues = (amount: number) => {
    const roundTo2 = (num: number) => Math.round(num * 100) / 100;
    
    return {
      amountPerGsf: property?.gsfGrossSquareFoot ? roundTo2(amount / property.gsfGrossSquareFoot) : 0,
      amountPerNsf: property?.totalNsfNetSquareFoot ? roundTo2(amount / property.totalNsfNetSquareFoot) : 0,
      amountPerZfa: property?.zfaZoningFloorArea ? roundTo2(amount / property.zfaZoningFloorArea) : 0
    };
  };
  
  // Calculate real-time percentage for sources/uses (with Common Equity exclusion logic)
  const calculateRealTimePercentage = (amount: number, context: 'sources' | 'uses_total', newSourceType?: string) => {
    if (context === 'sources') {
      // Get all source fields (existing + batch + new)
      const allSources = [
        ...nsfFields.filter(nsf => nsf.nsfContext === 'sources'),
        ...batchSources.map(s => ({ sourceType: s.sourceType, amount: s.amount })),
        ...(newSourceType ? [{ sourceType: newSourceType, amount }] : [])
      ];
      
      // Check if LP or GP are present
      const hasLP = allSources.some(nsf => 
        nsf.sourceType === 'Limited Partner (LP)' || 
        nsf.sourceType === 'LP'
      );
      const hasGP = allSources.some(nsf => 
        nsf.sourceType === 'General Partner (GP)' || 
        nsf.sourceType === 'GP'
      );
      
      // Calculate total sources (excluding Common Equity if LP/GP present)
      const totalSources = allSources.reduce((sum, nsf) => {
        const sourceAmount = parseFloat(nsf.amount?.toString() || '0') || 0;
        const isCommonEquity = nsf.sourceType === 'Common Equity';
        
        // If LP/GP are present, exclude Common Equity from total
        if ((hasLP || hasGP) && isCommonEquity) {
          return sum; // Don't add Common Equity to total
        }
        
        return sum + sourceAmount;
      }, 0);
      
      return totalSources > 0 ? ((amount / totalSources) * 100) : 0;
    } else {
      // For uses, use simple calculation (no exclusion logic needed)
      const existingTotal = nsfFields
        .filter(nsf => nsf.nsfContext === context)
        .reduce((sum, nsf) => sum + (nsf.amount || 0), 0);
      
      const batchTotal = batchUses.reduce((sum, use) => sum + (use.amount || 0), 0);
      const totalAmount = existingTotal + batchTotal + amount;
      
      return totalAmount > 0 ? ((amount / totalAmount) * 100) : 0;
    }
  };

  // Add source to batch (without saving)
  const handleAddSourceToBatch = () => {
    if (!newSourceData.sourceType || !newSourceData.amount) return;
    
    const perSqFtValues = calculatePerSqFtValues(newSourceData.amount);
    const autoPercentage = calculateRealTimePercentage(newSourceData.amount, 'sources', newSourceData.sourceType);
    
    const newBatchSource = {
      id: `temp-source-${Date.now()}`,
      ...newSourceData,
      ...perSqFtValues,
      calculatedPercentage: autoPercentage
    };
    
    setBatchSources(prev => [...prev, newBatchSource]);
    setNewSourceData({ sourceType: '', amount: 0, percentage: '' });
  };
  
  // Add use to batch (without saving)
  const handleAddUseToBatch = () => {
    if (!newUseData.useType || !newUseData.amount) return;
    
    const perSqFtValues = calculatePerSqFtValues(newUseData.amount);
    const autoPercentage = calculateRealTimePercentage(newUseData.amount, 'uses_total');
    
    const newBatchUse = {
      id: `temp-use-${Date.now()}`,
      ...newUseData,
      ...perSqFtValues,
      calculatedPercentage: autoPercentage
    };
    
    setBatchUses(prev => [...prev, newBatchUse]);
    setNewUseData({ useType: '', amount: 0, percentage: '' });
  };
  
  // Remove from batch
  const removeBatchSource = (id: string) => {
    setBatchSources(prev => prev.filter(source => source.id !== id));
  };
  
  const removeBatchUse = (id: string) => {
    setBatchUses(prev => prev.filter(use => use.id !== id));
  };
  
  // Save all batch sources and uses
  const handleSaveBatch = async () => {
    if (!dealId) return;
    
    try {
      const allItems = [
        ...batchSources.map(source => ({
          dealId,
          nsfContext: 'sources',
          dealType: 'sources',
          sourceType: source.sourceType,
          amount: source.amount || 0,
          amountPerGsf: source.amountPerGsf || 0,
          amountPerNsf: source.amountPerNsf || 0,
          amountPerZfa: source.amountPerZfa || 0,
          percentageOfTotal: source.percentage ? parseFloat(source.percentage) / 100 : 0,
          isRequired: false,
          additionalInfo: {}
        })),
        ...batchUses.map(use => ({
          dealId,
          nsfContext: 'uses_total',
          dealType: 'uses_total',
          useType: use.useType,
          amount: use.amount || 0,
          amountPerGsf: use.amountPerGsf || 0,
          amountPerNsf: use.amountPerNsf || 0,
          amountPerZfa: use.amountPerZfa || 0,
          percentageOfTotal: use.percentage ? parseFloat(use.percentage) / 100 : 0,
          isRequired: false,
          additionalInfo: {}
        }))
      ];
      
      if (allItems.length === 0) return;
      
      const response = await fetch(`/api/v2/deals/${dealId}/nsf-fields/batch`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ items: allItems })
      });

      if (response.ok) {
        toast.success(`Successfully added ${batchSources.length} sources and ${batchUses.length} uses`);
        setBatchSources([]);
        setBatchUses([]);
        setIsAddingSource(false);
        setIsAddingUse(false);
        
        if (onRefreshData) {
          setIsRefreshing(true);
          try {
            await onRefreshData();
          } finally {
            setIsRefreshing(false);
          }
        }
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Failed to save batch');
      }
    } catch (error) {
      console.error('Error saving batch:', error);
      toast.error('Error saving batch');
    }
  };

  // Separate sources and uses
  const sources = nsfFields.filter(field => field.sourceType || field.nsfContext === 'sources');
  const uses = nsfFields.filter(field => field.useType || field.nsfContext === 'uses_total');

    // Add new source
  const handleAddSource = () => {
    setNewSourceData({
      sourceType: '',
      amount: 0,
      percentage: ''
    });
    setIsAddingSource(true);
  };

  // Save new source
  const handleSaveNewSource = async (sourceData: {
    sourceType: string;
    amount: number;
    percentage: string;
  }) => {
    if (!dealId) return;
    
    try {
      const newSource: Partial<DealNsfFieldV2> = {
        id: -1, // Temporary ID for new items
        dealId,
        nsfContext: 'sources',
        dealType: 'sources', // Automatically set deal type based on context
        sourceType: sourceData.sourceType,
        amount: sourceData.amount || 0,
        amountPerGsf: 0,
        amountPerNsf: 0,
        amountPerZfa: 0,
        percentageOfTotal: sourceData.percentage ? parseFloat(sourceData.percentage) / 100 : 0,
        isRequired: false,
        additionalInfo: {}
      };

      const response = await fetch(`/api/v2/deals/${dealId}/nsf-fields`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newSource)
      });

      if (response.ok) {
        toast.success('Source added successfully');
        // Reset form and refresh data
        setIsAddingSource(false);
        setNewSourceData({ sourceType: '', amount: 0, percentage: '' });
        if (onRefreshData) {
          setIsRefreshing(true);
          try {
            await onRefreshData();
          } finally {
            setIsRefreshing(false);
          }
        }
      } else {
        const errorData = await response.json();
        console.error('Failed to add source:', errorData.error);
        toast.error(errorData.error || 'Failed to add source');
      }
    } catch (error) {
      console.error('Error adding source:', error);
    }
  };

  // Cancel adding source
  const handleCancelAddSource = () => {
    setIsAddingSource(false);
  };

  // Save new use
  const handleSaveNewUse = async (useData: {
    useType: string;
    amount: number;
    percentage: string;
  }) => {
    if (!dealId) return;
    
    try {
      const newUse: Partial<DealNsfFieldV2> = {
        id: -1, // Temporary ID for new items
        dealId,
        nsfContext: 'uses_total',
        dealType: 'uses', // Automatically set deal type based on context
        useType: useData.useType,
        amount: useData.amount || 0,
        amountPerGsf: 0,
        amountPerNsf: 0,
        amountPerZfa: 0,
        percentageOfTotal: useData.percentage ? parseFloat(useData.percentage) / 100 : 0,
        isRequired: false,
        additionalInfo: {}
      };

      const response = await fetch(`/api/v2/deals/${dealId}/nsf-fields`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newUse)
      });

      if (response.ok) {
        toast.success('Use added successfully');
        // Reset form and refresh data
        setIsAddingUse(false);
        setNewUseData({ useType: '', amount: 0, percentage: '' });
        if (onRefreshData) {
          setIsRefreshing(true);
          try {
            await onRefreshData();
          } finally {
            setIsRefreshing(false);
          }
        }
      } else {
        const errorData = await response.json();
        console.error('Failed to add use:', errorData.error);
        toast.error(errorData.error || 'Failed to add use');
      }
    } catch (error) {
      console.error('Error adding use:', error);
    } finally {
      setIsAddingUse(false);
    }
  };

  // Cancel adding use
  const handleCancelAddUse = () => {
    setIsAddingUse(false);
  };

  // Add new use
  const handleAddUse = () => {
    setNewUseData({
      useType: '',
      amount: 0,
      percentage: ''
    });
    setIsAddingUse(true);
  };

  // Delete NSF field
  const handleDelete = async (nsfId: number) => {
    if (!dealId) return;
    
    setIsDeleting(nsfId);
    try {
      const response = await fetch(`/api/v2/deals/${dealId}/nsf-fields?nsfFieldId=${nsfId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        toast.success('NSF field and related investment criteria deleted successfully');
        // Refresh data without page reload
        if (onRefreshData) {
          setIsRefreshing(true);
          try {
            await onRefreshData();
          } finally {
            setIsRefreshing(false);
          }
        }
      } else {
        console.error('Failed to delete NSF field');
        toast.error('Failed to delete NSF field');
      }
    } catch (error) {
      console.error('Error deleting NSF field:', error);
    } finally {
      setIsDeleting(null);
    }
  };

  // Handle percentage input changes
  const handlePercentageChange = (nsfId: string, value: string) => {
    setPercentageInputs(prev => ({ ...prev, [nsfId]: value }));
  };

  // Handle percentage input blur (save to actual field)
  const handlePercentageBlur = (nsfId: string) => {
    const inputValue = percentageInputs[nsfId];
    if (inputValue !== undefined) {
      const numericValue = parseFloat(inputValue) / 100; // Convert to decimal
      onNsfFieldChange(nsfId, 'percentageOfTotal', numericValue);
      setPercentageInputs(prev => {
        const newState = { ...prev };
        delete newState[nsfId];
        return newState;
      });
    }
  };

  // Initialize percentage inputs when component mounts or nsfFields change
  useEffect(() => {
    const initialInputs: { [key: string]: string } = {};
    nsfFields.forEach(nsf => {
      if (nsf.percentageOfTotal !== null && nsf.percentageOfTotal !== undefined) {
        initialInputs[nsf.id.toString()] = (nsf.percentageOfTotal * 100).toFixed(2);
      }
    });
    setPercentageInputs(initialInputs);
  }, [nsfFields]);

  const renderNsfField = (nsf: DealNsfFieldV2, index: number) => (
    <div key={nsf.id} className="border rounded-lg p-4 bg-gray-50">
      <div className="flex items-center justify-between mb-4">
        <div className="flex-1">
          {isEditing ? (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {/* Show Source Type for sources, Use Type for uses */}
              {nsf.nsfContext === 'sources' ? (
                <div>
                  <Label className="text-sm font-medium text-gray-600">Source Type</Label>
                  {isEditing ? (
                    <Select
                      value={nsf.sourceType || ""}
                      onValueChange={(value) => onNsfFieldChange(nsf.id.toString(), 'sourceType', value)}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select source type" />
                      </SelectTrigger>
                      <SelectContent>
                        {isLoadingMappings ? (
                          <SelectItem value="loading" disabled>Loading...</SelectItem>
                        ) : (
                          centralMappings?.capitalPositions?.map((position: any) => (
                            <SelectItem key={position.value} value={position.value}>
                              {position.label}
                            </SelectItem>
                          )) || []
                        )}
                      </SelectContent>
                    </Select>
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{nsf.sourceType || "N/A"}</p>
                  )}
                </div>
              ) : (
                <div>
                  <Label className="text-sm font-medium text-gray-600">Use Type</Label>
                  {isEditing ? (
                    <Input
                      type="text"
                      value={nsf.useType || ""}
                      onChange={(e) => onNsfFieldChange(nsf.id.toString(), 'useType', e.target.value)}
                      className="mt-1"
                      placeholder="e.g., Hard Cost, Soft Cost"
                    />
                  ) : (
                    <p className="text-sm text-gray-900 mt-1">{nsf.useType || "N/A"}</p>
                  )}
                </div>
              )}

              <div className="flex items-center space-x-2">
                <div className="text-sm text-gray-500 italic">
                  Required status is controlled by Ask Capital Position in Overview tab
                </div>
              </div>
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between">
                                  <div>
                    <h4 className="font-medium text-gray-900 text-lg capitalize">
                      {nsf.nsfContext === 'sources' ? (nsf.sourceType || 'Source Entry') : (nsf.useType || 'Use Entry')}
                    </h4>
                    {nsf.nsfContext === 'sources' && nsf.sourceType && (
                      <p className="text-sm text-gray-600">Capital Position: {nsf.sourceType}</p>
                    )}
                    {nsf.nsfContext === 'uses_total' && nsf.useType && (
                      <p className="text-sm text-gray-600">Use Type: {nsf.useType}</p>
                    )}
                  </div>
                <div className="flex items-center space-x-2">
                  {nsf.isRequired && (
                    <Badge variant="destructive" className="text-xs">
                      Required
                    </Badge>
                  )}
                  {(nsf.nsfContext === 'sources' && nsf.sourceType) || (nsf.nsfContext === 'uses_total' && nsf.useType) ? (
                    <Badge variant="outline" className="text-xs capitalize">
                      {nsf.nsfContext === 'sources' ? 'Capital Position' : 'Use Type'}
                    </Badge>
                  ) : null}
                </div>
              </div>
            </>
          )}
        </div>
        
        {/* Delete Button */}
        {isEditing && (
          <Button
            variant="destructive"
            size="sm"
            onClick={() => handleDelete(nsf.id)}
            disabled={isDeleting === nsf.id}
            className="ml-4"
          >
            {isDeleting === nsf.id ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Trash2 className="h-4 w-4" />
            )}
          </Button>
        )}
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        
        
        <div>
          <Label className="text-sm font-medium text-gray-600">Amount</Label>
          {isEditing ? (
            <Input
              type="number"
              value={nsf.amount || ""}
              onChange={(e) => onNsfFieldChange(nsf.id.toString(), 'amount', parseFloat(e.target.value) || 0)}
              className="mt-1"
              placeholder="0"
            />
          ) : (
            <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(nsf.amount)}</p>
          )}
          {/* NSF Field Amount Conflicts */}
          {(() => {
            const context = nsf.sourceType ? 'sources' : 'uses';
            const type = nsf.sourceType || nsf.useType;
            if (context && type && hasNsfConflict(context, type, 'amount')) {
              const conflictData = getNsfConflictData(context, type, 'amount');
              return conflictData ? (
                <ConflictIndicator
                  fieldName={`${type} Amount`}
                  conflictData={conflictData}
                  currentValue={nsf.amount}
                  onValueChange={(newValue) => onNsfFieldChange(nsf.id.toString(), 'amount', newValue)}
                  className="mt-2"
                />
              ) : null;
            }
            return null;
          })()}
        </div>
        
        {/* Second Row - Per GSF, Per ZFA, Per NSF */}
        <div>
          <Label className="text-sm font-medium text-gray-600">Per GSF</Label>
          {isEditing ? (
            <Input
              type="number"
              value={nsf.amountPerGsf || ""}
              onChange={(e) => onNsfFieldChange(nsf.id.toString(), 'amountPerGsf', parseFloat(e.target.value) || 0)}
              className="mt-1"
              placeholder="0"
            />
          ) : (
            <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(nsf.amountPerGsf)}</p>
          )}
        </div>
        
        <div>
          <Label className="text-sm font-medium text-gray-600">Per ZFA</Label>
          {isEditing ? (
            <Input
              type="number"
              value={nsf.amountPerZfa || ""}
              onChange={(e) => onNsfFieldChange(nsf.id.toString(), 'amountPerZfa', parseFloat(e.target.value) || 0)}
              className="mt-1"
              placeholder="0"
            />
          ) : (
            <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(nsf.amountPerZfa)}</p>
          )}
        </div>
        
        <div>
          <Label className="text-sm font-medium text-gray-600">Per NSF</Label>
          {isEditing ? (
            <Input
              type="number"
              value={nsf.amountPerNsf || ""}
              onChange={(e) => onNsfFieldChange(nsf.id.toString(), 'amountPerNsf', parseFloat(e.target.value) || 0)}
              className="mt-1"
              placeholder="0"
            />
          ) : (
            <p className="text-sm text-gray-900 mt-1">{formatRawCurrency(nsf.amountPerNsf)}</p>
          )}
        </div>
        
        {/* Third Row - Percentage and Measurements */}
        <div>
          <Label className="text-sm font-medium text-gray-600">Percent</Label>
          {isEditing ? (
            <Input
              type="number"
              step="0.01"
              min="0"
              max="100"
              value={percentageInputs[nsf.id] ?? (nsf.percentageOfTotal ? (nsf.percentageOfTotal * 100).toFixed(2) : "")}
              onChange={(e) => {
                setPercentageInputs(prev => ({
                  ...prev,
                  [nsf.id]: e.target.value
                }));
              }}
              onBlur={(e) => {
                const value = parseFloat(e.target.value) || 0;
                // Convert percentage (0-100) back to decimal (0-1) for backend
                onNsfFieldChange(nsf.id.toString(), 'percentageOfTotal', value / 100);
                // Clear the local input state after saving
                setPercentageInputs(prev => {
                  const newState = { ...prev };
                  delete newState[nsf.id];
                  return newState;
                });
              }}
              className="mt-1"
              placeholder="0.00"
            />
          ) : (
            <p className="text-sm text-gray-900 mt-1">
              {nsf.percentageOfTotal ? `${(nsf.percentageOfTotal * 100).toFixed(2)}%` : "0.00%"}
            </p>
          )}
        </div>
        
        <div>
          <Label className="text-sm font-medium text-gray-600">GSF (Gross Square Foot)</Label>
          {isEditing ? (
            <Input
              type="number"
              value={property?.gsfGrossSquareFoot || ""}
              onChange={(e) => {
                const value = parseFloat(e.target.value) || 0;
                // Update property via parent component
                if (onPropertyFieldChange) {
                  onPropertyFieldChange('gsfGrossSquareFoot', value);
                }
              }}
              className="mt-1"
              placeholder="0"
            />
          ) : (
            <p className="text-sm text-gray-900 mt-1">
              {formatNumber(property?.gsfGrossSquareFoot || null)} 
              <span className="text-xs text-gray-500 ml-2">(from property)</span>
            </p>
          )}
        </div>
        
        <div>
          <Label className="text-sm font-medium text-gray-600">ZFA (Zoning Floor Area)</Label>
          {isEditing ? (
            <Input
              type="number"
              value={property?.zfaZoningFloorArea || ""}
              onChange={(e) => {
                const value = parseFloat(e.target.value) || 0;
                // Update property via parent component
                if (onPropertyFieldChange) {
                  onPropertyFieldChange('zfaZoningFloorArea', value);
                }
              }}
              className="mt-1"
              placeholder="0"
            />
          ) : (
            <p className="text-sm text-gray-900 mt-1">
              {formatNumber(property?.zfaZoningFloorArea || null)} 
              <span className="text-xs text-gray-500 ml-2">(from property)</span>
            </p>
          )}
        </div>
        
        <div>
          <Label className="text-sm font-medium text-gray-600">Total NSF</Label>
          {isEditing ? (
            <Input
              type="number"
              value={property?.totalNsfNetSquareFoot || ""}
              onChange={(e) => {
                const value = parseFloat(e.target.value) || 0;
                // Update property via parent component
                if (onPropertyFieldChange) {
                  onPropertyFieldChange('totalNsfNetSquareFoot', value);
                }
              }}
              className="mt-1"
              placeholder="0"
            />
          ) : (
            <p className="text-sm text-gray-900 mt-1">
              {formatNumber(property?.totalNsfNetSquareFoot || null)} 
              <span className="text-xs text-gray-500 ml-2">(from property)</span>
            </p>
          )}
        </div>
      </div>
      
      {nsf.additionalInfo && Object.keys(nsf.additionalInfo).length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <Label className="text-sm font-medium text-gray-600">Additional Info</Label>
          {isEditing ? (
            <Textarea
              value={JSON.stringify(nsf.additionalInfo, null, 2)}
              onChange={(e) => {
                try {
                  const parsed = JSON.parse(e.target.value);
                  onNsfFieldChange(nsf.id.toString(), 'additionalInfo', parsed);
                } catch (error) {
                  // Handle invalid JSON
                }
              }}
              className="mt-1"
              rows={3}
              placeholder="Enter JSON data..."
            />
          ) : (
            <div className="space-y-2">
              {/* Display conflict resolution info */}
              {nsf.additionalInfo.conflict_resolution && (
                <div className="bg-blue-50 border border-blue-200 rounded p-3">
                  <h4 className="text-sm font-medium text-blue-800 mb-2">Conflict Resolution Data</h4>
                  {Object.entries(nsf.additionalInfo.conflict_resolution).map(([fieldName, conflictData]: [string, any]) => (
                    <div key={fieldName} className="mb-3 p-2 bg-white rounded border">
                      <div className="text-xs font-medium text-blue-700 mb-1">{fieldName}</div>
                      <div className="text-xs space-y-1">
                        <div><span className="font-medium">Source:</span> {conflictData.source_file}</div>
                        <div><span className="font-medium">Confidence:</span> {conflictData.confidence}</div>
                        {conflictData.alternatives && conflictData.alternatives.length > 0 && (
                          <div>
                            <span className="font-medium">Alternatives:</span>
                            <ul className="ml-2 mt-1">
                              {conflictData.alternatives.map((alt: any, idx: number) => (
                                <li key={idx} className="text-xs">
                                  {alt.value} ({alt.file}) - {alt.context}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        {conflictData.resolution_notes && (
                          <div><span className="font-medium">Notes:</span> {conflictData.resolution_notes}</div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {/* Display other additional info */}
              {Object.entries(nsf.additionalInfo).filter(([key]) => key !== 'conflict_resolution').length > 0 && (
                <div className="text-xs text-gray-600 bg-gray-100 p-2 rounded">
                  <pre>{JSON.stringify(
                    Object.fromEntries(
                      Object.entries(nsf.additionalInfo).filter(([key]) => key !== 'conflict_resolution')
                    ), 
                    null, 
                    2
                  )}</pre>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Square className="h-5 w-5" />
          Sources/Uses ({nsfFields.length})
          {isNsfSyncing && (
            <div className="flex items-center gap-1 text-xs text-blue-600">
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
              Syncing...
            </div>
          )}
          {isRefreshing && (
            <div className="flex items-center gap-1 text-xs text-green-600">
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-green-600"></div>
              Refreshing...
            </div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Help message when not editing */}
        {!isEditing && (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center gap-2 text-yellow-800">
              <div className="text-sm">
                <strong>Tip:</strong> To add or edit sources and uses, click the "Edit" button in the Overview tab to enable editing mode.
              </div>
            </div>
          </div>
        )}
        
        {/* NSF Summary Section */}
        {nsfFields && nsfFields.length > 0 && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-3">NSF Data Summary</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-blue-700 font-medium">Total NSF:</span>
                <div className="text-lg font-semibold text-blue-900">
                  {formatNumber(getTotalNsf(nsfFields), 'sq ft')}
                </div>
              </div>
              <div>
                <span className="text-blue-700 font-medium">Sources Amount:</span>
                <div className="text-lg font-semibold text-blue-900">
                  {formatRawCurrency(calculateAmountByContext(nsfFields, 'sources'))}
                </div>
              </div>
              <div>
                <span className="text-blue-700 font-medium">Uses Amount:</span>
                <div className="text-lg font-semibold text-blue-900">
                  {formatRawCurrency(calculateAmountByContext(nsfFields, 'uses_total'))}
                </div>
              </div>
              <div>
                <span className="text-blue-700 font-medium">Fields Count:</span>
                <div className="text-lg font-semibold text-blue-900">
                  {nsfFields.length}
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-6">
            {/* Sources Section */}
            <div>
              <div className="flex items-center justify-between mb-4 border-b border-gray-200 pb-2">
                <h3 className="text-lg font-semibold text-gray-900">
                  Sources {batchSources.length > 0 && <span className="text-sm text-blue-600">({batchSources.length} unsaved)</span>}
                </h3>
                <div className="flex items-center gap-2">
                  {(batchSources.length > 0 || batchUses.length > 0) && (
                    <Button
                      onClick={handleSaveBatch}
                      size="sm"
                      className="bg-green-600 hover:bg-green-700"
                    >
                      Save All ({batchSources.length + batchUses.length})
                    </Button>
                  )}
                  <Button
                    onClick={() => setIsAddingSource(!isAddingSource)}
                    size="sm"
                    variant={isAddingSource ? "outline" : "default"}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    {isAddingSource ? "Close" : "Add Sources"}
                  </Button>
                </div>
              </div>
              {/* Empty card for adding new source */}
              {isAddingSource && (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 bg-gray-50 mb-4">
                  <div className="text-center mb-4">
                    <h4 className="text-lg font-medium text-gray-900">Add New Source</h4>
                    <p className="text-sm text-gray-500">Fill in the details below to create a new source</p>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Source Type</Label>
                      <Select
                        value={newSourceData.sourceType || ""}
                        onValueChange={(value) => setNewSourceData(prev => ({ ...prev, sourceType: value }))}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select source type" />
                        </SelectTrigger>
                        <SelectContent>
                          {isLoadingMappings ? (
                            <SelectItem value="loading" disabled>Loading...</SelectItem>
                          ) : (
                            centralMappings?.capitalPositions?.map((position: any) => (
                              <SelectItem key={position.value} value={position.value}>
                                {position.label}
                              </SelectItem>
                            )) || []
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Amount</Label>
                      <Input
                        type="number"
                        value={newSourceData.amount || ""}
                        onChange={(e) => {
                          const amount = parseFloat(e.target.value) || 0;
                          setNewSourceData(prev => ({ ...prev, amount }));
                        }}
                        className="mt-1"
                        placeholder="0"
                      />
                      {newSourceData.amount > 0 && (
                        <div className="text-xs text-gray-500 mt-1">
                          <div className="font-medium text-blue-600">
                            Auto: {calculateRealTimePercentage(newSourceData.amount, 'sources', newSourceData.sourceType).toFixed(2)}%
                          </div>
                          <div>GSF: ${calculatePerSqFtValues(newSourceData.amount).amountPerGsf.toFixed(2)}</div>
                          <div>NSF: ${calculatePerSqFtValues(newSourceData.amount).amountPerNsf.toFixed(2)}</div>
                          <div>ZFA: ${calculatePerSqFtValues(newSourceData.amount).amountPerZfa.toFixed(2)}</div>
                        </div>
                      )}
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Percentage</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        max="100"
                        value={newSourceData.amount > 0 && newSourceData.sourceType ? 
                          calculateRealTimePercentage(newSourceData.amount, 'sources', newSourceData.sourceType).toFixed(2) : 
                          newSourceData.percentage || ""
                        }
                        onChange={(e) => setNewSourceData(prev => ({ ...prev, percentage: e.target.value }))}
                        className="mt-1"
                        placeholder="0.00"
                        disabled={!!(newSourceData.amount > 0 && newSourceData.sourceType)} // Auto-calculated
                      />
                      {newSourceData.amount > 0 && newSourceData.sourceType && (
                        <div className="text-xs text-blue-600 mt-1">
                          Auto-calculated based on total sources
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-end space-x-2">
                      <Button
                        onClick={handleAddSourceToBatch}
                        disabled={!newSourceData.sourceType || !newSourceData.amount}
                        size="sm"
                        className="flex-1"
                      >
                        Add Another
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setNewSourceData({ sourceType: '', amount: 0, percentage: '' });
                        }}
                        size="sm"
                      >
                        Clear
                      </Button>
                    </div>
                  </div>
                </div>
              )}
              
              {/* Batch Sources Preview */}
              {batchSources.length > 0 && (
                <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <h4 className="text-sm font-medium text-blue-900 mb-3">New Sources ({batchSources.length}) - Not Saved Yet</h4>
                  <div className="space-y-2">
                    {batchSources.map((source) => (
                      <div key={source.id} className="flex items-center justify-between p-2 bg-white rounded border">
                        <div className="flex-1">
                          <span className="font-medium text-sm">{source.sourceType}</span>
                          <div className="text-xs text-gray-600">
                            ${source.amount.toLocaleString()} 
                            {source.calculatedPercentage && ` • ${source.calculatedPercentage.toFixed(2)}%`}
                            {source.amountPerGsf && ` • GSF: $${source.amountPerGsf.toFixed(2)}`}
                            {source.amountPerNsf && ` • NSF: $${source.amountPerNsf.toFixed(2)}`}
                          </div>
                        </div>
                        <Button
                          onClick={() => removeBatchSource(source.id)}
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {sources.length > 0 ? (
                <div className="space-y-4">
                  {sources.map((nsf, index) => renderNsfField(nsf, index))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  {isEditing ? (
                    "No sources defined yet. Click 'Add Source' to create one."
                  ) : (
                    "No sources defined yet. Enable editing mode to add sources."
                  )}
                </div>
              )}
            </div>

            {/* Uses Section */}
            <div>
              <div className="flex items-center justify-between mb-4 border-b border-gray-200 pb-2">
                <h3 className="text-lg font-semibold text-gray-900">
                  Uses {batchUses.length > 0 && <span className="text-sm text-blue-600">({batchUses.length} unsaved)</span>}
                </h3>
                <div className="flex items-center gap-2">
                  {(batchSources.length > 0 || batchUses.length > 0) && (
                    <Button
                      onClick={handleSaveBatch}
                      size="sm"
                      className="bg-green-600 hover:bg-green-700"
                    >
                      Save All ({batchSources.length + batchUses.length})
                    </Button>
                  )}
                  <Button
                    onClick={() => setIsAddingUse(!isAddingUse)}
                    size="sm"
                    variant={isAddingUse ? "outline" : "default"}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    {isAddingUse ? "Close" : "Add Uses"}
                  </Button>
                </div>
              </div>
              {/* Empty card for adding new use */}
              {isAddingUse && (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 bg-gray-50 mb-4">
                  <div className="text-center mb-4">
                    <h4 className="text-lg font-medium text-gray-900">Add New Use</h4>
                    <p className="text-sm text-gray-500">Fill in the details below to create a new use</p>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Use Type</Label>
                      <Input
                        type="text"
                        value={newUseData.useType || ""}
                        onChange={(e) => setNewUseData(prev => ({ ...prev, useType: e.target.value }))}
                        className="mt-1"
                        placeholder="e.g., Hard Cost, Soft Cost"
                      />
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Amount</Label>
                      <Input
                        type="number"
                        value={newUseData.amount || ""}
                        onChange={(e) => {
                          const amount = parseFloat(e.target.value) || 0;
                          setNewUseData(prev => ({ ...prev, amount }));
                        }}
                        className="mt-1"
                        placeholder="0"
                      />
                      {newUseData.amount > 0 && (
                        <div className="text-xs text-gray-500 mt-1">
                          <div className="font-medium text-green-600">
                            Auto: {calculateRealTimePercentage(newUseData.amount, 'uses_total').toFixed(2)}%
                          </div>
                          <div>GSF: ${calculatePerSqFtValues(newUseData.amount).amountPerGsf.toFixed(2)}</div>
                          <div>NSF: ${calculatePerSqFtValues(newUseData.amount).amountPerNsf.toFixed(2)}</div>
                          <div>ZFA: ${calculatePerSqFtValues(newUseData.amount).amountPerZfa.toFixed(2)}</div>
                        </div>
                      )}
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-gray-600">Percentage</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        max="100"
                        value={newUseData.amount > 0 && newUseData.useType ? 
                          calculateRealTimePercentage(newUseData.amount, 'uses_total').toFixed(2) : 
                          newUseData.percentage || ""
                        }
                        onChange={(e) => setNewUseData(prev => ({ ...prev, percentage: e.target.value }))}
                        className="mt-1"
                        placeholder="0.00"
                        disabled={!!(newUseData.amount > 0 && newUseData.useType)} // Auto-calculated
                      />
                      {newUseData.amount > 0 && newUseData.useType && (
                        <div className="text-xs text-green-600 mt-1">
                          Auto-calculated based on total uses
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-end space-x-2">
                      <Button
                        onClick={handleAddUseToBatch}
                        disabled={!newUseData.useType || !newUseData.amount}
                        size="sm"
                        className="flex-1"
                      >
                        Add Another
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setNewUseData({ useType: '', amount: 0, percentage: '' });
                        }}
                        size="sm"
                      >
                        Clear
                      </Button>
                    </div>
                  </div>
                </div>
              )}
              
              {/* Batch Uses Preview */}
              {batchUses.length > 0 && (
                <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <h4 className="text-sm font-medium text-green-900 mb-3">New Uses ({batchUses.length}) - Not Saved Yet</h4>
                  <div className="space-y-2">
                    {batchUses.map((use) => (
                      <div key={use.id} className="flex items-center justify-between p-2 bg-white rounded border">
                        <div className="flex-1">
                          <span className="font-medium text-sm">{use.useType}</span>
                          <div className="text-xs text-gray-600">
                            ${use.amount.toLocaleString()} 
                            {use.calculatedPercentage && ` • ${use.calculatedPercentage.toFixed(2)}%`}
                            {use.amountPerGsf && ` • GSF: $${use.amountPerGsf.toFixed(2)}`}
                            {use.amountPerNsf && ` • NSF: $${use.amountPerNsf.toFixed(2)}`}
                          </div>
                        </div>
                        <Button
                          onClick={() => removeBatchUse(use.id)}
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {uses.length > 0 ? (
                <div className="space-y-4">
                  {uses.map((nsf, index) => renderNsfField(nsf, index))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  {isEditing ? (
                    "No uses defined yet. Click 'Add Use' to create one."
                  ) : (
                    "No uses defined yet. Enable editing mode to add uses."
                  )}
                </div>
              )}
            </div>
          </div>
          
          {/* Global Save All Button */}
          {(batchSources.length > 0 || batchUses.length > 0) && (
            <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-yellow-900">Unsaved Changes</h4>
                  <p className="text-xs text-yellow-700">
                    {batchSources.length} sources and {batchUses.length} uses ready to save
                  </p>
                </div>
                <Button
                  onClick={handleSaveBatch}
                  size="sm"
                  className="bg-green-600 hover:bg-green-700"
                >
                  Save All ({batchSources.length + batchUses.length})
                </Button>
              </div>
            </div>
          )}
     
      </CardContent>
    </Card>
  );
};

export default SourcesUsesTab;
