// Shared field configuration for contact components
// This ensures consistency across AddContact, ContactEdit, and ContactOverview components

import { Contact, ContactFieldGroups, ContactValidationRules } from './types';

// Field group configurations based on database schema
// This is used for ContactEditTab - includes all fields
export const contactFieldGroups: ContactFieldGroups = {
  personalInfo: {
    title: "Personal Information",
    icon: "User",
    fields: [
      {
        name: 'first_name',
        label: 'First Name',
        type: 'text',
        required: true,
        placeholder: 'Enter first name'
      },
      {
        name: 'last_name',
        label: 'Last Name', 
        type: 'text',
        required: true,
        placeholder: 'Enter last name'
      },
      {
        name: 'job_title',
        label: 'Job Title',
        type: 'text',
        placeholder: 'e.g., Managing Director'
      },
      {
        name: 'job_tier',
        label: 'Job Tier',
        type: 'select',
        options: ['C-Level', 'Senior Management', 'Middle Management', 'Associate/Analyst', 'Other']
      },
      {
        name: 'company_name',
        label: 'Company Name',
        type: 'text',
        placeholder: 'Enter company name'
      },
      {
        name: 'executive_summary',
        label: 'Executive Summary',
        type: 'textarea',
        placeholder: 'Enter executive summary'
      },
      {
        name: 'career_timeline',
        label: 'Career Timeline',
        type: 'array',
        placeholder: 'Add career milestone'
      }
    ]
  },
  contactInfo: {
    title: "Contact Information",
    icon: "Mail",
    fields: [
      {
        name: 'personal_email',
        label: 'Primary Email',
        type: 'email',
        placeholder: '<EMAIL>'
      },
      {
        name: 'additional_email',
        label: 'Additional Email',
        type: 'email',
        placeholder: '<EMAIL>'
      },
      {
        name: 'phone_number',
        label: 'Primary Phone',
        type: 'tel',
        placeholder: 'e.g., +****************'
      },
      {
        name: 'phone_number_secondary',
        label: 'Secondary Phone',
        type: 'tel',
        placeholder: 'e.g., +****************'
      },
      {
        name: 'contact_address',
        label: 'Address',
        type: 'text',
        placeholder: 'e.g., 123 Main Street'
      },
      {
        name: 'contact_city',
        label: 'City',
        type: 'text',
        placeholder: 'e.g., New York'
      },
      {
        name: 'contact_state',
        label: 'State/Province',
        type: 'text',
        placeholder: 'e.g., New York'
      },
      {
        name: 'contact_zip_code',
        label: 'ZIP Code',
        type: 'text',
        placeholder: 'e.g., 10001'
      },
      {
        name: 'contact_country',
        label: 'Country',
        type: 'text',
        placeholder: 'e.g., United States'
      },
      {
        name: 'contact_type',
        label: 'Contact Type',
        type: 'select',
        options: ['Primary', 'Secondary', 'Reference', 'Lead', 'Prospect']
      },
      {
        name: 'relationship_owner',
        label: 'Relationship Owner',
        type: 'text',
        placeholder: 'e.g., John Smith'
      },
      {
        name: 'role_in_decision_making',
        label: 'Decision Making Role',
        type: 'select',
        options: ['Decision Maker', 'Influencer', 'Analyst', 'Gatekeeper']
      },
      {
        name: 'last_contact_date',
        label: 'Last Contact Date',
        type: 'date'
      },
      {
        name: 'last_contact_at',
        label: 'Last Contact At',
        type: 'datetime-local'
      },
      {
        name: 'source_of_introduction',
        label: 'Source of Introduction',
        type: 'text',
        placeholder: 'e.g., LinkedIn, Referral'
      },
      {
        name: 'accredited_investor_status',
        label: 'Accredited Investor',
        type: 'checkbox'
      },
      {
        name: 'kyc_status',
        label: 'KYC Status',
        type: 'select',
        options: ['pending', 'In Progress', 'Completed', 'Not Required']
      },
      {
        name: 'executive_summary',
        label: 'Executive Summary',
        type: 'textarea',
        placeholder: 'Enter executive summary'
      },
      {
        name: 'career_timeline',
        label: 'Career Timeline',
        type: 'array',
        placeholder: 'Add career milestone'
      }
    ]
  },
  socialMedia: {
    title: "Social Media",
    icon: "Globe",
    fields: [
      {
        name: 'linkedin_url',
        label: 'LinkedIn Profile',
        type: 'text',
        placeholder: 'https://linkedin.com/in/username'
      },
      {
        name: 'twitter',
        label: 'Twitter Profile',
        type: 'text',
        placeholder: 'https://twitter.com/username'
      },
      {
        name: 'facebook',
        label: 'Facebook Profile',
        type: 'text',
        placeholder: 'Facebook URL'
      },
      {
        name: 'instagram',
        label: 'Instagram Profile',
        type: 'text',
        placeholder: 'Instagram URL'
      },
      {
        name: 'youtube',
        label: 'YouTube Channel',
        type: 'text',
        placeholder: 'YouTube URL'
      }
    ]
  },
  education: {
    title: "Education",
    icon: "GraduationCap",
    fields: [
      {
        name: 'education_college',
        label: 'College/University',
        type: 'text',
        placeholder: 'Enter college or university name'
      },
      {
        name: 'education_college_year_graduated',
        label: 'College Graduation Year',
        type: 'text',
        placeholder: 'e.g., 2020'
      },
      {
        name: 'education_high_school',
        label: 'High School',
        type: 'text',
        placeholder: 'Enter high school name'
      },
      {
        name: 'education_high_school_year_graduated',
        label: 'High School Graduation Year',
        type: 'text',
        placeholder: 'e.g., 2016'
      }
    ]
  },
  personalDetails: {
    title: "Personal Details",
    icon: "Heart",
    fields: [
      {
        name: 'honorable_achievements',
        label: 'Honorable Achievements',
        type: 'array',
        placeholder: 'Enter achievements'
      },
      {
        name: 'hobbies',
        label: 'Hobbies',
        type: 'array',
        placeholder: 'Enter hobbies'
      },
      {
        name: 'age',
        label: 'Age',
        type: 'text',
        placeholder: 'e.g., 45'
      }
    ]
  },

};

// Field group configurations for AddContact - excludes enrichment fields
// These fields should be populated by ContactEnrichmentProcessorV2, not manually entered
export const addContactFieldGroups: ContactFieldGroups = {
  personalInfo: {
    title: "Personal Information",
    icon: "User",
    fields: [
      {
        name: 'first_name',
        label: 'First Name',
        type: 'text',
        required: true,
        placeholder: 'Enter first name'
      },
      {
        name: 'last_name',
        label: 'Last Name', 
        type: 'text',
        required: true,
        placeholder: 'Enter last name'
      },
      {
        name: 'job_title',
        label: 'Job Title',
        type: 'text',
        placeholder: 'e.g., Managing Director'
      },
      {
        name: 'job_tier',
        label: 'Job Tier',
        type: 'select',
        options: ['C-Level', 'Senior Management', 'Middle Management', 'Associate/Analyst', 'Other']
      },
      {
        name: 'company_name',
        label: 'Company Name',
        type: 'text',
        placeholder: 'Enter company name'
      }
    ]
  },
  contactInfo: {
    title: "Contact Information",
    icon: "Mail",
    fields: [
      {
        name: 'personal_email',
        label: 'Primary Email',
        type: 'email',
        placeholder: '<EMAIL>'
      },
      {
        name: 'phone_number',
        label: 'Primary Phone',
        type: 'tel',
        placeholder: 'e.g., +****************'
      },
      {
        name: 'phone_number_secondary',
        label: 'Secondary Phone',
        type: 'tel',
        placeholder: 'e.g., +****************'
      },
      {
        name: 'contact_address',
        label: 'Address',
        type: 'text',
        placeholder: 'e.g., 123 Main Street'
      },
      {
        name: 'contact_city',
        label: 'City',
        type: 'text',
        placeholder: 'e.g., New York'
      },
      {
        name: 'contact_state',
        label: 'State/Province',
        type: 'text',
        placeholder: 'e.g., New York'
      },
      {
        name: 'contact_zip_code',
        label: 'ZIP Code',
        type: 'text',
        placeholder: 'e.g., 10001'
      },
      {
        name: 'contact_country',
        label: 'Country',
        type: 'text',
        placeholder: 'e.g., United States'
      },
      {
        name: 'last_contact_date',
        label: 'Last Contact Date',
        type: 'date'
      },
      {
        name: 'source_of_introduction',
        label: 'Source of Introduction',
        type: 'text',
        placeholder: 'e.g., LinkedIn, Referral'
      }
    ]
  },
  socialMedia: {
    title: "Social Media",
    icon: "Globe",
    fields: [
      {
        name: 'linkedin_url',
        label: 'LinkedIn Profile',
        type: 'text',
        placeholder: 'https://linkedin.com/in/username'
      }
    ]
  },
  education: {
    title: "Education",
    icon: "GraduationCap",
    fields: [] // Empty - these fields are populated by enrichment
  },
  personalDetails: {
    title: "Personal Details",
    icon: "Heart",
    fields: [] // Empty - these fields are populated by enrichment
  }
};

// Validation rules for contact fields
export const contactValidationRules: ContactValidationRules = {
  first_name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    errorMessage: 'First name is required and must be between 2-100 characters'
  },
  last_name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    errorMessage: 'Last name is required and must be between 2-100 characters'
  },
  email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    errorMessage: 'Please enter a valid email address'
  },
  additional_email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    errorMessage: 'Please enter a valid email address'
  },
  personal_email: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    errorMessage: 'Please enter a valid email address'
  },
  phone_number: {
    pattern: /^[\+]?[1-9][\d]{0,15}$/,
    errorMessage: 'Please enter a valid phone number'
  },
  phone_number_secondary: {
    pattern: /^[\+]?[1-9][\d]{0,15}$/,
    errorMessage: 'Please enter a valid phone number'
  },
  linkedin_url: {
    pattern: /^https?:\/\/(www\.)?linkedin\.com\/.*/,
    errorMessage: 'Please enter a valid LinkedIn URL'
  },
  age: {
    validator: (value: string) => !value || (!isNaN(Number(value)) && Number(value) > 0 && Number(value) < 120),
    errorMessage: 'Age must be a valid number between 1-120'
  }
};

// Helper functions for field validation and formatting
export const validateField = (fieldName: string, value: any): string | null => {
  const rule = contactValidationRules[fieldName];
  if (!rule) return null;

  // Check required
  if (rule.required && (!value || value.toString().trim() === '')) {
    return rule.errorMessage || `${fieldName} is required`;
  }

  // Skip other validations if value is empty and not required
  if (!value || value.toString().trim() === '') return null;

  // Check min length
  if (rule.minLength && value.toString().length < rule.minLength) {
    return rule.errorMessage || `${fieldName} must be at least ${rule.minLength} characters`;
  }

  // Check max length
  if (rule.maxLength && value.toString().length > rule.maxLength) {
    return rule.errorMessage || `${fieldName} must be no more than ${rule.maxLength} characters`;
  }

  // Check pattern
  if (rule.pattern && !rule.pattern.test(value.toString())) {
    return rule.errorMessage || `${fieldName} format is invalid`;
  }

  // Check custom validator
  if (rule.validator && !rule.validator(value)) {
    return rule.errorMessage || `${fieldName} is invalid`;
  }

  return null;
};

// Format phone number for display
export const formatPhoneNumber = (phone: string): string => {
  if (!phone) return '';
  const cleaned = phone.replace(/\D/g, '');
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  }
  if (cleaned.length === 11 && cleaned[0] === '1') {
    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
  return phone;
};

// Normalize URL by ensuring it starts with http/https
export const normalizeUrl = (url: string): string => {
  if (!url) return '';
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  return `https://${url}`;
};

// Determine capital type from company data
export const determineCapitalType = (companyType: string): string => {
  const type = companyType.toLowerCase();
  if (type.includes('debt') || type.includes('lending')) return 'Debt';
  if (type.includes('equity') || type.includes('investment')) return 'Equity';
  if (type.includes('mezzanine')) return 'Mezzanine';
  return '';
};

// Get field display value (handles arrays, booleans, etc.)
export const getFieldDisplayValue = (contact: Contact, fieldName: keyof Contact): string => {
  const value = contact[fieldName];
  
  if (value === null || value === undefined) return '';
  
  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }
  
  if (Array.isArray(value)) {
    return value.join(', ');
  }
  
  if (typeof value === 'object') {
    return JSON.stringify(value);
  }
  
  return String(value);
};

// Check if field has a value worth displaying
export const hasFieldValue = (contact: Contact, fieldName: keyof Contact): boolean => {
  const value = contact[fieldName];
  
  if (value === null || value === undefined) return false;
  if (typeof value === 'string' && value.trim() === '') return false;
  if (Array.isArray(value) && value.length === 0) return false;
  
  return true;
};

// Get all fields that have values for a contact
export const getPopulatedFields = (contact: Contact): Array<keyof Contact> => {
  return Object.keys(contact).filter(key => 
    hasFieldValue(contact, key as keyof Contact)
  ) as Array<keyof Contact>;
};

// Convert contact form data to database format
export const contactFormToDb = (formData: any): Partial<Contact> => {
  return {
    first_name: formData.first_name,
    last_name: formData.last_name,
    full_name: formData.first_name && formData.last_name 
      ? `${formData.first_name} ${formData.last_name}`.trim()
      : formData.first_name || formData.last_name || '',
    title: formData.title || formData.job_title,
    job_tier: formData.job_tier,
    email: formData.email,
    additional_email: formData.additional_email,
    phone_number: formData.phone_number || formData.phone,
    phone_number_secondary: formData.phone_number_secondary || formData.additional_phone,
    linkedin_url: formData.linkedin_url || formData.linkedin,
    twitter: formData.twitter,
    facebook: formData.facebook,
    instagram: formData.instagram,
    youtube: formData.youtube,
    executive_summary: formData.executive_summary,
    career_timeline: formData.career_timeline,
    education_college: formData.education_college,
    education_college_year_graduated: formData.education_college_year_graduated,
    education_high_school: formData.education_high_school,
    education_high_school_year_graduated: formData.education_high_school_year_graduated,
    honorable_achievements: formData.honorable_achievements || formData.honorable_achievments,
    hobbies: formData.hobbies,
    age: formData.age,
    contact_address: formData.contact_address || formData.address,
    contact_city: formData.contact_city || formData.city,
    contact_state: formData.contact_state || formData.state,
    contact_zip_code: formData.contact_zip_code || formData.zipcode,
    contact_country: formData.contact_country || formData.country,
    contact_type: formData.contact_type,
    relationship_owner: formData.relationship_owner,
    role_in_decision_making: formData.role_in_decision_making,
    last_contact_date: formData.last_contact_date,
    source_of_introduction: formData.source_of_introduction,
    accredited_investor_status: formData.accredited_investor_status,
    kyc_status: formData.kyc_status,
    company_name: formData.company_name,
    capital_type: formData.capital_type,
    investment_criteria_country: formData.investment_criteria_country,
    investment_criteria_state: formData.investment_criteria_state,
    investment_criteria_city: formData.investment_criteria_city,
    investment_criteria_property_type: formData.investment_criteria_property_type,
    investment_criteria_asset_type: formData.investment_criteria_asset_type,
    investment_criteria_loan_type: formData.investment_criteria_loan_type,
    investment_criteria_deal_size: formData.investment_criteria_deal_size
  };
};

// Convert database contact to form format
export const contactDbToForm = (contact: Contact): any => {
  return {
    first_name: contact.first_name || '',
    last_name: contact.last_name || '',
    title: contact.title || '',
    job_title: contact.title || '', // Legacy support
    job_tier: contact.job_tier || '',
    email: contact.email || '',
    additional_email: contact.additional_email || '',
    phone_number: contact.phone_number || '',
    phone: contact.phone_number || '', // Legacy support
    phone_number_secondary: contact.phone_number_secondary || '',
    additional_phone: contact.phone_number_secondary || '', // Legacy support
    linkedin_url: contact.linkedin_url || '',
    linkedin: contact.linkedin_url || '', // Legacy support
    twitter: contact.twitter || '',
    facebook: contact.facebook || '',
    instagram: contact.instagram || '',
    youtube: contact.youtube || '',
    executive_summary: contact.executive_summary || '',
    career_timeline: contact.career_timeline || [],
    education_college: contact.education_college || '',
    education_college_year_graduated: contact.education_college_year_graduated || '',
    education_high_school: contact.education_high_school || '',
    education_high_school_year_graduated: contact.education_high_school_year_graduated || '',
    honorable_achievements: contact.honorable_achievements || [],
    honorable_achievments: contact.honorable_achievements || [], // Legacy support
    hobbies: contact.hobbies || [],
    age: contact.age || '',
    contact_address: contact.contact_address || '',
    address: contact.contact_address || '', // Legacy support
    contact_city: contact.contact_city || '',
    city: contact.contact_city || '', // Legacy support
    contact_state: contact.contact_state || '',
    state: contact.contact_state || '', // Legacy support
    contact_zip_code: contact.contact_zip_code || '',
    zipcode: contact.contact_zip_code || '', // Legacy support
    contact_country: contact.contact_country || '',
    country: contact.contact_country || '', // Legacy support
    contact_type: contact.contact_type || '',
    relationship_owner: contact.relationship_owner || '',
    role_in_decision_making: contact.role_in_decision_making || '',
    last_contact_date: contact.last_contact_date || '',
    source_of_introduction: contact.source_of_introduction || '',
    accredited_investor_status: contact.accredited_investor_status || false,
    kyc_status: contact.kyc_status || '',
    company_name: contact.company_name || '',
    capital_type: contact.capital_type || '',
    investment_criteria_country: contact.investment_criteria_country || '',
    investment_criteria_state: contact.investment_criteria_state || '',
    investment_criteria_city: contact.investment_criteria_city || '',
    investment_criteria_property_type: contact.investment_criteria_property_type || '',
    investment_criteria_asset_type: contact.investment_criteria_asset_type || '',
    investment_criteria_loan_type: contact.investment_criteria_loan_type || '',
    investment_criteria_deal_size: contact.investment_criteria_deal_size || ''
  };
};
