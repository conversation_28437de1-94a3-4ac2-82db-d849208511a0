"use client"

import React, { useRef, useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Building2, Search, Loader2, ExternalLink, MapPin, Users, Target, TrendingUp, Globe, Phone, Mail, CheckCircle, Copy, Star, Edit, Save, X } from "lucide-react"
import { CompanySuggestion } from '../shared/types'
import { formatNumber } from '@/lib/utils'

interface CompanySearchFieldProps {
  companyName: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyDown: (e: React.KeyboardEvent) => void;
  suggestions: CompanySuggestion[];
  showSuggestions: boolean;
  isSearching: boolean;
  selectedSuggestionIndex: number;
  onCompanySelect: (company: CompanySuggestion) => void;
  selectedCompany: CompanySuggestion | null;
  companyId?: string;
  onInvestmentCriteriaSelect?: (selectedCriteria: any[]) => void;
  onCreateNewCompany?: (companyName: string) => void;
}

const CompanySearchField: React.FC<CompanySearchFieldProps> = ({
  companyName,
  onChange,
  onKeyDown,
  suggestions,
  showSuggestions,
  isSearching,
  selectedSuggestionIndex,
  onCompanySelect,
  selectedCompany,
  companyId,
  onInvestmentCriteriaSelect,
  onCreateNewCompany
}) => {
  const companyInputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const [investmentCriteria, setInvestmentCriteria] = useState<any[]>([]);
  const [loadingIC, setLoadingIC] = useState(false);
  const [selectedIC, setSelectedIC] = useState<number[]>([]);

  // Fetch investment criteria when company is selected
  useEffect(() => {
    const fetchInvestmentCriteria = async () => {
      if (selectedCompany?.company_id) {
        setLoadingIC(true);
        try {
          const response = await fetch(`/api/companies/${selectedCompany.company_id}/investment-criteria`);
          if (response.ok) {
            const data = await response.json();
            setInvestmentCriteria(data);
          }
        } catch (error) {
          console.error('Error fetching investment criteria:', error);
        } finally {
          setLoadingIC(false);
        }
      } else {
        setInvestmentCriteria([]);
        setSelectedIC([]);
      }
    };

    fetchInvestmentCriteria();
  }, [selectedCompany?.company_id]);

  const handleICSelection = (icId: number) => {
    const newSelectedIC = selectedIC.includes(icId) 
      ? selectedIC.filter(id => id !== icId)
      : [...selectedIC, icId];
    
    setSelectedIC(newSelectedIC);
    
    // Call callback with selected criteria data
    if (onInvestmentCriteriaSelect) {
      const selectedCriteriaData = investmentCriteria.filter(ic => 
        newSelectedIC.includes(ic.investment_criteria_id)
      );
      onInvestmentCriteriaSelect(selectedCriteriaData);
    }
  };

  return (
    <Card className="bg-white shadow-xl border-0 rounded-2xl">
      <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-100">
        <CardTitle className="flex items-center">
          <div className="p-2 bg-green-100 rounded-xl mr-3">
            <Building2 className="h-5 w-5 text-green-600" />
          </div>
          <div>
            <div className="text-lg font-semibold text-slate-900">Company Information</div>
            <div className="text-sm text-slate-600">
              {companyId ? 'Pre-selected company' : 'Search and select company'}
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6 bg-gradient-to-b from-white to-slate-50">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="company_name" className="text-sm font-medium text-slate-700">
              Company Name <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 z-10">
                {isSearching ? (
                  <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                ) : (
                  <Search className="h-4 w-4 text-gray-400" />
                )}
              </div>
              <Input
                ref={companyInputRef}
                id="company_name"
                name="company_name"
                value={companyName}
                onChange={onChange}
                onKeyDown={onKeyDown}
                className="h-12 pl-10 rounded-xl border-2 transition-all duration-200 focus:ring-4 focus:ring-green-100 border-slate-200 focus:border-green-500 hover:border-slate-300"
                placeholder={companyId ? "Company pre-selected" : "Start typing company name..."}
                required
                disabled={!!companyId}
              />
              
              {/* Company Search Suggestions */}
              {showSuggestions && (
                <div 
                  ref={suggestionsRef}
                  data-search-results
                  className="absolute top-full left-0 right-0 z-[9999] mt-1 bg-white border-2 border-slate-200 rounded-xl shadow-2xl max-h-80 overflow-y-auto"
                  style={{ position: 'absolute', zIndex: 9999 }}
                >
                  {suggestions.length > 0 ? (
                    suggestions.map((suggestion, index) => (
                      <div
                        key={suggestion.company_id || index}
                        className={`p-4 cursor-pointer transition-colors duration-150 border-b border-slate-100 last:border-b-0 ${
                          index === selectedSuggestionIndex 
                            ? 'bg-blue-50 border-blue-200' 
                            : 'hover:bg-slate-50'
                        }`}
                        onClick={() => onCompanySelect(suggestion)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <div className="font-medium text-slate-900">
                                {suggestion.company_name}
                              </div>
                              {suggestion.investment_criteria_count && suggestion.investment_criteria_count > 0 && (
                                <Badge className="bg-blue-100 text-blue-700 border-blue-200 text-xs font-medium px-2 py-0.5">
                                  <Target className="h-3 w-3 mr-1" />
                                  Has IC ({suggestion.investment_criteria_count})
                                </Badge>
                              )}
                            </div>
                            <div className="flex items-center space-x-4 text-sm text-slate-600">
                              {suggestion.industry && (
                                <Badge variant="secondary" className="text-xs">
                                  {suggestion.industry}
                                </Badge>
                              )}
                              {suggestion.company_website && (
                                <div className="flex items-center">
                                  <ExternalLink className="h-3 w-3 mr-1" />
                                  {suggestion.company_website.replace(/^https?:\/\//, '')}
                                </div>
                              )}
                            </div>
                            
                            {/* Enhanced Company Information */}
                            <div className="grid grid-cols-2 gap-2 mt-2 text-xs text-slate-500">
                              {(suggestion.company_city || suggestion.company_state) && (
                                <div className="flex items-center">
                                  <MapPin className="h-3 w-3 mr-1" />
                                  {[suggestion.company_city, suggestion.company_state].filter(Boolean).join(', ')}
                                </div>
                              )}
                              {suggestion.company_country && (
                                <div className="flex items-center">
                                  <Globe className="h-3 w-3 mr-1" />
                                  {suggestion.company_country}
                                </div>
                              )}
                              {suggestion.employee_count && (
                                <div className="flex items-center">
                                  <Users className="h-3 w-3 mr-1" />
                                  {suggestion.employee_count} employees
                                </div>
                              )}
                              {suggestion.revenue_range && (
                                <div className="flex items-center">
                                  <TrendingUp className="h-3 w-3 mr-1" />
                                  {suggestion.revenue_range}
                                </div>
                              )}
                              {suggestion.company_phone && (
                                <div className="flex items-center">
                                  <Phone className="h-3 w-3 mr-1" />
                                  {suggestion.company_phone}
                                </div>
                              )}
                              {suggestion.company_email && (
                                <div className="flex items-center">
                                  <Mail className="h-3 w-3 mr-1" />
                                  {suggestion.company_email}
                                </div>
                              )}
                            </div>
                            
                            {/* Additional Investment Criteria Info */}
                            {suggestion.investment_criteria_count && suggestion.investment_criteria_count > 0 && (
                              <div className="flex items-center mt-2 text-xs text-blue-600">
                                <Target className="h-3 w-3 mr-1" />
                                {suggestion.investment_criteria_count > 1 ? `${suggestion.investment_criteria_count} criteria available` : '1 criterion available'}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-center">
                      <div className="text-sm text-slate-600 mb-3">
                        No companies found matching "{companyName}"
                      </div>
                      {companyName.trim() && onCreateNewCompany && (
                        <Button
                          type="button"
                          onClick={() => onCreateNewCompany(companyName.trim())}
                          className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium py-2 px-4 rounded-lg"
                        >
                          <Building2 className="h-4 w-4 mr-2" />
                          Create New Company: {companyName}
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Selected Company Display */}
          {selectedCompany && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-xl">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="font-medium text-green-900">
                      ✓ Selected: {selectedCompany.company_name}
                    </div>
                    {selectedCompany.investment_criteria_count && selectedCompany.investment_criteria_count > 0 && (
                      <Badge className="bg-blue-100 text-blue-700 border-blue-200 text-xs font-medium px-2 py-0.5">
                        <Target className="h-3 w-3 mr-1" />
                        Has IC ({selectedCompany.investment_criteria_count})
                      </Badge>
                    )}
                  </div>
                  
                  {/* Basic Info */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-3">
                    {selectedCompany.industry && (
                      <div className="flex items-center text-sm text-green-700">
                        <Badge variant="secondary" className="mr-2 text-xs">
                          {selectedCompany.industry}
                        </Badge>
                      </div>
                    )}
                    {selectedCompany.company_website && (
                      <div className="flex items-center text-sm text-green-700">
                        <ExternalLink className="h-3 w-3 mr-1" />
                        <a href={selectedCompany.company_website} target="_blank" rel="noopener noreferrer" className="hover:underline">
                          {selectedCompany.company_website.replace(/^https?:\/\//, '')}
                        </a>
                      </div>
                    )}
                  </div>
                  
                  {/* Enhanced Company Details */}
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-xs text-green-600">
                    {(selectedCompany.company_city || selectedCompany.company_state) && (
                      <div className="flex items-center">
                        <MapPin className="h-3 w-3 mr-1" />
                        {[selectedCompany.company_city, selectedCompany.company_state].filter(Boolean).join(', ')}
                      </div>
                    )}
                    {selectedCompany.company_country && (
                      <div className="flex items-center">
                        <Globe className="h-3 w-3 mr-1" />
                        {selectedCompany.company_country}
                      </div>
                    )}
                    {selectedCompany.employee_count && (
                      <div className="flex items-center">
                        <Users className="h-3 w-3 mr-1" />
                        {selectedCompany.employee_count} employees
                      </div>
                    )}
                    {selectedCompany.revenue_range && (
                      <div className="flex items-center">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        {selectedCompany.revenue_range}
                      </div>
                    )}
                    {selectedCompany.company_phone && (
                      <div className="flex items-center">
                        <Phone className="h-3 w-3 mr-1" />
                        {selectedCompany.company_phone}
                      </div>
                    )}
                    {selectedCompany.company_email && (
                      <div className="flex items-center">
                        <Mail className="h-3 w-3 mr-1" />
                        {selectedCompany.company_email}
                      </div>
                    )}
                  </div>
                  
                  {/* Investment Criteria Selection */}
                  {selectedCompany.investment_criteria_count && selectedCompany.investment_criteria_count > 0 && (
                    <div className="mt-4 space-y-3">
                      <div className="text-sm font-medium text-slate-700 mb-2">
                        Select Investment Criteria to Copy:
                      </div>
                      
                      {loadingIC ? (
                        <div className="flex items-center justify-center py-4">
                          <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                          <span className="ml-2 text-sm text-slate-600">Loading criteria...</span>
                        </div>
                      ) : investmentCriteria.length > 0 ? (
                        <div className="space-y-3">
                          {investmentCriteria.map((ic, index) => (
                            <div
                              key={ic.investment_criteria_id}
                              className={`relative group p-4 border-2 rounded-xl cursor-pointer transition-all duration-300 transform hover:scale-[1.02] ${
                                selectedIC.includes(ic.investment_criteria_id)
                                  ? 'border-blue-500 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-lg shadow-blue-200/50 ring-2 ring-blue-200'
                                  : 'border-slate-200 bg-white hover:border-blue-300 hover:shadow-md'
                              }`}
                              onClick={() => handleICSelection(ic.investment_criteria_id)}
                            >
                              {/* Selection Indicator */}
                              <div className="absolute top-3 right-3">
                                {selectedIC.includes(ic.investment_criteria_id) ? (
                                  <div className="flex items-center gap-1">
                                    <CheckCircle className="h-5 w-5 text-blue-600 animate-pulse" />
                                    <Star className="h-4 w-4 text-yellow-500 animate-bounce" />
                                  </div>
                                ) : (
                                  <div className="w-5 h-5 border-2 border-slate-300 rounded-full group-hover:border-blue-400 transition-colors" />
                                )}
                              </div>

                              <div className="flex items-start justify-between pr-12">
                                <div className="flex-1">
                                  <div className="flex items-center gap-3 mb-2">
                                    <div className={`font-semibold text-lg ${
                                      selectedIC.includes(ic.investment_criteria_id) 
                                        ? 'text-blue-900' 
                                        : 'text-slate-900'
                                    }`}>
                                      {ic.capital_position}
                                    </div>
                                    {selectedIC.includes(ic.investment_criteria_id) && (
                                      <Badge className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white border-0 text-xs font-medium px-3 py-1 shadow-lg animate-pulse">
                                        <Copy className="h-3 w-3 mr-1" />
                                        Will Copy
                                      </Badge>
                                    )}
                                  </div>
                                  
                                  {/* Details Grid */}
                                  <div className="grid grid-cols-1 gap-2 mb-3">
                                    {/* Deal Size */}
                                    {(ic.minimum_deal_size || ic.maximum_deal_size) && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-green-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          Deal Size: 
                                          <span className="ml-1 text-green-600 font-semibold">
                                            {ic.minimum_deal_size ? `$${formatNumber(parseInt(ic.minimum_deal_size))}` : 'No min'} - {ic.maximum_deal_size ? `$${formatNumber(parseInt(ic.maximum_deal_size))}` : 'No max'}
                                          </span>
                                        </div>
                                      </div>
                                    )}
                                    
                                    {/* Geographic Focus */}
                                    {(ic.country || ic.region || ic.state || ic.city) && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-blue-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          Geographic: 
                                          <span className="ml-1 text-blue-600 font-semibold">
                                            {[ic.country, ic.region, ic.state, ic.city].filter(Boolean).slice(0, 2).join(', ')}
                                          </span>
                                        </div>
                                      </div>
                                    )}

                                    {/* Debt-Specific Details */}
                                    {ic.loan_type && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-orange-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          Loan Type: 
                                          <span className="ml-1 text-orange-600 font-semibold">
                                            {ic.loan_type}
                                          </span>
                                        </div>
                                      </div>
                                    )}

                                    {/* Loan Program */}
                                    {ic.loan_program && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-purple-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          Loan Program: 
                                          <span className="ml-1 text-purple-600 font-semibold">
                                            {ic.loan_program}
                                          </span>
                                        </div>
                                      </div>
                                    )}

                                    {/* Loan Terms */}
                                    {(ic.min_loan_term || ic.max_loan_term) && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-indigo-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          Loan Term: 
                                          <span className="ml-1 text-indigo-600 font-semibold">
                                            {ic.min_loan_term ? `${ic.min_loan_term} months` : 'No min'} - {ic.max_loan_term ? `${ic.max_loan_term} months` : 'No max'}
                                          </span>
                                        </div>
                                      </div>
                                    )}

                                    {/* LTV Range */}
                                    {(ic.loan_to_value_min || ic.loan_to_value_max) && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-teal-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          LTV: 
                                          <span className="ml-1 text-teal-600 font-semibold">
                                            {ic.loan_to_value_min ? `${ic.loan_to_value_min}%` : 'No min'} - {ic.loan_to_value_max ? `${ic.loan_to_value_max}%` : 'No max'}
                                          </span>
                                        </div>
                                      </div>
                                    )}

                                    {/* Interest Rate */}
                                    {ic.loan_interest_rate && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-red-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          Interest Rate: 
                                          <span className="ml-1 text-red-600 font-semibold">
                                            {ic.loan_interest_rate}%
                                          </span>
                                        </div>
                                      </div>
                                    )}

                                    {/* Equity-Specific Details */}
                                    {ic.target_return && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-emerald-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          Target Return: 
                                          <span className="ml-1 text-emerald-600 font-semibold">
                                            {ic.target_return}%
                                          </span>
                                        </div>
                                      </div>
                                    )}

                                    {/* IRR */}
                                    {ic.minimum_internal_rate_of_return && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-cyan-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          Min IRR: 
                                          <span className="ml-1 text-cyan-600 font-semibold">
                                            {ic.minimum_internal_rate_of_return}%
                                          </span>
                                        </div>
                                      </div>
                                    )}

                                    {/* Equity Multiple */}
                                    {ic.minimum_equity_multiple && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-pink-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          Min Equity Multiple: 
                                          <span className="ml-1 text-pink-600 font-semibold">
                                            {ic.minimum_equity_multiple}x
                                          </span>
                                        </div>
                                      </div>
                                    )}

                                    {/* Hold Period */}
                                    {(ic.min_hold_period_years || ic.max_hold_period_years) && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-yellow-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          Hold Period: 
                                          <span className="ml-1 text-yellow-600 font-semibold">
                                            {ic.min_hold_period_years ? `${ic.min_hold_period_years} years` : 'No min'} - {ic.max_hold_period_years ? `${ic.max_hold_period_years} years` : 'No max'}
                                          </span>
                                        </div>
                                      </div>
                                    )}

                                    {/* Cash on Cash */}
                                    {ic.target_cash_on_cash_min && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-lime-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          Min Cash on Cash: 
                                          <span className="ml-1 text-lime-600 font-semibold">
                                            {ic.target_cash_on_cash_min}%
                                          </span>
                                        </div>
                                      </div>
                                    )}

                                    {/* Attachment Point */}
                                    {ic.attachment_point && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-amber-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          Attachment Point: 
                                          <span className="ml-1 text-amber-600 font-semibold">
                                            {ic.attachment_point}%
                                          </span>
                                        </div>
                                      </div>
                                    )}

                                    {/* Closing Timeline */}
                                    {ic.typical_closing_timeline_days && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-rose-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          Closing Timeline: 
                                          <span className="ml-1 text-rose-600 font-semibold">
                                            {ic.typical_closing_timeline_days} days
                                          </span>
                                        </div>
                                      </div>
                                    )}

                                    {/* DSCR Range */}
                                    {(ic.min_loan_dscr || ic.max_loan_dscr) && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-fuchsia-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          DSCR: 
                                          <span className="ml-1 text-fuchsia-600 font-semibold">
                                            {ic.min_loan_dscr ? `${ic.min_loan_dscr}` : 'No min'} - {ic.max_loan_dscr ? `${ic.max_loan_dscr}` : 'No max'}
                                          </span>
                                        </div>
                                      </div>
                                    )}

                                    {/* Recourse Loan */}
                                    {ic.recourse_loan && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-sky-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          Recourse: 
                                          <span className="ml-1 text-sky-600 font-semibold">
                                            {ic.recourse_loan}
                                          </span>
                                        </div>
                                      </div>
                                    )}

                                    {/* Amortization */}
                                    {ic.amortization && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-stone-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          Amortization: 
                                          <span className="ml-1 text-stone-600 font-semibold">
                                            {ic.amortization}
                                          </span>
                                        </div>
                                      </div>
                                    )}

                                    {/* Closing Time */}
                                    {ic.closing_time && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-neutral-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          Closing Time: 
                                          <span className="ml-1 text-neutral-600 font-semibold">
                                            {ic.closing_time} days
                                          </span>
                                        </div>
                                      </div>
                                    )}

                                    {/* Max Leverage Tolerance */}
                                    {ic.max_leverage_tolerance && (
                                      <div className="flex items-center gap-2">
                                        <div className="flex-shrink-0 w-2 h-2 bg-zinc-400 rounded-full"></div>
                                        <div className="text-sm font-medium text-slate-700">
                                          Max Leverage: 
                                          <span className="ml-1 text-zinc-600 font-semibold">
                                            {ic.max_leverage_tolerance}%
                                          </span>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                  
                                  {/* Badges Section */}
                                  <div className="flex flex-wrap gap-1 mb-3">
                                    {/* Property Types */}
                                    {ic.property_types && ic.property_types.length > 0 && 
                                      ic.property_types.slice(0, 3).map((type, idx) => (
                                        <Badge 
                                          key={idx} 
                                          variant="secondary" 
                                          className={`text-xs px-2 py-1 ${
                                            selectedIC.includes(ic.investment_criteria_id) 
                                              ? 'bg-blue-100 text-blue-700 border-blue-200' 
                                              : 'bg-slate-100 text-slate-600'
                                          }`}
                                        >
                                          {type}
                                        </Badge>
                                      ))
                                    }
                                    {ic.property_types && ic.property_types.length > 3 && (
                                      <Badge variant="outline" className="text-xs">
                                        +{ic.property_types.length - 3} more
                                      </Badge>
                                    )}
                                    
                                    {/* Strategies */}
                                    {ic.strategies && ic.strategies.length > 0 && 
                                      ic.strategies.slice(0, 2).map((strategy, idx) => (
                                        <Badge 
                                          key={idx} 
                                          variant="secondary" 
                                          className={`text-xs px-2 py-1 ${
                                            selectedIC.includes(ic.investment_criteria_id) 
                                              ? 'bg-purple-100 text-purple-700 border-purple-200' 
                                              : 'bg-slate-100 text-slate-600'
                                          }`}
                                        >
                                          {strategy}
                                        </Badge>
                                      ))
                                    }
                                    {ic.strategies && ic.strategies.length > 2 && (
                                      <Badge variant="outline" className="text-xs">
                                        +{ic.strategies.length - 2} more
                                      </Badge>
                                    )}
                                  </div>
                                  


                                  {/* Footer with IDs */}
                                  {(ic.investment_criteria_debt_id || ic.investment_criteria_equity_id) && (
                                    <div className={`text-xs pt-2 border-t flex items-center gap-3 ${
                                      selectedIC.includes(ic.investment_criteria_id) 
                                        ? 'border-blue-200 text-blue-600' 
                                        : 'border-slate-200 text-slate-500'
                                    }`}>
                                      {ic.investment_criteria_debt_id && (
                                        <div className="flex items-center gap-1">
                                          <div className="w-1 h-1 bg-orange-400 rounded-full"></div>
                                          Debt ID: {ic.investment_criteria_debt_id}
                                        </div>
                                      )}
                                      {ic.investment_criteria_equity_id && (
                                        <div className="flex items-center gap-1">
                                          <div className="w-1 h-1 bg-green-400 rounded-full"></div>
                                          Equity ID: {ic.investment_criteria_equity_id}
                                        </div>
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-sm text-slate-500 text-center py-4">
                          No investment criteria found
                        </div>
                      )}
                      
                      {/* Copy Selected Button and Summary */}
                      {selectedIC.length > 0 && (
                        <div className="mt-6 pt-4 border-t-2 border-blue-200">
                          {/* Selection Summary */}
                          <div className="mb-4 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg">
                            <div className="flex items-center gap-2 mb-2">
                              <Star className="h-4 w-4 text-yellow-500" />
                              <span className="text-sm font-semibold text-blue-900">
                                Ready to Copy {selectedIC.length} Investment Criteria
                              </span>
                            </div>
                            <div className="text-xs text-blue-700">
                              {investmentCriteria
                                .filter(ic => selectedIC.includes(ic.investment_criteria_id))
                                .map(ic => ic.capital_position)
                                .join(', ')
                              }
                            </div>
                          </div>
                          
                          <Button
                            type="button"
                            onClick={() => {
                              const selectedCriteriaData = investmentCriteria.filter(ic => 
                                selectedIC.includes(ic.investment_criteria_id)
                              );
                              if (onInvestmentCriteriaSelect) {
                                onInvestmentCriteriaSelect(selectedCriteriaData);
                              }
                            }}
                            className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white text-sm font-semibold py-3 px-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02]"
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            Copy {selectedIC.length} Selected Criteria to Contact
                            <CheckCircle className="h-4 w-4 ml-2" />
                          </Button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default CompanySearchField;
