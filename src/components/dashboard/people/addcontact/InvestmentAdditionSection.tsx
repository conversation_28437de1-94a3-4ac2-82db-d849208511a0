'use client'

import React, { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Target, Plus, Trash2, CheckCircle, Copy, Star, Building2, Edit, Save, X } from "lucide-react"
import { toast } from "sonner"
import InvestmentCriteriaForm from "../../investment-criteria/InvestmentCriteriaForm"
import { formatNumber, formatRange } from '@/lib/utils'

interface InvestmentCriteriaFormData {
  capital_position: string;
  minimum_deal_size?: number;
  maximum_deal_size?: number;
  country?: string[];
  region?: string[];
  state?: string[];
  city?: string[];
  property_types?: string[];
  property_subcategories?: string[];
  strategies?: string[];
  decision_making_process?: string;
  notes?: string;
  debtModified?: boolean;
  equityModified?: boolean;
  investment_criteria_debt_id?: number;
  investment_criteria_equity_id?: number;
  [key: string]: any; // Allow for debt and equity fields
}

interface InvestmentAdditionSectionProps {
  onInvestmentCriteriaChange: (criteria: InvestmentCriteriaFormData[]) => void;
  selectedCompanyIC?: any[];
  onCompanyICEdit?: (editedIC: any) => void;
  isInCompanyCreationMode?: boolean; // New prop to indicate if this is being used in standalone company creation
}

const DEBT_POSITIONS = ['Senior Debt', 'Stretch Senior', 'Mezzanine'];
const EQUITY_POSITIONS = ['Preferred Equity', 'Common Equity', 'General Partner (GP)', 'Limited Partner (LP)', 'Joint Venture (JV)', 'Co-GP'];

// Note: Simplified approach - for company IC copy, we only send central fields to the API
// The debt/equity details are preserved by referencing the original company's debt/equity IDs

// Function to map database fields to form fields
const mapDatabaseToFormFields = (dbData: any): InvestmentCriteriaFormData => {
  return {
    // Central fields (direct mapping)
    capital_position: dbData.capital_position || '',
    minimum_deal_size: dbData.minimum_deal_size,
    maximum_deal_size: dbData.maximum_deal_size,
    country: dbData.country || [],
    region: dbData.region || [],
    state: dbData.state || [],
    city: dbData.city || [],
    property_types: dbData.property_types || [],
    property_subcategories: dbData.property_subcategories || [],
    strategies: dbData.strategies || [],
    decision_making_process: dbData.decision_making_process || '',
    notes: dbData.notes || '',
    
    // Debt fields (map from database to form format) - FIXED: Added missing fields
    loan_type: dbData.loan_type || '',
    loan_program: dbData.loan_program || '',
    structured_loan_tranche: dbData.structured_loan_tranche || '',
    debt_min_loan_term: dbData.min_loan_term,
    debt_max_loan_term: dbData.max_loan_term,
    debt_loan_interest_rate: dbData.loan_interest_rate,
    debt_loan_to_value_min: dbData.loan_to_value_min,
    debt_loan_to_value_max: dbData.loan_to_value_max,
    debt_loan_to_cost_min: dbData.loan_to_cost_min,
    debt_loan_to_cost_max: dbData.loan_to_cost_max,
    debt_loan_origination_min_fee: dbData.loan_origination_min_fee,
    debt_loan_origination_max_fee: dbData.loan_origination_max_fee,
    debt_loan_exit_min_fee: dbData.loan_exit_min_fee,
    debt_loan_exit_max_fee: dbData.loan_exit_max_fee,
    debt_min_loan_dscr: dbData.min_loan_dscr,
    debt_max_loan_dscr: dbData.max_loan_dscr,
    debt_recourse_loan: dbData.recourse_loan || '',
    debt_lien_position: dbData.lien_position || '',
    debt_notes: dbData.debt_notes || '',
    
    // ADDED: Missing debt fields that were causing debt details to be lost
    debt_loan_interest_rate_based_off_sofr: dbData.loan_interest_rate_based_off_sofr,
    debt_loan_interest_rate_based_off_wsj: dbData.loan_interest_rate_based_off_wsj,
    debt_loan_interest_rate_based_off_prime: dbData.loan_interest_rate_based_off_prime,
    debt_loan_interest_rate_based_off_3yt: dbData.loan_interest_rate_based_off_3yt,
    debt_loan_interest_rate_based_off_5yt: dbData.loan_interest_rate_based_off_5yt,
    debt_loan_interest_rate_based_off_10yt: dbData.loan_interest_rate_based_off_10yt,
    debt_loan_interest_rate_based_off_30yt: dbData.loan_interest_rate_based_off_30yt,
    debt_rate_lock: dbData.rate_lock || '',
    debt_rate_type: dbData.rate_type || '',
    debt_loan_min_debt_yield: dbData.loan_min_debt_yield || '',
    debt_prepayment: dbData.prepayment || '',
    debt_yield_maintenance: dbData.yield_maintenance || '',
    debt_application_deposit: dbData.application_deposit,
    debt_good_faith_deposit: dbData.good_faith_deposit,
    debt_amortization: dbData.amortization || '',
    debt_closing_time: dbData.closing_time,
    debt_future_facilities: dbData.future_facilities || '',
    debt_eligible_borrower: dbData.eligible_borrower || '',
    debt_occupancy_requirements: dbData.occupancy_requirements || '',
    debt_program_overview: dbData.debt_program_overview || '',
    debt_loan_type_normalized: dbData.loan_type_normalized || '',
    
    // Equity fields (map from database to form format)
    equity_target_return: dbData.target_return,
    equity_minimum_internal_rate_of_return: dbData.minimum_internal_rate_of_return,
    equity_minimum_yield_on_cost: dbData.minimum_yield_on_cost,
    equity_minimum_equity_multiple: dbData.minimum_equity_multiple,
    equity_target_cash_on_cash_min: dbData.target_cash_on_cash_min,
    equity_min_hold_period_years: dbData.min_hold_period_years,
    equity_max_hold_period_years: dbData.max_hold_period_years,
    equity_ownership_requirement: dbData.ownership_requirement || '',
    equity_attachment_point: dbData.attachment_point,
    equity_max_leverage_tolerance: dbData.max_leverage_tolerance,
    equity_typical_closing_timeline_days: dbData.typical_closing_timeline_days,
    equity_proof_of_funds_requirement: dbData.proof_of_funds_requirement,
    equity_notes: dbData.equity_notes || '',
    equity_occupancy_requirements: dbData.equity_occupancy_requirements || '',
    
    // ADDED: Missing equity fields
    equity_program_overview: dbData.equity_program_overview || '',
    equity_yield_on_cost: dbData.yield_on_cost,
    equity_target_return_irr_on_equity: dbData.target_return_irr_on_equity,
    equity_equity_multiple: dbData.equity_multiple,
    equity_position_specific_irr: dbData.position_specific_irr,
    equity_position_specific_equity_multiple: dbData.position_specific_equity_multiple,
    
    // IDs for tracking
    investment_criteria_debt_id: dbData.investment_criteria_debt_id,
    investment_criteria_equity_id: dbData.investment_criteria_equity_id
  };
};

export default function InvestmentAdditionSection({ onInvestmentCriteriaChange, selectedCompanyIC, onCompanyICEdit, isInCompanyCreationMode = false }: InvestmentAdditionSectionProps) {
  const [criteriaList, setCriteriaList] = useState<InvestmentCriteriaFormData[]>([]);
  const [showAddForm, setShowAddForm] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [editingIC, setEditingIC] = useState<number | null>(null);
  const [editFormData, setEditFormData] = useState<any>(null);

  const handleSaveCriteria = async (formData: InvestmentCriteriaFormData): Promise<void> => {
    setIsSaving(true);
    try {
      // Add the new criteria to the list
      const updatedList = [...criteriaList, formData];
      setCriteriaList(updatedList);
      onInvestmentCriteriaChange(updatedList);
      
      setShowAddForm(false);
      toast.success('Investment criteria added successfully');
    } catch (error) {
      console.error('Error adding investment criteria:', error);
      toast.error('Failed to add investment criteria');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancelForm = () => {
    setShowAddForm(false);
  };

  const removeCriteria = (index: number) => {
    const updatedList = criteriaList.filter((_, i) => i !== index);
    setCriteriaList(updatedList);
    onInvestmentCriteriaChange(updatedList);
    toast.success('Investment criteria removed');
  };

  const handleEditIC = (icId: number) => {
    const ic = selectedCompanyIC?.find(c => c.investment_criteria_id === icId);
    if (ic) {
      setEditingIC(icId);
      // Transform database fields to form fields
      const mappedData = mapDatabaseToFormFields(ic);
      setEditFormData(mappedData);
    }
  };

  const handleSaveEdit = async (formData: InvestmentCriteriaFormData): Promise<void> => {
    if (!editingIC) return;
    
    try {
      // Get the original criteria
      const originalCriteria = selectedCompanyIC?.find(c => c.investment_criteria_id === editingIC);
      
      if (!originalCriteria) {
        toast.error('Original criteria not found');
        return;
      }
      
      // Create simplified criteria data for company IC copy
      // Only include central fields + reference to original debt/equity IDs
      const editedCriteria = {
        // Central table fields only (user can edit these)
        capital_position: formData.capital_position,
        minimum_deal_size: formData.minimum_deal_size,
        maximum_deal_size: formData.maximum_deal_size,
        country: formData.country,
        region: formData.region,
        state: formData.state,
        city: formData.city,
        property_types: formData.property_types,
        property_subcategories: formData.property_subcategories,
        strategies: formData.strategies,
        decision_making_process: formData.decision_making_process,
        notes: formData.notes,
        
        // Preserve the company's debt/equity IDs (user cannot edit these)
        investment_criteria_debt_id: originalCriteria.investment_criteria_debt_id,
        investment_criteria_equity_id: originalCriteria.investment_criteria_equity_id,
        
        // Flag to indicate this is copied from company
        isCopiedFromCompany: true,
        originalCompanyCriteriaId: originalCriteria.investment_criteria_id
      };
      
      // Update the parent component with the edited IC
      if (onCompanyICEdit) {
        onCompanyICEdit(editedCriteria);
      }
      
      toast.success('Investment criteria updated');
      
      setEditingIC(null);
      setEditFormData(null);
    } catch (error) {
      console.error('Error saving criteria:', error);
      toast.error('Failed to save investment criteria');
    }
  };

  const handleCancelEdit = () => {
    setEditingIC(null);
    setEditFormData(null);
  };

  const generateCriteriaLabel = (criteria: InvestmentCriteriaFormData) => {
    const parts: string[] = [];
    
    if (criteria.capital_position) {
      parts.push(criteria.capital_position);
    }
    
    if (criteria.loan_type) {
      parts.push(criteria.loan_type);
    }
    
    if (criteria.minimum_deal_size || criteria.maximum_deal_size) {
      const dealSizeStr = formatRange(
        criteria.minimum_deal_size || 0, 
        criteria.maximum_deal_size || 0,
        (num) => `$${formatNumber(num)}`
      );
      if (dealSizeStr !== 'Any') {
        parts.push(dealSizeStr);
      }
    }
    
    return parts.join(' | ') || 'Investment Criteria';
  };

  const getCapitalPositionColor = (position: string) => {
    if (DEBT_POSITIONS.includes(position)) {
      return 'bg-blue-50 text-blue-700 border-blue-200';
    } else if (EQUITY_POSITIONS.includes(position)) {
      return 'bg-green-50 text-green-700 border-green-200';
    }
    return 'bg-gray-50 text-gray-700 border-gray-200';
  };

  return (
    <div>
      <Card className="bg-white shadow-xl border-0 rounded-2xl overflow-hidden">
      <CardHeader className="bg-gradient-to-r from-purple-50 to-indigo-50 border-b border-purple-100">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-xl mr-3">
              <Target className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <div className="text-lg font-semibold text-slate-900">Investment Criteria</div>
              <div className="text-sm text-slate-600">
                Add investment criteria for this contact ({criteriaList.length} added)
              </div>
            </div>
          </div>
          
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => setShowAddForm(!showAddForm)}
            className="bg-white hover:bg-purple-50 border-purple-200"
            disabled={showAddForm}
          >
            <Plus className="h-4 w-4 mr-2" />
            {showAddForm ? 'Cancel' : 'Add Criteria'}
          </Button>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="p-6 space-y-6 bg-gradient-to-b from-white to-slate-50">
        {/* Selected Company Investment Criteria */}
        {selectedCompanyIC && selectedCompanyIC.length > 0 && (
          <div className="relative p-6 bg-gradient-to-r from-emerald-50 via-blue-50 to-indigo-50 border-2 border-blue-300 rounded-2xl shadow-lg shadow-blue-200/30">
            {/* Header with animations */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-600 rounded-xl shadow-lg">
                  <Building2 className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-blue-900 flex items-center gap-2">
                    Company Investment Criteria
                    <Star className="h-5 w-5 text-yellow-500 animate-pulse" />
                  </h3>
                  <p className="text-sm text-blue-700 font-medium">
                    {selectedCompanyIC.length} criteria selected for copying
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-6 w-6 text-green-600 animate-bounce" />
                <Badge className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-3 py-1 text-sm font-semibold shadow-lg">
                  <Copy className="h-4 w-4 mr-1" />
                  Ready to Copy
                </Badge>
              </div>
            </div>
            
            {/* Criteria Cards */}
            <div className="grid gap-3">
              {selectedCompanyIC.map((ic, index) => (
                <div 
                  key={index} 
                  className="relative p-4 bg-white border-2 border-blue-200 rounded-xl shadow-md transform transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
                >
                  {/* Animated indicator */}
                  <div className="absolute top-2 right-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse shadow-lg"></div>
                  </div>
                  
                  <div className="flex items-start justify-between pr-6">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="font-bold text-lg text-blue-900">
                          {ic.capital_position}
                        </div>
                        <Badge className="bg-blue-100 text-blue-700 border-blue-200 text-xs font-medium px-2 py-1">
                          IC #{ic.investment_criteria_id}
                        </Badge>
                        {/* Edit Button */}
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditIC(ic.investment_criteria_id)}
                          className="h-8 w-8 p-0 hover:bg-blue-100 text-blue-600"
                          disabled={editingIC === ic.investment_criteria_id}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      {/* Enhanced Details */}
                      <div className="space-y-2">
                        {/* Deal Size */}
                        {(ic.minimum_deal_size || ic.maximum_deal_size) && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                            <span className="text-sm font-medium text-slate-700">
                              Deal Size: 
                              <span className="ml-1 text-green-600 font-semibold">
                                {ic.minimum_deal_size ? `$${formatNumber(parseInt(ic.minimum_deal_size))}` : 'No min'} - {ic.maximum_deal_size ? `$${formatNumber(parseInt(ic.maximum_deal_size))}` : 'No max'}
                              </span>
                            </span>
                          </div>
                        )}
                        
                        {/* Central Table Reference IDs */}
                        <div className="flex items-center gap-4 text-xs bg-slate-50 p-2 rounded-lg border">
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                            <span className="text-slate-600 font-medium">Central IC ID:</span>
                            <span className="font-mono text-blue-600 bg-blue-100 px-1 rounded">{ic.investment_criteria_id}</span>
                          </div>
                          {ic.investment_criteria_debt_id && (
                            <div className="flex items-center gap-1">
                              <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                              <span className="text-slate-600 font-medium">Debt ID:</span>
                              <span className="font-mono text-orange-600 bg-orange-100 px-1 rounded">{ic.investment_criteria_debt_id}</span>
                            </div>
                          )}
                          {ic.investment_criteria_equity_id && (
                            <div className="flex items-center gap-1">
                              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                              <span className="text-slate-600 font-medium">Equity ID:</span>
                              <span className="font-mono text-green-600 bg-green-100 px-1 rounded">{ic.investment_criteria_equity_id}</span>
                            </div>
                          )}
                        </div>
                        
                        {/* Property Types as badges */}
                        {ic.property_types && ic.property_types.length > 0 && (
                          <div className="flex items-center gap-2 flex-wrap">
                            <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                            <span className="text-sm font-medium text-slate-700 mr-2">Property Types:</span>
                            {ic.property_types.slice(0, 3).map((type, idx) => (
                              <Badge key={idx} variant="secondary" className="bg-purple-100 text-purple-700 text-xs">
                                {type}
                              </Badge>
                            ))}
                            {ic.property_types.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{ic.property_types.length - 3} more
                              </Badge>
                            )}
                          </div>
                        )}
                        
                        {/* Strategies as badges */}
                        {ic.strategies && ic.strategies.length > 0 && (
                          <div className="flex items-center gap-2 flex-wrap">
                            <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                            <span className="text-sm font-medium text-slate-700 mr-2">Strategies:</span>
                            {ic.strategies.slice(0, 2).map((strategy, idx) => (
                              <Badge key={idx} variant="secondary" className="bg-orange-100 text-orange-700 text-xs">
                                {strategy}
                              </Badge>
                            ))}
                            {ic.strategies.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{ic.strategies.length - 2} more
                              </Badge>
                            )}
                          </div>
                        )}
                        
                        {/* Geographic info */}
                        {(ic.country?.length > 0 || ic.region?.length > 0 || ic.state?.length > 0 || ic.city?.length > 0) && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                            <span className="text-sm font-medium text-slate-700">
                              Geographic: 
                              <span className="ml-1 text-blue-600 font-semibold">
                                {[
                                  ...(ic.country || []),
                                  ...(ic.region || []),
                                  ...(ic.state || []),
                                  ...(ic.city || [])
                                ].filter(Boolean).slice(0, 3).join(', ')}
                              </span>
                            </span>
                          </div>
                        )}

                        {/* Debt-Specific Details */}
                        {ic.loan_type && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                            <span className="text-sm font-medium text-slate-700">
                              Loan Type: 
                              <span className="ml-1 text-orange-600 font-semibold">
                                {ic.loan_type}
                              </span>
                            </span>
                          </div>
                        )}

                        {/* Loan Program */}
                        {ic.loan_program && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                            <span className="text-sm font-medium text-slate-700">
                              Loan Program: 
                              <span className="ml-1 text-purple-600 font-semibold">
                                {ic.loan_program}
                              </span>
                            </span>
                          </div>
                        )}

                        {/* Loan Terms */}
                        {(ic.min_loan_term || ic.max_loan_term) && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-indigo-400 rounded-full"></div>
                            <span className="text-sm font-medium text-slate-700">
                              Loan Term: 
                              <span className="ml-1 text-indigo-600 font-semibold">
                                {ic.min_loan_term ? `${ic.min_loan_term} months` : 'No min'} - {ic.max_loan_term ? `${ic.max_loan_term} months` : 'No max'}
                              </span>
                            </span>
                          </div>
                        )}

                        {/* LTV Range */}
                        {(ic.loan_to_value_min || ic.loan_to_value_max) && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-teal-400 rounded-full"></div>
                            <span className="text-sm font-medium text-slate-700">
                              LTV: 
                              <span className="ml-1 text-teal-600 font-semibold">
                                {ic.loan_to_value_min ? `${ic.loan_to_value_min}%` : 'No min'} - {ic.loan_to_value_max ? `${ic.loan_to_value_max}%` : 'No max'}
                              </span>
                            </span>
                          </div>
                        )}

                        {/* Interest Rate */}
                        {ic.loan_interest_rate && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                            <span className="text-sm font-medium text-slate-700">
                              Interest Rate: 
                              <span className="ml-1 text-red-600 font-semibold">
                                {ic.loan_interest_rate}%
                              </span>
                            </span>
                          </div>
                        )}

                        {/* Equity-Specific Details */}
                        {ic.target_return && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                            <span className="text-sm font-medium text-slate-700">
                              Target Return: 
                              <span className="ml-1 text-emerald-600 font-semibold">
                                {ic.target_return}%
                              </span>
                            </span>
                          </div>
                        )}

                        {/* IRR */}
                        {ic.minimum_internal_rate_of_return && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                            <span className="text-sm font-medium text-slate-700">
                              Min IRR: 
                              <span className="ml-1 text-cyan-600 font-semibold">
                                {ic.minimum_internal_rate_of_return}%
                              </span>
                            </span>
                          </div>
                        )}

                        {/* Equity Multiple */}
                        {ic.minimum_equity_multiple && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-pink-400 rounded-full"></div>
                            <span className="text-sm font-medium text-slate-700">
                              Min Equity Multiple: 
                              <span className="ml-1 text-pink-600 font-semibold">
                                {ic.minimum_equity_multiple}x
                              </span>
                            </span>
                          </div>
                        )}

                        {/* Hold Period */}
                        {(ic.min_hold_period_years || ic.max_hold_period_years) && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                            <span className="text-sm font-medium text-slate-700">
                              Hold Period: 
                              <span className="ml-1 text-yellow-600 font-semibold">
                                {ic.min_hold_period_years ? `${ic.min_hold_period_years} years` : 'No min'} - {ic.max_hold_period_years ? `${ic.max_hold_period_years} years` : 'No max'}
                              </span>
                            </span>
                          </div>
                        )}

                        {/* Cash on Cash */}
                        {ic.target_cash_on_cash_min && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-lime-400 rounded-full"></div>
                            <span className="text-sm font-medium text-slate-700">
                              Min Cash on Cash: 
                              <span className="ml-1 text-lime-600 font-semibold">
                                {ic.target_cash_on_cash_min}%
                              </span>
                            </span>
                          </div>
                        )}

                        {/* Attachment Point */}
                        {ic.attachment_point && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-amber-400 rounded-full"></div>
                            <span className="text-sm font-medium text-slate-700">
                              Attachment Point: 
                              <span className="ml-1 text-amber-600 font-semibold">
                                {ic.attachment_point}%
                              </span>
                            </span>
                          </div>
                        )}

                        {/* Closing Timeline */}
                        {ic.typical_closing_timeline_days && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-rose-400 rounded-full"></div>
                            <span className="text-sm font-medium text-slate-700">
                              Closing Timeline: 
                              <span className="ml-1 text-rose-600 font-semibold">
                                {ic.typical_closing_timeline_days} days
                              </span>
                            </span>
                          </div>
                        )}

                        {/* Ownership Requirement */}
                        {ic.ownership_requirement && (
                          <div className="flex items-start gap-2">
                            <div className="w-2 h-2 bg-violet-400 rounded-full mt-2 flex-shrink-0"></div>
                            <span className="text-sm font-medium text-slate-700">
                              Ownership: 
                              <span className="ml-1 text-violet-600 font-semibold">
                                {ic.ownership_requirement}
                              </span>
                            </span>
                          </div>
                        )}

                        {/* Proof of Funds */}
                        {ic.proof_of_funds_requirement !== null && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-slate-400 rounded-full"></div>
                            <span className="text-sm font-medium text-slate-700">
                              Proof of Funds: 
                              <span className="ml-1 text-slate-600 font-semibold">
                                {ic.proof_of_funds_requirement ? 'Required' : 'Not Required'}
                              </span>
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>


                  
                  {/* Footer with status */}
                  <div className="mt-3 pt-2 border-t border-blue-100 space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-xs text-blue-600 font-medium">
                        Will be copied to new contact
                      </div>
                      <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs px-2 py-1 shadow-md">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Selected
                      </Badge>
                    </div>
                    
                    {/* Debt & Equity IDs */}
                    <div className="flex items-center gap-4 text-xs">
                      {ic.investment_criteria_debt_id && (
                        <div className="flex items-center gap-1">
                          <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                          <span className="text-slate-600">
                            Debt ID: <span className="font-mono text-orange-600">{ic.investment_criteria_debt_id}</span>
                          </span>
                        </div>
                      )}
                      {ic.investment_criteria_equity_id && (
                        <div className="flex items-center gap-1">
                          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                          <span className="text-slate-600">
                            Equity ID: <span className="font-mono text-green-600">{ic.investment_criteria_equity_id}</span>
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

              ))}
              
              {/* Inline Edit Form */}
              {editingIC && editFormData && (
                <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-xl shadow-lg">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className="text-lg font-semibold text-blue-900">Edit Investment Criteria</h4>
                      <p className="text-sm text-blue-700">
                        Modify and add to contact (does not change company's original criteria)
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCancelEdit}
                      className="h-8 w-8 p-0 text-blue-400 hover:text-blue-600"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <InvestmentCriteriaForm
                    initialData={editFormData}
                    onSave={handleSaveEdit}
                    onCancel={handleCancelEdit}
                    isEditing={true}
                    isSaving={isSaving}
                    showCompanySelection={false}
                    isEditingCopiedIC={true} // Lock debt/equity fields when editing copied IC
                  />
                </div>
              )}
            </div>
            
            {/* Summary footer */}
            <div className="mt-4 p-3 bg-blue-100 border border-blue-300 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="text-sm font-semibold text-blue-900">
                  These {selectedCompanyIC.length} investment criteria will be automatically added to the new contact
                </div>
                <div className="flex items-center gap-1">
                  <Copy className="h-4 w-4 text-blue-600" />
                  <span className="text-xs text-blue-700 font-medium">Auto-copy enabled</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Existing Criteria List */}
        {criteriaList.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-slate-700">Added Investment Criteria:</h4>
            <div className="space-y-2">
              {criteriaList.map((criteria, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-white rounded-lg border border-slate-200">
                  <div className="flex items-center gap-3">
                    <Badge className={getCapitalPositionColor(criteria.capital_position)}>
                      {criteria.capital_position}
                    </Badge>
                    <span className="text-sm text-slate-700">
                      {generateCriteriaLabel(criteria)}
                    </span>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeCriteria(index)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Add New Criteria Form */}
        {showAddForm && (
          <div className="border-t pt-6">
            <InvestmentCriteriaForm
              onSave={handleSaveCriteria}
              onCancel={handleCancelForm}
              isSaving={isSaving}
              showCompanySelection={false}
              isEditingCopiedIC={false} // Allow full editing for new IC
            />
          </div>
        )}
      </CardContent>
    </Card>

  </div>
  );
}
