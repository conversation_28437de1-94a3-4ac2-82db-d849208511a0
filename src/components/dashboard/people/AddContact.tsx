"use client"

import React from 'react'
import { CompanySuggestion } from './shared/types'
import AddContactSimplified from './addcontact/AddContactSimplified'

// Props interface for the AddContact component
interface AddContactProps {
  onBack: () => void;
  companyId?: string;
  preSelectedCompany?: CompanySuggestion;
  onSuccess?: (contactId: number) => void;
}

const AddContact: React.FC<AddContactProps> = ({ onBack, companyId, preSelectedCompany, onSuccess }) => {
  return (
    <AddContactSimplified
      onBack={onBack}
      companyId={companyId}
      preSelectedCompany={preSelectedCompany}
      onSuccess={onSuccess}
    />
  );
};

export default AddContact;
