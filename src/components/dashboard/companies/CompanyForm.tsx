"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import {
  Search,
  ExternalLink,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import InvestmentAdditionSection from "../people/addcontact/InvestmentAdditionSection";

// Debounce utility function
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Define the company data structure for AddContact - simplified fields only
interface CompanyFormData {
  // Core identifiers
  company_id?: number;
  company_name: string;
  
  // Contact information (only fields that should be manually entered)
  company_website?: string;
  company_phone?: string;
  main_email?: string;
  
  // Location information (only fields that should be manually entered)
  company_address?: string;
  company_city?: string;
  company_state?: string;
  company_zip?: string;
  company_country?: string;
}

interface CompanyFormProps {
  companyId?: number | string;
  initialData?: Partial<CompanyFormData>;
  isEmbedded?: boolean;
  isInAddContactMode?: boolean; // New prop to indicate if this is being used in AddContact context
  onSuccess?: (companyId: number) => void;
  onCancel?: () => void;
}

// Phone number validation - only allows digits, spaces, parentheses, dashes, dots, and plus sign
const validatePhoneNumber = (phone: string): boolean => {
  if (!phone) return true; // Empty phone numbers are valid
  const phoneRegex = /^[\d\s\(\)\-\+\.]+$/;
  return phoneRegex.test(phone);
};

// Format phone number input - remove invalid characters
const formatPhoneNumber = (phone: string): string => {
  return phone.replace(/[^\d\s\(\)\-\+\.]/g, '');
};

const normalizeUrl = (url: string): string => {
  if (!url) return url;
  
  // Remove whitespace
  url = url.trim();
  
  // If it doesn't start with http:// or https://, add https://
  if (!url.match(/^https?:\/\//)) {
    url = 'https://' + url;
  }
  
  return url;
};

const isValidUrl = (url: string): boolean => {
  if (!url) return true; // Empty URLs are valid
  try {
    new URL(normalizeUrl(url));
    return true;
  } catch {
    return false;
  }
};

export default function CompanyForm({
  companyId,
  initialData,
  isEmbedded = false,
  isInAddContactMode = false,
  onSuccess,
  onCancel,
}: CompanyFormProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [urlErrors, setUrlErrors] = useState<{ 
    company_website?: string; 
  }>({});
  
  // Phone number validation state
  const [phoneErrors, setPhoneErrors] = useState<{
    company_phone?: string;
  }>({});

  // Company search functionality
  const [companySuggestions, setCompanySuggestions] = useState<any[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  
  // Refs for click-outside detection
  const companyNameInputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const domainInputRef = useRef<HTMLInputElement>(null);
  const domainSuggestionsRef = useRef<HTMLDivElement>(null);

  // Investment Criteria states (matching AddContact pattern)
  const [investmentCriteria, setInvestmentCriteria] = useState<any[]>([]);
  const [selectedCompanyIC, setSelectedCompanyIC] = useState<any[]>([]);

  // Domain search functionality
  const [domainSuggestions, setDomainSuggestions] = useState<any[]>([]);
  const [showDomainSuggestions, setShowDomainSuggestions] = useState(false);
  const [isSearchingDomain, setIsSearchingDomain] = useState(false);
  const [selectedDomainSuggestionIndex, setSelectedDomainSuggestionIndex] = useState(-1);

  const isEditing = !!companyId;

  const [formData, setFormData] = useState<CompanyFormData>({
    company_name: "",
    company_website: "",
    company_phone: "",
    main_email: "",
    company_address: "",
    company_city: "",
    company_state: "",
    company_zip: "",
    company_country: "",
    ...initialData,
  });

  useEffect(() => {
    if (companyId && !initialData) {
      const fetchCompanyData = async () => {
        try {
          const response = await fetch(`/api/companies/${companyId}`);
          if (!response.ok) {
            throw new Error("Failed to fetch company data");
          }
          const data = await response.json();
          setFormData({
            company_id: data.company_id,
            company_name: data.company_name || "",
            company_website: data.company_website || "",
            company_phone: data.company_phone || "",
            main_email: data.main_email || "",
            company_address: data.company_address || "",
            company_city: data.company_city || "",
            company_state: data.company_state || "",
            company_zip: data.company_zip || "",
            company_country: data.company_country || "",
          });
        } catch (error) {
          console.error("Error fetching company data:", error);
          setError("Failed to load company data");
        }
      };

      fetchCompanyData();
    }
  }, [companyId, initialData]);

  // Update form data when initialData changes
  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({
        ...prev,
        ...initialData,
      }));
    }
  }, [initialData]);

  // Cleanup effect to clear suggestions when component unmounts
  useEffect(() => {
    return () => {
      setCompanySuggestions([]);
      setShowSuggestions(false);
      setIsSearching(false);
      setDomainSuggestions([]);
      setShowDomainSuggestions(false);
      setIsSearchingDomain(false);
    };
  }, []);

  // Debounced company search function
  const debouncedSearchCompanies = useCallback(
    debounce(async (searchTerm: string) => {
      console.log('Debounced search called with:', searchTerm, 'Length:', searchTerm.length);
      
      if (searchTerm.length < 2) {
        console.log('Search term too short, clearing suggestions');
        setCompanySuggestions([]);
        setShowSuggestions(false);
        setIsSearching(false);
        return;
      }

      setIsSearching(true);
      try {
        console.log('Making API call for:', searchTerm);
        const url = `/api/companies/search?q=${encodeURIComponent(searchTerm)}`;
        console.log('Full URL:', url);
        
        const response = await fetch(url);
        console.log('Response status:', response.status, response.ok);
        
        if (response.ok) {
          const suggestions = await response.json();
          console.log('Suggestions received:', suggestions);
          setCompanySuggestions(suggestions);
          setShowSuggestions(true);
        } else {
          console.error('Search failed with status:', response.status);
          toast({
            title: "Search Failed",
            description: `Search failed: ${response.status} ${response.statusText}`,
            variant: "destructive",
          });
          setCompanySuggestions([]);
          setShowSuggestions(false);
        }
      } catch (error) {
        console.error('Error searching companies:', error);
        toast({
          title: "Search Error",
          description: "Failed to search companies. Please check your connection and try again.",
          variant: "destructive",
        });
        setCompanySuggestions([]);
        setShowSuggestions(false);
      } finally {
        setIsSearching(false);
      }
    }, 300),
    [toast]
  );

  // Debounced domain search function
  const debouncedSearchByDomain = useCallback(
    debounce(async (domain: string) => {
      console.log('Domain search called with:', domain, 'Length:', domain.length);
      
      if (domain.length < 2) {
        console.log('Domain too short, clearing suggestions');
        setDomainSuggestions([]);
        setShowDomainSuggestions(false);
        setIsSearchingDomain(false);
        return;
      }

      setIsSearchingDomain(true);
      try {
        console.log('Making domain API call for:', domain);
        const url = `/api/companies/search?domain=${encodeURIComponent(domain)}`;
        console.log('Full URL:', url);
        
        const response = await fetch(url);
        console.log('Response status:', response.status, response.ok);
        
        if (response.ok) {
          const suggestions = await response.json();
          console.log('Domain suggestions received:', suggestions);
          setDomainSuggestions(suggestions);
          setShowDomainSuggestions(true);
        } else {
          console.error('Domain search failed with status:', response.status);
          toast({
            title: "Domain Search Failed",
            description: `Domain search failed: ${response.status} ${response.statusText}`,
            variant: "destructive",
          });
          setDomainSuggestions([]);
          setShowDomainSuggestions(false);
        }
      } catch (error) {
        console.error('Error searching companies by domain:', error);
        toast({
          title: "Domain Search Error",
          description: "Failed to search companies by domain. Please check your connection and try again.",
          variant: "destructive",
        });
        setDomainSuggestions([]);
        setShowDomainSuggestions(false);
      } finally {
        setIsSearchingDomain(false);
      }
    }, 300),
    [toast]
  );

  // Handle company name input change
  const handleCompanyNameChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setFormData(prev => ({ ...prev, company_name: value }));
      
      if (value.length >= 2) {
        debouncedSearchCompanies(value);
      } else {
        setCompanySuggestions([]);
        setShowSuggestions(false);
      }
    },
    [debouncedSearchCompanies]
  );

  // Handle website input change with automatic domain search
  const handleWebsiteChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setFormData(prev => ({ ...prev, company_website: value }));
      
      // Clear domain suggestions when user starts typing
      setDomainSuggestions([]);
      setShowDomainSuggestions(false);
      setSelectedDomainSuggestionIndex(-1);
    },
    []
  );

  // Handle company selection from suggestions
  const handleCompanySelect = useCallback((company: any) => {
    console.log('Company selected:', company);
    setFormData(prev => ({ ...prev, company_name: company.company_name }));
    setCompanySuggestions([]);
    setShowSuggestions(false);
    setSelectedSuggestionIndex(-1);
  }, []);

  // Handle keyboard navigation
  const handleCompanyNameKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev < companySuggestions.length - 1 ? prev + 1 : prev
        );
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedSuggestionIndex(prev => prev > 0 ? prev - 1 : -1);
      } else if (e.key === 'Enter' && selectedSuggestionIndex >= 0) {
        e.preventDefault();
        handleCompanySelect(companySuggestions[selectedSuggestionIndex]);
      } else if (e.key === 'Escape') {
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
      }
    },
    [companySuggestions, selectedSuggestionIndex, handleCompanySelect]
  );

  // Handle company link click
  const handleCompanyLinkClick = useCallback((e: React.MouseEvent, companyId: number) => {
    e.stopPropagation();
    router.push(`/dashboard/companies/${companyId}`);
  }, [router]);

  // Handle domain selection from suggestions
  const handleDomainSelect = useCallback((company: any) => {
    console.log('Company selected by domain:', company);
    setFormData(prev => ({ 
      ...prev, 
      company_name: company.company_name,
      company_website: company.company_website || prev.company_website,
      company_address: company.company_address || prev.company_address,
      company_city: company.company_city || prev.company_city,
      company_state: company.company_state || prev.company_state,
      company_country: company.company_country || prev.company_country,
    }));
    setDomainSuggestions([]);
    setShowDomainSuggestions(false);
    setSelectedDomainSuggestionIndex(-1);
  }, []);

  // Handle domain search button click
  const handleDomainSearch = useCallback(() => {
    const domain = formData.company_website;
    if (domain && domain.length >= 2) {
      // Extract domain from URL if it's a full URL
      let domainToSearch = domain;
      
      try {
        if (domain.includes('://')) {
          domainToSearch = new URL(domain).hostname;
        } else {
          // Remove protocol and www if present
          domainToSearch = domain.replace(/^https?:\/\//, '').replace(/^www\./, '');
        }
        
        console.log('Searching by domain:', domainToSearch);
        debouncedSearchByDomain(domainToSearch);
      } catch (error) {
        console.error('Error parsing domain:', error);
        // If URL parsing fails, try to clean the domain manually
        domainToSearch = domain.replace(/^https?:\/\//, '').replace(/^www\./, '');
        debouncedSearchByDomain(domainToSearch);
      }
    }
  }, [formData.company_website, debouncedSearchByDomain]);

  // Handle domain keyboard navigation
  const handleDomainKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedDomainSuggestionIndex(prev => 
          prev < domainSuggestions.length - 1 ? prev + 1 : prev
        );
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedDomainSuggestionIndex(prev => prev > 0 ? prev - 1 : -1);
      } else if (e.key === 'Enter' && selectedDomainSuggestionIndex >= 0) {
        e.preventDefault();
        handleDomainSelect(domainSuggestions[selectedDomainSuggestionIndex]);
      } else if (e.key === 'Escape') {
        setShowDomainSuggestions(false);
        setSelectedDomainSuggestionIndex(-1);
      }
    },
    [domainSuggestions, selectedDomainSuggestionIndex, handleDomainSelect]
  );

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      setLoading(true);
      setError(null);

      try {
        const url = isEditing
          ? `/api/companies/${companyId}`
          : "/api/companies";

        const method = isEditing ? "PUT" : "POST";

        const response = await fetch(url, {
          method,
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(formData),
        });

        if (!response.ok) {
          throw new Error(
            `Failed to ${isEditing ? "update" : "create"} company`
          );
        }

        const data = await response.json();
        const savedCompanyId = data.company_id;
            
            toast({
              title: "Success",
          description: "Company saved successfully",
        });

        if (onSuccess) {
          onSuccess(savedCompanyId);
        } else {
          router.push(`/dashboard/companies/${savedCompanyId}`);
          router.refresh();
        }
      } catch (error) {
        console.error(
          `Error ${isEditing ? "updating" : "creating"} company:`,
          error
        );
        setError(
          `Failed to ${
            isEditing ? "update" : "create"
          } company. Please try again.`
        );
      } finally {
        setLoading(false);
      }
    },
    [formData, isEditing, companyId, onSuccess, router, toast]
  );

  // Investment Criteria handlers (matching AddContact pattern)
  const handleCompanyICEdit = (companyId: number) => {
    // Handle company investment criteria editing
    console.log('Editing company IC for company:', companyId);
  };

  // Click-outside handler for company suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Handle company name suggestions
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target as Node) &&
        companyNameInputRef.current &&
        !companyNameInputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
      }
      
      // Handle domain suggestions
      if (
        domainSuggestionsRef.current &&
        !domainSuggestionsRef.current.contains(event.target as Node) &&
        domainInputRef.current &&
        !domainInputRef.current.contains(event.target as Node)
      ) {
        setShowDomainSuggestions(false);
        setSelectedDomainSuggestionIndex(-1);
      }
    };

    if (showSuggestions || showDomainSuggestions) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showSuggestions, showDomainSuggestions]);

  // Helper function to update phone fields with validation
  const updatePhoneField = useCallback((field: string, value: string) => {
    const formattedValue = formatPhoneNumber(value);
    
    // Validate phone number
    if (formattedValue && !validatePhoneNumber(formattedValue)) {
      setPhoneErrors(prev => ({
        ...prev,
        [field]: 'Phone number can only contain digits, spaces, parentheses, dashes, dots, and plus sign'
      }));
      return;
    }
    
    // Clear error if validation passes
    if (phoneErrors[field as keyof typeof phoneErrors]) {
      setPhoneErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
    
    setFormData(prev => ({
      ...prev,
      [field]: formattedValue
    }));
  }, [phoneErrors]);

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const { name, value } = e.target;
      
      // Handle phone number fields with validation
      if (name === 'company_phone') {
        updatePhoneField(name, value);
        return;
      }
      
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));

      // Clear URL validation errors when user types
      if (name === 'company_website') {
        setUrlErrors(prev => ({
          ...prev,
          [name]: undefined
        }));
      }
    },
    [updatePhoneField]
  );

  // Handle URL field blur for validation and normalization
  const handleUrlBlur = useCallback(
    (fieldName: 'company_website') => {
      const value = formData[fieldName];
      if (!value) return;

      if (!isValidUrl(value)) {
        setUrlErrors(prev => ({
          ...prev,
          [fieldName]: 'Please enter a valid URL'
        }));
        return;
      }

      // Normalize the URL
      const normalizedUrl = normalizeUrl(value);
      if (normalizedUrl !== value) {
        setFormData(prev => ({
          ...prev,
          [fieldName]: normalizedUrl
        }));
      }
    },
    [formData]
  );

  const ContentWrapper = ({
    children,
    isEmbedded,
  }: {
    children: React.ReactNode;
    isEmbedded: boolean;
  }) => {
    if (isEmbedded) {
      return <div className="space-y-6">{children}</div>;
    }
    return (
      <Card>
        <CardContent className="p-6">{children}</CardContent>
      </Card>
    );
  };

  return (
    <div className="max-w-4xl mx-auto p-8 bg-gradient-to-br from-blue-50 to-indigo-50 min-h-screen">
      <div className="bg-white rounded-xl shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 px-8 py-6">
          <h1 className="text-3xl font-bold text-white mb-2">
            {isEditing ? "Edit Company" : "Add New Company"}
          </h1>
          <p className="text-blue-100">
            {isEditing ? "Update company information" : "Enter basic company details"}
          </p>
        </div>

        {error && (
          <div className="mx-8 mt-6 p-4 bg-red-50 border-l-4 border-red-400 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-red-800 font-medium">{error}</p>
              </div>
            </div>
        </div>
      )}

        <form onSubmit={handleSubmit} className="p-8 space-y-8">
          {/* Company Information Section */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-100">
            <div className="p-6">
              <div className="flex items-center mb-6">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                  <div className="text-blue-600">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
                  </div>
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">Company Information</h2>
                  <p className="text-sm text-gray-500">Basic company details and identification</p>
                </div>
              </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="space-y-2">
                <Label htmlFor="company_name" className="text-sm font-medium text-gray-700">
                        Company Name *
                      </Label>
                      <div className="relative">
                      <Input
                        ref={companyNameInputRef}
                        id="company_name"
                        name="company_name"
                        value={formData.company_name}
                          onChange={handleCompanyNameChange}
                          onKeyDown={handleCompanyNameKeyDown}
                                                  placeholder="Enter company name"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 truncate"
                        required
                        />
                        
                        {/* Company Search Results */}
                        {showSuggestions && (
                          <div 
                            ref={suggestionsRef}
                            className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto"
                          >
                            {companySuggestions.length > 0 ? (
                              <div className="p-2 space-y-1">
                                {companySuggestions.map((company, index) => (
                                  <div
                                    key={company.company_id}
                                    className={`p-3 rounded-lg cursor-pointer border transition-all duration-200 ${
                                      index === selectedSuggestionIndex 
                                        ? 'bg-emerald-50 border-emerald-300 shadow-sm' 
                                        : 'bg-white border-slate-200 hover:bg-slate-50 hover:border-slate-300'
                                    }`}
                                    onClick={() => handleCompanySelect(company)}
                                  >
                                    <div className="flex items-start justify-between">
                                      <div className="flex-1">
                                        <div className="font-medium text-gray-900 text-sm flex items-center">
                                          {company.company_name}
                                          <button
                                            type="button"
                                            onClick={(e) => handleCompanyLinkClick(e, company.company_id)}
                                            className="ml-2 p-1 text-gray-400 hover:text-blue-600 transition-colors"
                                            title="View company details"
                                          >
                                            <ExternalLink className="h-3 w-3" />
                                          </button>
                                        </div>
                                        {company.industry && (
                                          <div className="text-xs text-gray-500 mt-1">
                                            {company.industry}
                                          </div>
                                        )}
                                    {company.company_city && company.company_state && (
                                          <div className="text-xs text-gray-500">
                                        {company.company_city}, {company.company_state}
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : isSearching ? (
                              <div className="p-4 text-center border rounded-xl bg-slate-50">
                                <div className="text-sm text-gray-600">
                                  <Search className="h-4 w-4 mx-auto mb-2 text-gray-400" />
                                  Searching for companies...
                                </div>
                              </div>
                            ) : (
                              <div className="p-4 text-center border rounded-xl bg-slate-50">
                                <div className="text-sm text-gray-600">
                                  <Search className="h-4 w-4 mx-auto mb-2 text-gray-400" />
                                  No companies found matching "{formData.company_name}"
                                </div>
                                <div className="text-xs text-gray-500 mt-1">
                                  Try a different search term or continue with a new company
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                  <Label htmlFor="company_website" className="text-sm font-medium text-gray-700">
                  Website
                      </Label>
                      <div className="relative">
                        <Input
                          ref={domainInputRef}
                      id="company_website"
                      name="company_website"
                      value={formData.company_website}
                          onChange={handleWebsiteChange}
                      onBlur={() => handleUrlBlur('company_website')}
                  onKeyDown={handleDomainKeyDown}
                  placeholder="https://example.com"
                      className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 truncate pr-12 ${urlErrors.company_website ? 'border-red-300' : ''}`}
                        />
                        <Button
                          type="button"
                          onClick={handleDomainSearch}
                      disabled={!formData.company_website || formData.company_website.length < 2 || isSearchingDomain}
                          className="absolute right-1 top-1 h-8 w-8 p-0 bg-blue-600 hover:bg-blue-700 text-white disabled:bg-gray-300 disabled:cursor-not-allowed"
                          title="Search for companies with this domain"
                        >
                          {isSearchingDomain ? (
                            <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                          ) : (
                            <Search className="h-4 w-4" />
                          )}
                        </Button>
                        
                        {/* Domain Search Results */}
                        {showDomainSuggestions && (
                          <div 
                            ref={domainSuggestionsRef}
                            className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto"
                          >
                            {domainSuggestions.length > 0 ? (
                              <div className="p-2 space-y-1">
                                {domainSuggestions.map((company, index) => (
                                  <div
                                    key={company.company_id}
                                    className={`p-3 rounded-lg cursor-pointer border transition-all duration-200 ${
                                      index === selectedDomainSuggestionIndex 
                                        ? 'bg-emerald-50 border-emerald-300 shadow-sm' 
                                        : 'bg-white border-slate-200 hover:bg-slate-50 hover:border-slate-300'
                                    }`}
                                    onClick={() => handleDomainSelect(company)}
                                  >
                                    <div className="flex items-start justify-between">
                                      <div className="flex-1">
                                        <div className="font-medium text-gray-900 text-sm flex items-center">
                                          {company.company_name}
                                          <button
                                            type="button"
                                            onClick={(e) => handleCompanyLinkClick(e, company.company_id)}
                                            className="ml-2 p-1 text-gray-400 hover:text-blue-600 transition-colors"
                                            title="View company details"
                                          >
                                            <ExternalLink className="h-3 w-3" />
                                          </button>
                                        </div>
                                        {company.company_website && (
                                          <div className="text-xs text-gray-500 mt-1">
                                            {company.company_website}
                                          </div>
                                        )}
                                        {company.industry && (
                                          <div className="text-xs text-gray-500">
                                            {company.industry}
                                          </div>
                                        )}
                                        {company.company_city && company.company_state && (
                                          <div className="text-xs text-gray-500">
                                            {company.company_city}, {company.company_state}
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ) : isSearchingDomain ? (
                              <div className="p-4 text-center border rounded-xl bg-slate-50">
                                <div className="text-sm text-gray-600">
                                  <Search className="h-4 w-4 mx-auto mb-2 text-gray-400" />
                                  Searching for companies...
                                </div>
                              </div>
                            ) : (
                              <div className="p-4 text-center border rounded-xl bg-slate-50">
                                <div className="text-sm text-gray-600">
                                  <Search className="h-4 w-4 mx-auto mb-2 text-gray-400" />
                              No companies found with domain "{formData.company_website}"
                                </div>
                                <div className="text-xs text-gray-500 mt-1">
                                  Try a different domain or continue with a new company
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                  {urlErrors.company_website && (
                    <p className="text-red-600 text-xs">{urlErrors.company_website}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                  <Label htmlFor="company_phone" className="text-sm font-medium text-gray-700">
                    Company Phone
                      </Label>
                      <Input
                    id="company_phone"
                    name="company_phone"
                    value={formData.company_phone}
                        onChange={handleChange}
                  placeholder="+****************"
                    className={`border-gray-300 focus:border-blue-500 focus:ring-blue-500 ${phoneErrors.company_phone ? 'border-red-300' : ''}`}
                      />
                  {phoneErrors.company_phone && (
                    <p className="text-red-600 text-xs">{phoneErrors.company_phone}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                <Label htmlFor="main_email" className="text-sm font-medium text-gray-700">
                  Main Email
                      </Label>
                      <Input
                  id="main_email"
                  name="main_email"
                  type="email"
                  value={formData.main_email}
                        onChange={handleChange}
                  placeholder="<EMAIL>"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                  <Label htmlFor="company_address" className="text-sm font-medium text-gray-700">
                    Address
                </Label>
                                 <Input
                    id="company_address"
                    name="company_address"
                    value={formData.company_address}
                        onChange={handleChange}
                  placeholder="123 Main Street"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 truncate"
                      />
                    </div>

                    <div className="space-y-2">
                  <Label htmlFor="company_city" className="text-sm font-medium text-gray-700">
                        City
                      </Label>
                      <Input
                    id="company_city"
                    name="company_city"
                    value={formData.company_city}
                        onChange={handleChange}
                  placeholder="San Francisco"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div className="space-y-2">
                  <Label htmlFor="company_state" className="text-sm font-medium text-gray-700">
                  State
                      </Label>
                      <Input
                    id="company_state"
                    name="company_state"
                    value={formData.company_state}
                        onChange={handleChange}
                  placeholder="CA"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div className="space-y-2">
                  <Label htmlFor="company_zip" className="text-sm font-medium text-gray-700">
                  ZIP Code
                      </Label>
                      <Input
                    id="company_zip"
                    name="company_zip"
                    value={formData.company_zip}
                        onChange={handleChange}
                  placeholder="94105"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>

                    <div className="space-y-2">
                  <Label htmlFor="company_country" className="text-sm font-medium text-gray-700">
                        Country
                      </Label>
                      <Input
                    id="company_country"
                    name="company_country"
                    value={formData.company_country}
                        onChange={handleChange}
                  placeholder="United States"
                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                    </div>
                    </div>

                    {/* Investment Criteria Section */}
          <InvestmentAdditionSection
            onInvestmentCriteriaChange={setInvestmentCriteria}
            selectedCompanyIC={selectedCompanyIC}
            onCompanyICEdit={handleCompanyICEdit}
            isInCompanyCreationMode={!isInAddContactMode} // Pass the inverse - when NOT in AddContact mode, we're in pure company creation mode
          />

          {/* Form Actions */}
          {!isEmbedded && (
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                className="px-6 py-2"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className="px-6 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium"
              >
                {loading ? (
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {isEditing ? 'Updating...' : 'Creating...'}
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      {isEditing ? 'Update Company' : 'Create Company'}
                    </div>
                )}
              </Button>
            </div>
          )}
          
          {/* Embedded Form Actions */}
          {isEmbedded && (
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                className="px-6 py-2"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className="px-6 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium"
              >
                {loading ? (
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {isEditing ? 'Updating...' : 'Creating...'}
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      {isEditing ? 'Update Company' : 'Create Company'}
                    </div>
                )}
              </Button>
            </div>
                    )}
        </form>


      </div>
    </div>
  );
}