'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>List, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Progress } from "@/components/ui/progress";
import { Building2, Building, Search, Users, Newspaper, Briefcase, Mail, Phone, Globe, MapPin, 
         ChevronRight, MessageSquare, Plus, ExternalLink, Sparkles, Target, 
         UserPlus, History, Zap, Loader2, RefreshCw } from 'lucide-react';
import CompanyForm from '../companies/CompanyForm';
import { useRouter } from 'next/navigation';
import { useToast } from "@/hooks/use-toast";
import InvestmentCriteriaSliderV2 from '../investment-criteria/InvestmentCriteriaSliderV2';

interface MetricItemProps {
  label: string;
  value: string;
  change?: string;
}

const MetricItem = ({ label, value, change }: MetricItemProps) => (
  <div className="bg-gray-50/50 p-4 rounded-lg border border-gray-100">
    <p className="text-sm font-medium text-gray-500">{label}</p>
    <h4 className="text-2xl font-semibold mt-1">{value}</h4>
    {change && (
      <p className="text-xs font-medium text-green-600 mt-1">
        {change}
      </p>
    )}
  </div>
);

interface ActivityItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  time: string;
}

const ActivityItem = ({ icon, title, description, time }: ActivityItemProps) => (
  <div className="flex items-start space-x-3">
    <div className="h-8 w-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center">
      {icon}
    </div>
    <div className="flex-1">
      <div className="font-medium">{title}</div>
      <div className="text-sm text-gray-600">{description}</div>
    </div>
    <div className="text-sm text-gray-500">{time}</div>
  </div>
);

interface ContactItemProps {
  name: string;
  title: string;
  email: string;
  phone?: string;
  relationship?: number;
}

const ContactItem = ({ name, title, email, phone, relationship }: ContactItemProps) => (
  <div className="p-4 border border-gray-100 rounded-lg hover:bg-gray-50">
    <div className="flex justify-between items-start">
      <div>
        <div className="font-medium">{name}</div>
        {title && <div className="text-sm text-gray-500">{title}</div>}
      </div>
      <Button size="sm" variant="outline">Contact</Button>
    </div>
    {(email || phone) && (
      <div className="flex mt-2 text-sm text-gray-500 space-x-4">
        {email && (
          <div>
            Email: <a href={`mailto:${email}`} className="text-blue-600 hover:underline">{email}</a>
          </div>
        )}
        {phone && (
          <div>
            Phone: <a href={`tel:${phone}`} className="text-blue-600 hover:underline">{phone}</a>
          </div>
        )}
      </div>
    )}
  </div>
);

interface DealItemProps {
  title: string;
  type: string;
  value: string;
  stage: string;
  progress: number;
}

const DealItem = ({ title, type, value, stage, progress }: DealItemProps) => (
  <div className="p-4 border rounded-lg">
    <div className="flex justify-between items-start mb-3">
      <div>
        <div className="font-medium">{title}</div>
        <div className="text-sm text-gray-500">{type}</div>
      </div>
      <Badge>{stage}</Badge>
    </div>
    <div className="flex items-center justify-between">
      <div className="text-lg font-medium">{value}</div>
      <Progress value={progress} className="w-32" />
    </div>
  </div>
);

interface NewsItemProps {
  title: string;
  source: string;
  date: string;
  sentiment: 'positive' | 'neutral' | 'negative';
}

const NewsItem = ({ title, source, date, sentiment }: NewsItemProps) => {
  const sentimentColors = {
    positive: 'text-green-600 bg-green-50',
    neutral: 'text-gray-600 bg-gray-50',
    negative: 'text-red-600 bg-red-50'
  };

  return (
    <div className="p-4 border rounded-lg">
      <div className="flex items-start justify-between">
        <div>
          <div className="font-medium">{title}</div>
          <div className="flex items-center space-x-3 mt-1">
            <span className="text-sm text-gray-500">{source}</span>
            <span className="text-sm text-gray-500">{date}</span>
          </div>
        </div>
        <Badge className={sentimentColors[sentiment]}>
          {sentiment.charAt(0).toUpperCase() + sentiment.slice(1)}
        </Badge>
      </div>
    </div>
  );
};

interface InsightItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const InsightItem = ({ icon, title, description }: InsightItemProps) => (
  <div className="flex space-x-3">
    <div className="flex-shrink-0 h-9 w-9 rounded-full flex items-center justify-center bg-blue-50 text-blue-600">
      {icon}
    </div>
    <div>
      <h4 className="text-sm font-medium">{title}</h4>
      <p className="text-sm text-gray-600 mt-1">{description}</p>
    </div>
  </div>
);



interface ActionItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
}

const ActionItem = ({ icon, title, description, priority }: ActionItemProps) => (
  <div className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer">
    <div className={`h-8 w-8 rounded-full flex items-center justify-center
      ${priority === 'high' ? 'bg-red-100 text-red-600' : 
        priority === 'medium' ? 'bg-yellow-100 text-yellow-600' : 
        'bg-blue-100 text-blue-600'}`}>
      {icon}
    </div>
    <div>
      <div className="font-medium">{title}</div>
      <div className="text-sm text-gray-600">{description}</div>
    </div>
    <ChevronRight className="h-5 w-5 text-gray-400 ml-auto" />
  </div>
);

interface CompanyData {
  company_id?: number;
  company_name?: string;
  name?: string;
  industry?: string;
  company_address?: string;
  company_city?: string;
  company_state?: string;
  company_country?: string;
  company_website?: string;
  company_linkedin?: string;
  summary?: string;
  founded_year?: number;
  contact_count?: string | number;
  risk_factors?: string[];
  recent_developments?: string[];
  engagement_opportunities?: string[];
  overview?: {
    structure_history?: string;
    executive_contacts?: any[];
    investment_program?: any;
    lending_program?: any;
    capital_commitments?: any;
    investment_strategy?: string[];
    recent_transactions?: any[];
    hold_horizon?: string;
  };
  scraped_data?: any;
  scraped_contacts?: any[];
  contacts?: any[];
  website_scraping_status?: string;
  company_overview_status?: string;
  overview_v2_status?: string;
  investment_criteria_status?: string;
}

interface CompanyViewProps {
  company?: CompanyData;
  isEmbedded?: boolean;
  onAddContact?: () => void;
}

const CompanyView: React.FC<CompanyViewProps> = ({ 
  company = {}, 
  isEmbedded = false,
  onAddContact
}) => {
  const [isAddingCompany, setIsAddingCompany] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingStage, setProcessingStage] = useState<string>('');
  const router = useRouter();
  const { toast } = useToast();
  
  // Extract company data with fallbacks
  const companyName = company.company_name || company.name || "Company Name";
  const industry = company.industry || company.overview?.investment_program?.asset_type?.[0] || "Not specified";
  const location = company.company_city && company.company_state ? 
    `${company.company_city}, ${company.company_state}` : 
    company.company_city || company.company_state || company.scraped_data?.companyProfile?.headquarters || "Location not specified";
  const website = company.company_website || company.scraped_data?.contactInfo?.website || "#";
  const websiteDomain = website !== "#" ? website.replace(/^https?:\/\//, '').replace(/\/$/, '') : "website";
  const summary = company.summary || "No company description available.";
  const contactCount = company.contact_count || company.contacts?.length || 0;
  const initials = companyName.substring(0, 2).toUpperCase();

  // Function to handle processing triggers
  const handleProcessingTrigger = async (stage: string, stageName: string) => {
    if (!company.company_id) {
      toast({
        title: "Error",
        description: "Company ID is required to trigger processing",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    setProcessingStage(stage);

    try {
      const response = await fetch('/api/processing/trigger', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'execute_manual',
          stage: stage,
          entityType: 'company',
          options: {
            multiIds: [company.company_id],
            limit: 1, // Single company processing
            batchSize: 1, // Single company batch size
            filters: {}
          }
        })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        toast({
          title: "Processing Started",
          description: `${stageName} has been triggered successfully for ${companyName}`,
        });
        
        // Refresh after a short delay
        setTimeout(() => {
          router.refresh();
        }, 2000);
      } else {
        throw new Error(data.error || 'Failed to trigger processing');
      }
    } catch (error) {
      console.error(`Error triggering ${stageName}:`, error);
      toast({
        title: "Processing Error",
        description: error instanceof Error ? error.message : 'Failed to trigger processing',
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      setProcessingStage('');
    }
  };



  // Function to handle creating a new company
  const handleAddCompany = () => {
    setIsAddingCompany(true);
  };

  const handleCancelAddCompany = () => {
    setIsAddingCompany(false);
  };

  // Determine if we should show the header and sidebar based on if this is embedded
  const showHeader = !isEmbedded;
  const containerClass = isEmbedded ? "" : "h-screen bg-gray-50 overflow-auto";
  
  // If we're adding a company, show the company form
  if (isAddingCompany) {
    return (
      <div className="p-6 max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Add New Company</h1>
          <Button variant="ghost" onClick={handleCancelAddCompany}>Cancel</Button>
        </div>
        <CompanyForm 
          isEmbedded={true}
          onCancel={handleCancelAddCompany}
          onSuccess={(companyId) => {
            setIsAddingCompany(false);
            router.push(`/dashboard/companies/${companyId}`);
            router.refresh();
          }}
        />
      </div>
    );
  }
  
  return (
    <div className={containerClass}>
      {/* Company Header - only show if not embedded */}
      {showHeader && (
        <div className="bg-white border-b border-gray-200 px-8 py-6">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-4">
              <Avatar className="h-16 w-16">
                <AvatarFallback className="bg-blue-600">{initials}</AvatarFallback>
              </Avatar>
              <div>
                <div className="flex items-center space-x-3">
                  <h1 className="text-2xl font-bold">{companyName}</h1>
                  {company.scraped_data?.companyProfile?.companyType && (
                    <Badge className="bg-blue-100 text-blue-800">
                      {company.scraped_data.companyProfile.companyType}
                    </Badge>
                  )}
                </div>
                <div className="flex items-center mt-1 text-gray-500 space-x-4">
                  <div className="flex items-center">
                    <Building2 className="h-4 w-4 mr-2" />
                    <span>{industry}</span>
                  </div>
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2" />
                    <span>{location}</span>
                  </div>
                  <div className="flex items-center">
                    <Globe className="h-4 w-4 mr-2" />
                    <a href={website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                      {websiteDomain}
                    </a>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" onClick={handleAddCompany}>
                <Plus className="h-4 w-4 mr-2" />
                Add Company
              </Button>
              {onAddContact && (
                <Button variant="outline" onClick={onAddContact}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add Contact
                </Button>
              )}
              <Button>
                <MessageSquare className="h-4 w-4 mr-2" />
                Start Engagement
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className={isEmbedded ? "" : "px-8 py-6"}>
        <div className="grid grid-cols-3 gap-6">
          <div className="col-span-2">
            <Tabs defaultValue="overview">
              <TabsList>
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="contacts">Contacts ({contactCount})</TabsTrigger>
                <TabsTrigger value="investment-criteria">Investment Criteria</TabsTrigger>
                <TabsTrigger value="deals">Deals & News</TabsTrigger>
                <TabsTrigger value="insights">AI Insights</TabsTrigger>
                <TabsTrigger value="v2-processing">V2 Processing</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Company Overview</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <p className="text-gray-600">
                        {summary}
                      </p>
                      <div className="grid grid-cols-3 gap-4">
                        {company.scraped_data?.companyProfile?.aum && (
                          <MetricItem 
                            label="AUM" 
                            value={company.scraped_data.companyProfile.aum} 
                            change="" 
                          />
                        )}
                        {company.founded_year && (
                          <MetricItem 
                            label="Founded" 
                            value={company.founded_year.toString()} 
                            change="" 
                          />
                        )}
                        {company.scraped_data?.companyProfile?.numberOfEmployees && (
                          <MetricItem 
                            label="Employees" 
                            value={company.scraped_data.companyProfile.numberOfEmployees} 
                            change="" 
                          />
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="contacts" className="space-y-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between">
                    <CardTitle>Company Contacts</CardTitle>
                    <Button size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Contact
                    </Button>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {company.contacts && company.contacts.length > 0 ? (
                      company.contacts.map((contact, index) => (
                        <ContactItem 
                          key={contact.contact_id || index}
                          name={`${contact.first_name} ${contact.last_name}`}
                          title={contact.title || ""}
                          email={contact.email || ""}
                          phone={contact.phone_number}
                        />
                      ))
                    ) : company.scraped_contacts && company.scraped_contacts.length > 0 ? (
                      company.scraped_contacts.map((contact, index) => (
                        <ContactItem 
                          key={index}
                          name={contact.full_name || `${contact.first_name || ""} ${contact.last_name || ""}`.trim()}
                          title={contact.title || ""}
                          email={contact.email || contact.personal_email || ""}
                          phone={contact.phone}
                        />
                      ))
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        No contacts found for this company
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="investment-criteria" className="space-y-6">
                <InvestmentCriteriaSliderV2
                  entityType="company"
                  entityId={company.company_id || 0}
                  entityName={companyName}
                />
              </TabsContent>

              <TabsContent value="deals" className="space-y-6">
                {company.overview?.recent_transactions && company.overview.recent_transactions.length > 0 ? (
                  <Card>
                    <CardHeader>
                      <CardTitle>Recent Transactions</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {company.overview.recent_transactions.map((transaction, index) => (
                        <div key={index} className="p-4 border rounded-lg">
                          <div className="flex justify-between items-start">
                            <div>
                              <div className="font-medium">{transaction.asset_type}</div>
                              <div className="text-sm text-gray-500">{transaction.location}</div>
                            </div>
                            {transaction.capital_deployed && (
                              <Badge>{transaction.capital_deployed}</Badge>
                            )}
                          </div>
                          {transaction.close_date && (
                            <div className="text-xs text-gray-500 mt-2">
                              Closed: {transaction.close_date}
                            </div>
                          )}
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardContent className="py-8">
                      <div className="text-center text-gray-500">
                        No recent transactions found
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="insights" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>AI Generated Insights</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {company.engagement_opportunities && company.engagement_opportunities.length > 0 ? (
                      company.engagement_opportunities.map((opportunity, index) => (
                        <InsightItem 
                          key={index}
                          icon={<Sparkles className="h-4 w-4" />}
                          title={`Opportunity ${index + 1}`}
                          description={opportunity}
                        />
                      ))
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        No AI insights available yet
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="v2-processing" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>V2 Company Processing</CardTitle>
                    <CardDescription>
                      Trigger AI-powered processing to extract and analyze company data
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Processing Status */}
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="font-medium mb-3">Processing Status</h4>
                        <div className="grid grid-cols-2 gap-3 text-sm">
                          <div className="flex items-center justify-between">
                            <span>Website Scraping:</span>
                            <Badge variant={company.website_scraping_status === 'completed' ? 'default' : 'secondary'}>
                              {company.website_scraping_status || 'pending'}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between">
                            <span>Company Overview:</span>
                            <Badge variant={company.company_overview_status === 'completed' ? 'default' : 'secondary'}>
                              {company.company_overview_status || 'pending'}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between">
                            <span>Company Overview V2:</span>
                            <Badge variant={company.overview_v2_status === 'completed' ? 'default' : 'secondary'}>
                              {company.overview_v2_status || 'pending'}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between">
                            <span>Investment Criteria:</span>
                            <Badge variant={company.investment_criteria_status === 'completed' ? 'default' : 'secondary'}>
                              {company.investment_criteria_status || 'pending'}
                            </Badge>
                          </div>
                        </div>
                      </div>

                      {/* Processing Actions */}
                      <div>
                        <h4 className="font-medium mb-3">Processing Actions</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <Button 
                            className="w-full justify-start" 
                            variant="outline"
                            onClick={() => handleProcessingTrigger('website_scraping', 'V2 Web Crawler')}
                            disabled={isProcessing && processingStage === 'website_scraping'}
                          >
                            {isProcessing && processingStage === 'website_scraping' ? (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                              <Search className="h-4 w-4 mr-2" />
                            )}
                            {isProcessing && processingStage === 'website_scraping' ? 'Processing...' : 'Run V2 Web Crawler'}
                          </Button>
                          
                          <Button 
                            className="w-full justify-start" 
                            variant="outline"
                            onClick={() => handleProcessingTrigger('company_overview_v2', 'V2 Company Overview')}
                            disabled={isProcessing && processingStage === 'company_overview_v2'}
                          >
                            {isProcessing && processingStage === 'company_overview_v2' ? (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                              <Building className="h-4 w-4 mr-2" />
                            )}
                            {isProcessing && processingStage === 'company_overview_v2' ? 'Processing...' : 'Run V2 Company Overview'}
                          </Button>

                          <Button 
                            className="w-full justify-start" 
                            variant="outline"
                            onClick={() => handleProcessingTrigger('company_investment_criteria', 'Investment Criteria')}
                            disabled={isProcessing && processingStage === 'company_investment_criteria'}
                          >
                            {isProcessing && processingStage === 'company_investment_criteria' ? (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                              <Target className="h-4 w-4 mr-2" />
                            )}
                            {isProcessing && processingStage === 'company_investment_criteria' ? 'Processing...' : 'Extract Investment Criteria'}
                          </Button>

                          <Button 
                            className="w-full justify-start" 
                            variant="outline"
                            onClick={() => handleProcessingTrigger('company_overview', 'Company Overview')}
                            disabled={isProcessing && processingStage === 'company_overview'}
                          >
                            {isProcessing && processingStage === 'company_overview' ? (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            ) : (
                              <Briefcase className="h-4 w-4 mr-2" />
                            )}
                            {isProcessing && processingStage === 'company_overview' ? 'Processing...' : 'Run Company Overview'}
                          </Button>
                        </div>
                      </div>
                      
                      <div className="text-sm text-gray-600">
                        <p className="mb-2">V2 processing uses enhanced AI models and comprehensive data extraction to provide more detailed company insights.</p>
                        <div className="space-y-1 text-xs">
                          <p><strong>Web Crawler:</strong> Scrapes company website for contact information and company details</p>
                          <p><strong>Company Overview V2:</strong> Enhanced AI analysis of company structure, strategy, and investment criteria</p>
                          <p><strong>Investment Criteria:</strong> Extracts detailed investment parameters and deal preferences</p>
                          <p><strong>Company Overview:</strong> Standard company analysis and profile extraction</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>

          <div className="space-y-6">
            <Card className="bg-gradient-to-br from-blue-50 to-white border-blue-100">
              <CardHeader>
                <CardTitle>AI Research Agent</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Button className="w-full justify-start" variant="ghost">
                    <Sparkles className="h-4 w-4 mr-2" />
                    Research recent deals
                  </Button>
                  <Button className="w-full justify-start" variant="ghost">
                    <Sparkles className="h-4 w-4 mr-2" />
                    Find key contacts
                  </Button>
                  <Button className="w-full justify-start" variant="ghost">
                    <Sparkles className="h-4 w-4 mr-2" />
                    Analyze strategy
                  </Button>
                </div>
              </CardContent>
            </Card>


          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyView;