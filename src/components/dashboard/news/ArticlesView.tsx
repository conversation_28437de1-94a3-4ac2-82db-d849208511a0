"use client"

import { useState, useEffect, useCallback } from 'react'
import { 
  FileText, Search, Filter, Plus, ChevronRight, 
  ChevronLeft, ArrowUpDown, Calendar, Clock,
  CheckCircle, XCircle, AlertCircle, Newspaper,
  Tag, Users, Building, BarChart3, TrendingUp,
  RefreshCcw, Eye, Settings, Database, Zap
} from 'lucide-react'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import debounce from 'lodash/debounce'
import { useRouter } from 'next/navigation'

interface Article {
  article_id: number
  publication_name: string
  article_url: string
  headline: string
  publication_date: string
  author: string
  summary: string
  topic: string
  market_trend_tags: string[]
  is_distressed: boolean
  sentiment: string
  sentiment_summary: string
  key_takeaways: string
  llm_tags: string[]
  source_confidence: number
  fetch_status: string
  extraction_status: string
  is_bad_url: boolean
  is_relevant: boolean
  created_at: string
  updated_at: string
  properties_count: number
  metrics_count: number
  transactions_count: number
  entities_count: number
}

interface SourceStat {
  publication_name: string
  article_count: number
  today_count: number
  processed_count: number
  pending_count: number
  failed_count: number
}

interface DailyStat {
  date: string
  publication_name: string
  count: number
}

interface PaginatedResponse {
  articles: Article[]
  total: number
  page: number
  totalPages: number
  pageSize: number
  sources: SourceStat[]
  dailyStats: DailyStat[]
}

export default function ArticlesView() {
  const router = useRouter()
  const [articles, setArticles] = useState<Article[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [totalArticles, setTotalArticles] = useState(0)
  const [pageSize, setPageSize] = useState(20)
  const [sortBy, setSortBy] = useState('created_desc')
  const [selectedSource, setSelectedSource] = useState<string>("")
  const [selectedStatus, setSelectedStatus] = useState<string>("all")
  const [sourceStats, setSourceStats] = useState<SourceStat[]>([])
  const [refreshing, setRefreshing] = useState(false)

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((term: string) => {
      setDebouncedSearchTerm(term)
    }, 300),
    []
  )

  // Handle search input changes
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setSearchTerm(value)
    debouncedSearch(value)
  }

  // Fetch articles
  useEffect(() => {
    fetchArticles()
  }, [page, pageSize, debouncedSearchTerm, sortBy, selectedSource, selectedStatus])

  const fetchArticles = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString(),
        search: debouncedSearchTerm,
        sort: sortBy,
        ...(selectedSource && { source: selectedSource }),
        ...(selectedStatus && selectedStatus !== 'all' && { status: selectedStatus })
      })

      const response = await fetch(`/api/articles?${params}`)
      if (!response.ok) throw new Error('Failed to fetch articles')
      
      const data: PaginatedResponse = await response.json()
      
      setArticles(data.articles || [])
      setTotalPages(data.totalPages || 0)
      setTotalArticles(data.total || 0)
      setSourceStats(data.sources || [])
    } catch (error) {
      console.error('Error fetching articles:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchArticles()
    setRefreshing(false)
  }

  const handleArticleClick = (articleId: number) => {
    router.push(`/dashboard/articles/${articleId}`)
  }

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Unknown'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Format date time
  const formatDateTime = (dateString: string) => {
    if (!dateString) return 'Unknown'
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Get status color
  const getStatusColor = (status: string): string => {
    const colors: Record<string, string> = {
      'completed': 'bg-green-100 text-green-800',
      'processing': 'bg-blue-100 text-blue-800',
      'pending': 'bg-yellow-100 text-yellow-800',
      'failed': 'bg-red-100 text-red-800',
      'default': 'bg-gray-100 text-gray-800'
    }
    return colors[status?.toLowerCase()] || colors.default
  }

  // Get sentiment color
  const getSentimentColor = (sentiment: string): string => {
    const colors: Record<string, string> = {
      'positive': 'bg-green-100 text-green-800',
      'negative': 'bg-red-100 text-red-800',
      'neutral': 'bg-gray-100 text-gray-800',
      'default': 'bg-gray-100 text-gray-800'
    }
    return colors[sentiment?.toLowerCase()] || colors.default
  }

  // Get source color
  const getSourceColor = (source: string): string => {
    const colors: Record<string, string> = {
      'bisnow': 'bg-blue-100 text-blue-800',
      'therealdeal': 'bg-green-100 text-green-800',
      'pincus': 'bg-purple-100 text-purple-800',
      'globest': 'bg-yellow-100 text-yellow-800',
      'default': 'bg-gray-100 text-gray-800'
    }
    return colors[source?.toLowerCase()] || colors.default
  }

  return (
    <div className="bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <FileText className="h-8 w-8 mr-3 text-blue-600" />
              Articles
            </h1>
            <p className="text-gray-500 mt-1">
              Showing {articles?.length} of {totalArticles} articles
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              onClick={handleRefresh}
              disabled={refreshing}
              variant="outline"
              size="sm"
              className="border-gray-200"
            >
              <RefreshCcw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              onClick={() => router.push('/dashboard/articles/new')}
              size="sm"
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Article
            </Button>
          </div>
        </div>

        {/* Source Statistics */}
        <div className="mb-6 bg-white p-4 rounded-lg shadow-sm">
          <div className="flex items-center justify-between mb-3 border-b pb-2">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <BarChart3 className="h-5 w-5 mr-2 text-blue-500" />
              Source Statistics
            </h3>
            {sourceStats.length > 0 && (
              <div className="text-sm text-blue-600 font-medium flex items-center">
                <Clock className="h-4 w-4 mr-1" />
                {sourceStats.reduce((total, source) => total + source.today_count, 0)} new articles today
              </div>
            )}
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-3">
            {/* All Sources option */}
            <div 
              className={`flex items-center p-2 rounded-lg transition-colors cursor-pointer ${selectedSource === "" ? 'bg-blue-50 border border-blue-200' : 'border border-gray-100 hover:bg-gray-50'}`}
              onClick={() => { setSelectedSource(""); setPage(1); }}
            >
              <div className="w-8 h-8 rounded-md flex items-center justify-center mr-2 bg-blue-600 text-white">
                <FileText className="h-4 w-4" />
              </div>
              <div>
                <div className="flex items-center">
                  <span className="font-medium text-sm text-gray-900">All Sources</span>
                  {sourceStats.length > 0 && (
                    <Badge className="ml-1.5 text-xs h-5 px-1.5 bg-blue-100 text-blue-800">
                      +{sourceStats.reduce((total, source) => total + source.today_count, 0)}
                    </Badge>
                  )}
                </div>
                <span className="text-xs text-gray-500">{totalArticles} total</span>
              </div>
            </div>
            
            {/* Individual sources */}
            {sourceStats.map((source) => (
              <div 
                key={source.publication_name} 
                className={`flex items-center p-2 rounded-lg transition-colors cursor-pointer ${selectedSource === source.publication_name ? 'bg-blue-50 border border-blue-200' : 'border border-gray-100 hover:bg-gray-50'}`}
                onClick={() => { setSelectedSource(source.publication_name); setPage(1); }}
              >
                <div className="w-8 h-8 rounded-md flex items-center justify-center mr-2 bg-gray-100">
                  <Newspaper className="h-4 w-4 text-gray-600" />
                </div>
                <div>
                  <div className="flex items-center">
                    <span className="font-medium text-sm text-gray-900">{source.publication_name}</span>
                    {source.today_count > 0 && (
                      <Badge className="ml-1.5 text-xs h-5 px-1.5 bg-green-100 text-green-800">
                        +{source.today_count}
                      </Badge>
                    )}
                  </div>
                  <div className="text-xs text-gray-500 flex gap-2">
                    <span>{source.article_count} total</span>
                    <span className="text-green-600">{source.processed_count} processed</span>
                    <span className="text-yellow-600">{source.pending_count} pending</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white p-4 rounded-xl shadow-sm mb-6">
          <div className="flex flex-col md:flex-row items-stretch md:items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search articles by headline, content, or author..."
                value={searchTerm}
                onChange={handleSearchChange}
                className="pl-10 bg-gray-50 border-gray-200 focus:bg-white"
              />
            </div>
            
            <div className="flex flex-wrap gap-3">
              <Select
                value={selectedStatus}
                onValueChange={(value) => {
                  setSelectedStatus(value)
                  setPage(1)
                }}
              >
                <SelectTrigger className="w-[140px] bg-gray-50 border-gray-200">
                  <div className="flex items-center">
                    <Filter className="h-3.5 w-3.5 mr-2 text-gray-500" />
                    <span>Status</span>
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="processed">Processed</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={sortBy}
                onValueChange={(value) => {
                  setSortBy(value)
                  setPage(1)
                }}
              >
                <SelectTrigger className="w-[160px] bg-gray-50 border-gray-200">
                  <div className="flex items-center">
                    <ArrowUpDown className="h-3.5 w-3.5 mr-2 text-gray-500" />
                    <span>Sort by</span>
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_desc">Newest First</SelectItem>
                  <SelectItem value="created_asc">Oldest First</SelectItem>
                  <SelectItem value="updated_desc">Recently Updated</SelectItem>
                  <SelectItem value="publication_desc">Publication Date</SelectItem>
                </SelectContent>
              </Select>
              
              <Select
                value={pageSize.toString()}
                onValueChange={(value) => {
                  setPageSize(Number(value))
                  setPage(1)
                }}
              >
                <SelectTrigger className="w-[140px] bg-gray-50 border-gray-200">
                  <div className="flex items-center">
                    <Filter className="h-3.5 w-3.5 mr-2 text-gray-500" />
                    <span>Per page</span>
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10 per page</SelectItem>
                  <SelectItem value="20">20 per page</SelectItem>
                  <SelectItem value="50">50 per page</SelectItem>
                  <SelectItem value="100">100 per page</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Articles List */}
        {loading ? (
          <div className="flex flex-col items-center justify-center py-20 bg-white rounded-xl shadow-sm">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
            <p className="text-gray-500">Loading articles...</p>
          </div>
        ) : articles.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-20 bg-white rounded-xl shadow-sm">
            <FileText className="h-16 w-16 text-gray-300 mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-1">No articles found</h3>
            <p className="text-gray-500 mb-6">Try changing your search criteria</p>
            <Button onClick={() => router.push('/dashboard/articles/new')}>
              <Plus className="h-4 w-4 mr-2" />
              Add First Article
            </Button>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 gap-4">
              {articles.map((article) => (
                <Card 
                  key={article.article_id} 
                  className="overflow-hidden hover:shadow-md transition-all duration-200 cursor-pointer bg-white"
                  onClick={() => handleArticleClick(article.article_id)}
                >
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1 min-w-0">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                          {article.headline || 'Untitled Article'}
                        </h3>
                        
                        <div className="flex flex-wrap gap-2 mb-3">
                          {article.publication_name && (
                            <Badge className={getSourceColor(article.publication_name)}>
                              {article.publication_name}
                            </Badge>
                          )}
                          
                          <Badge className={getStatusColor(article.extraction_status)}>
                            {article.extraction_status || 'pending'}
                          </Badge>
                          
                          {article.sentiment && (
                            <Badge className={getSentimentColor(article.sentiment)}>
                              {article.sentiment}
                            </Badge>
                          )}
                          
                          {article.is_distressed && (
                            <Badge className="bg-red-100 text-red-800">
                              Distressed
                            </Badge>
                          )}
                          
                          {!article.is_relevant && (
                            <Badge className="bg-gray-100 text-gray-800">
                              Not Relevant
                            </Badge>
                          )}
                          
                          {article.is_bad_url && (
                            <Badge className="bg-orange-100 text-orange-800">
                              Bad URL
                            </Badge>
                          )}
                        </div>
                        
                        {article.summary && (
                          <p className="text-gray-600 text-sm line-clamp-2 mb-3">
                            {article.summary}
                          </p>
                        )}
                      </div>
                      
                      <div className="flex items-center ml-4">
                        <Button variant="ghost" size="icon" className="text-gray-400">
                          <ChevronRight className="h-5 w-5" />
                        </Button>
                      </div>
                    </div>
                    
                    {/* Article Stats and Metadata */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t border-gray-100">
                      <div className="flex items-center text-sm text-gray-500">
                        <Calendar className="h-4 w-4 mr-2" />
                        <div>
                          <div className="text-xs text-gray-400">Published</div>
                          <div>{formatDate(article.publication_date)}</div>
                        </div>
                      </div>
                      
                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="h-4 w-4 mr-2" />
                        <div>
                          <div className="text-xs text-gray-400">Created</div>
                          <div>{formatDate(article.created_at)}</div>
                        </div>
                      </div>
                      
                      {/* Data Extraction Stats */}
                      <div className="flex items-center gap-2">
                        {article.properties_count > 0 && (
                          <Badge variant="outline" className="text-xs">
                            <Building className="h-3 w-3 mr-1" />
                            {article.properties_count} Properties
                          </Badge>
                        )}
                        {article.transactions_count > 0 && (
                          <Badge variant="outline" className="text-xs">
                            <Database className="h-3 w-3 mr-1" />
                            {article.transactions_count} Deals
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {article.entities_count > 0 && (
                          <Badge variant="outline" className="text-xs">
                            <Users className="h-3 w-3 mr-1" />
                            {article.entities_count} Entities
                          </Badge>
                        )}
                        {article.metrics_count > 0 && (
                          <Badge variant="outline" className="text-xs">
                            <BarChart3 className="h-3 w-3 mr-1" />
                            {article.metrics_count} Metrics
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    {/* Tags */}
                    {article.llm_tags && article.llm_tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-3 pt-3 border-t border-gray-100">
                        {article.llm_tags.slice(0, 4).map((tag, index) => (
                          <Badge key={index} variant="outline" className="text-xs bg-blue-50 text-blue-700">
                            <Tag className="h-2 w-2 mr-1" />
                            {tag}
                          </Badge>
                        ))}
                        {article.llm_tags.length > 4 && (
                          <Badge variant="outline" className="text-xs text-gray-500">
                            +{article.llm_tags.length - 4} more
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>
                </Card>
              ))}
            </div>

            {/* Pagination */}
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-500">
                Page {page} of {totalPages}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setPage(p => Math.max(1, p - 1))}
                  disabled={page === 1}
                  className="border-gray-200 bg-white"
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                  disabled={page === totalPages}
                  className="border-gray-200 bg-white"
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
