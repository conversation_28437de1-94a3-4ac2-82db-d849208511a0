'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { ArrowLeft, Save, Plus, X } from 'lucide-react'
import EntityManager from './EntityManager'

interface ArticleFormProps {
  article?: any
  isEdit?: boolean
}

export default function ArticleForm({ article, isEdit = false }: ArticleFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Form state
  const [formData, setFormData] = useState({
    publication_name: article?.publication_name || '',
    article_url: article?.article_url || '',
    headline: article?.headline || '',
    publication_date: article?.publication_date || '',
    author: article?.author || '',
    summary: article?.summary || '',
    article_body_text: article?.article_body_text || '',
    topic: article?.topic || '',
    sentiment: article?.sentiment || '',
    sentiment_summary: article?.sentiment_summary || '',
    key_takeaways: article?.key_takeaways || '',
    source_confidence: article?.source_confidence || 0,
    is_distressed: article?.is_distressed || false,
    is_relevant: article?.is_relevant !== undefined ? article.is_relevant : true
  })
  
  const [tags, setTags] = useState<string[]>(article?.llm_tags || [])
  const [marketTags, setMarketTags] = useState<string[]>(article?.market_trend_tags || [])
  const [newTag, setNewTag] = useState('')
  const [newMarketTag, setNewMarketTag] = useState('')

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleSelectChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const addTag = (tagType: 'llm' | 'market') => {
    const tag = tagType === 'llm' ? newTag.trim() : newMarketTag.trim()
    if (!tag) return

    if (tagType === 'llm') {
      if (!tags.includes(tag)) {
        setTags([...tags, tag])
      }
      setNewTag('')
    } else {
      if (!marketTags.includes(tag)) {
        setMarketTags([...marketTags, tag])
      }
      setNewMarketTag('')
    }
  }

  const removeTag = (tagType: 'llm' | 'market', tagToRemove: string) => {
    if (tagType === 'llm') {
      setTags(tags.filter(tag => tag !== tagToRemove))
    } else {
      setMarketTags(marketTags.filter(tag => tag !== tagToRemove))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      const payload = {
        ...formData,
        llm_tags: tags,
        market_trend_tags: marketTags,
        source_confidence: parseFloat(formData.source_confidence.toString()) || 0
      }

      const url = isEdit ? `/api/articles/${article.article_id}` : '/api/articles'
      const method = isEdit ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      })

      const result = await response.json()

      if (response.ok) {
        router.push('/dashboard/articles')
      } else {
        setError(result.error || 'Failed to save article')
      }
    } catch (err) {
      console.error('Error saving article:', err)
      setError('Failed to save article')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button 
            variant="ghost" 
            onClick={() => router.back()}
            className="p-2"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {isEdit ? 'Edit Article' : 'Create New Article'}
            </h1>
            <p className="text-gray-500">
              {isEdit ? 'Update article information and content' : 'Add a new article to the system'}
            </p>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="headline">Headline *</Label>
                <Input
                  id="headline"
                  name="headline"
                  value={formData.headline}
                  onChange={handleInputChange}
                  required
                  placeholder="Article headline"
                />
              </div>
              
              <div>
                <Label htmlFor="publication_name">Publication</Label>
                <Select 
                  value={formData.publication_name} 
                  onValueChange={(value) => handleSelectChange('publication_name', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select publication" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="bisnow">Bisnow</SelectItem>
                    <SelectItem value="therealdeal">The Real Deal</SelectItem>
                    <SelectItem value="pincus">Pincus</SelectItem>
                    <SelectItem value="globest">GlobeSt</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="article_url">URL *</Label>
                <Input
                  id="article_url"
                  name="article_url"
                  type="url"
                  value={formData.article_url}
                  onChange={handleInputChange}
                  required
                  placeholder="https://example.com/article"
                />
              </div>

              <div>
                <Label htmlFor="publication_date">Publication Date</Label>
                <Input
                  id="publication_date"
                  name="publication_date"
                  type="date"
                  value={formData.publication_date}
                  onChange={handleInputChange}
                />
              </div>

              <div>
                <Label htmlFor="author">Author</Label>
                <Input
                  id="author"
                  name="author"
                  value={formData.author}
                  onChange={handleInputChange}
                  placeholder="Article author"
                />
              </div>

              <div>
                <Label htmlFor="topic">Topic</Label>
                <Select 
                  value={formData.topic} 
                  onValueChange={(value) => handleSelectChange('topic', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select topic" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Market Report">Market Report</SelectItem>
                    <SelectItem value="Company News">Company News</SelectItem>
                    <SelectItem value="Individual Deal">Individual Deal</SelectItem>
                    <SelectItem value="Industry News">Industry News</SelectItem>
                    <SelectItem value="Economic Update">Economic Update</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </Card>

          {/* Content */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Content</h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="summary">Summary</Label>
                <Textarea
                  id="summary"
                  name="summary"
                  value={formData.summary}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Brief summary of the article"
                />
              </div>

              <div>
                <Label htmlFor="article_body_text">Article Content</Label>
                <Textarea
                  id="article_body_text"
                  name="article_body_text"
                  value={formData.article_body_text}
                  onChange={handleInputChange}
                  rows={10}
                  placeholder="Full article content"
                />
              </div>
            </div>
          </Card>

          {/* Analysis & Tags */}
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Analysis & Classification</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="sentiment">Sentiment</Label>
                <Select 
                  value={formData.sentiment} 
                  onValueChange={(value) => handleSelectChange('sentiment', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select sentiment" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Positive">Positive</SelectItem>
                    <SelectItem value="Neutral">Neutral</SelectItem>
                    <SelectItem value="Negative">Negative</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="source_confidence">Confidence Score (0-1)</Label>
                <Input
                  id="source_confidence"
                  name="source_confidence"
                  type="number"
                  step="0.01"
                  min="0"
                  max="1"
                  value={formData.source_confidence}
                  onChange={handleInputChange}
                  placeholder="0.85"
                />
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="sentiment_summary">Sentiment Summary</Label>
                <Textarea
                  id="sentiment_summary"
                  name="sentiment_summary"
                  value={formData.sentiment_summary}
                  onChange={handleInputChange}
                  rows={2}
                  placeholder="Summary of market sentiment expressed in the article"
                />
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="key_takeaways">Key Takeaways</Label>
                <Textarea
                  id="key_takeaways"
                  name="key_takeaways"
                  value={formData.key_takeaways}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Main points and insights from the article"
                />
              </div>
            </div>

            {/* Tags Section */}
            <div className="mt-6 space-y-4">
              {/* AI Tags */}
              <div>
                <Label>AI Tags</Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="Add AI tag"
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag('llm'))}
                  />
                  <Button 
                    type="button" 
                    onClick={() => addTag('llm')}
                    size="sm"
                    variant="outline"
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeTag('llm', tag)}
                        className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                      >
                        <X className="h-2 w-2" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Market Trend Tags */}
              <div>
                <Label>Market Trend Tags</Label>
                <div className="flex items-center gap-2 mt-1">
                  <Input
                    value={newMarketTag}
                    onChange={(e) => setNewMarketTag(e.target.value)}
                    placeholder="Add market trend tag"
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag('market'))}
                  />
                  <Button 
                    type="button" 
                    onClick={() => addTag('market')}
                    size="sm"
                    variant="outline"
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {marketTags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="flex items-center gap-1">
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeTag('market', tag)}
                        className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                      >
                        <X className="h-2 w-2" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* Flags */}
            <div className="mt-6 space-y-3">
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="is_distressed"
                  name="is_distressed"
                  checked={formData.is_distressed}
                  onChange={handleInputChange}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="is_distressed">Article relates to distressed debt or workout situation</Label>
              </div>
              
              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="is_relevant"
                  name="is_relevant"
                  checked={formData.is_relevant}
                  onChange={handleInputChange}
                  className="rounded border-gray-300"
                />
                <Label htmlFor="is_relevant">Article is relevant to our analysis</Label>
              </div>
            </div>
          </Card>

          {/* Submit Buttons */}
          <div className="flex items-center gap-4">
            <Button
              type="submit"
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              {isEdit ? 'Update Article' : 'Create Article'}
            </Button>
            
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
