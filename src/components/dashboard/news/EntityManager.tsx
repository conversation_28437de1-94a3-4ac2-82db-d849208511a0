'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Plus, Edit, Trash2, Link, Unlink } from 'lucide-react'

interface Entity {
  article_entity_id: number
  entity_type: string
  entity_name: string
  entity_role: string
  matched_id?: number | null
  matched_type?: string | null
  match_confidence?: number | null
  created_at: string
  updated_at: string
}

interface EntityManagerProps {
  articleId: number
}

export default function EntityManager({ articleId }: EntityManagerProps) {
  const [entities, setEntities] = useState<Entity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [editingEntity, setEditingEntity] = useState<Entity | null>(null)
  const [newEntity, setNewEntity] = useState({
    entityType: '',
    entityName: '',
    entityRole: ''
  })

  // Fetch entities on component mount
  useEffect(() => {
    fetchEntities()
  }, [articleId])

  const fetchEntities = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/articles/${articleId}/entities`)
      const data = await response.json()
      
      if (data.success) {
        setEntities(data.entities)
      } else {
        setError(data.error || 'Failed to fetch entities')
      }
    } catch (err) {
      console.error('Error fetching entities:', err)
      setError('Failed to fetch entities')
    } finally {
      setLoading(false)
    }
  }

  const addEntity = async () => {
    if (!newEntity.entityType || !newEntity.entityName || !newEntity.entityRole) {
      setError('All fields are required')
      return
    }

    try {
      const response = await fetch(`/api/articles/${articleId}/entities`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newEntity)
      })

      const data = await response.json()
      
      if (data.success) {
        setNewEntity({ entityType: '', entityName: '', entityRole: '' })
        fetchEntities()
      } else {
        setError(data.error || 'Failed to add entity')
      }
    } catch (err) {
      console.error('Error adding entity:', err)
      setError('Failed to add entity')
    }
  }

  const updateEntity = async (entity: Entity) => {
    try {
      const response = await fetch(`/api/articles/${articleId}/entities`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          entityId: entity.article_entity_id,
          matchedId: entity.matched_id,
          matchedType: entity.matched_type,
          matchConfidence: entity.match_confidence
        })
      })

      const data = await response.json()
      
      if (data.success) {
        setEditingEntity(null)
        fetchEntities()
      } else {
        setError(data.error || 'Failed to update entity')
      }
    } catch (err) {
      console.error('Error updating entity:', err)
      setError('Failed to update entity')
    }
  }

  const deleteEntity = async (entityId: number) => {
    if (!confirm('Are you sure you want to delete this entity?')) {
      return
    }

    try {
      const response = await fetch(`/api/articles/${articleId}/entities?entityId=${entityId}`, {
        method: 'DELETE'
      })

      const data = await response.json()
      
      if (data.success) {
        fetchEntities()
      } else {
        setError(data.error || 'Failed to delete entity')
      }
    } catch (err) {
      console.error('Error deleting entity:', err)
      setError('Failed to delete entity')
    }
  }

  const searchMatches = async (entityName: string, entityType: string) => {
    // This would typically call an API to search for matches
    // For now, we'll just show a placeholder
    console.log(`Searching for ${entityType} matches for: ${entityName}`)
  }

  if (loading) {
    return (
      <Card className="p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </Card>
    )
  }

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Entities</h3>
        <Badge variant="secondary">{entities.length} entities</Badge>
      </div>

      {error && (
        <div className="mb-4 bg-red-50 border border-red-200 rounded-lg p-3">
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {/* Add New Entity */}
      <div className="mb-6 p-4 border border-gray-200 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 mb-3">Add New Entity</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          <div>
            <Label htmlFor="entityType">Type</Label>
            <Select 
              value={newEntity.entityType} 
              onValueChange={(value) => setNewEntity(prev => ({ ...prev, entityType: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="company">Company</SelectItem>
                <SelectItem value="contact">Contact</SelectItem>
                <SelectItem value="deal">Deal</SelectItem>
                <SelectItem value="property">Property</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="entityName">Name</Label>
            <Input
              id="entityName"
              value={newEntity.entityName}
              onChange={(e) => setNewEntity(prev => ({ ...prev, entityName: e.target.value }))}
              placeholder="Entity name"
            />
          </div>
          
          <div>
            <Label htmlFor="entityRole">Role</Label>
            <Input
              id="entityRole"
              value={newEntity.entityRole}
              onChange={(e) => setNewEntity(prev => ({ ...prev, entityRole: e.target.value }))}
              placeholder="Role in article"
            />
          </div>
        </div>
        
        <Button 
          onClick={addEntity}
          className="mt-3"
          size="sm"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Entity
        </Button>
      </div>

      {/* Entities List */}
      <div className="space-y-3">
        {entities.map((entity) => (
          <div key={entity.article_entity_id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <Badge variant="outline">{entity.entity_type}</Badge>
                  <span className="font-medium text-gray-900">{entity.entity_name}</span>
                  <span className="text-gray-500">•</span>
                  <span className="text-gray-600">{entity.entity_role}</span>
                </div>
                
                {entity.matched_id && (
                  <div className="flex items-center gap-2 text-sm text-green-600">
                    <Link className="h-3 w-3" />
                    <span>Matched to {entity.matched_type} ID: {entity.matched_id}</span>
                    {entity.match_confidence && (
                      <Badge variant="secondary" className="text-xs">
                        {Math.round(entity.match_confidence * 100)}% match
                      </Badge>
                    )}
                  </div>
                )}
                
                {!entity.matched_id && (
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <Unlink className="h-3 w-3" />
                    <span>No match found</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => searchMatches(entity.entity_name, entity.entity_type)}
                      className="h-6 px-2 text-xs"
                    >
                      Search Matches
                    </Button>
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setEditingEntity(entity)}
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => deleteEntity(entity.article_entity_id)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        ))}
        
        {entities.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <p>No entities found for this article.</p>
            <p className="text-sm">Add entities above to get started.</p>
          </div>
        )}
      </div>
    </Card>
  )
}
