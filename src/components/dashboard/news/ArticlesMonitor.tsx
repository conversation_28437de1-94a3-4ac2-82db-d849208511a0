"use client";

import React, { useEffect, useState } from "react";
import {
  Newspaper,
  Check,
  AlertCircle,
  Clock,
  RefreshCcw,
  Calendar as CalendarIcon,
  Globe,
  FileText,
  Cpu,
  Database,
  ExternalLink,
  Target,
  Building2,
  TrendingUp,
  DollarSign,
  Users,
  Zap,
} from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import ArticleDetail from "./ArticleDetail";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { useInView } from "react-intersection-observer";
import ScrapingManager from "./ScrapingManager";

interface Article {
  article_id: number;
  publication_name: string;
  article_url: string;
  headline: string | null;
  publication_date: string | null;
  author: string | null;
  summary: string | null;
  article_body_text: string | null;
  topic: string | null;
  market_trend_tags: string[];
  is_distressed: boolean;
  sentiment: string | null;
  sentiment_summary: string | null;
  key_takeaways: string | null;
  llm_tags: string[];
  source_confidence: number | null;
  fetch_status: string;
  extraction_status: string;
  is_bad_url: boolean;
  is_relevant: boolean;
  created_at: string;
  updated_at: string;
  properties_count: string;
  metrics_count: string;
  transactions_count: string;
  entities_count: string;
}

interface ArticleState {
  articles: Article[];
  isLoading: boolean;
  selectedArticle: Article | null;
  page: number;
  hasMore: boolean;
  totalCount: number;
  error?: string;
}

interface SourceStats {
  publication_name: string;
  article_count: string;
  today_count: string;
  processed_count: string;
  pending_count: string;
  failed_count: string;
}

interface ArticlesMonitorProps {
  isActive: boolean;
}

const ArticlesMonitor: React.FC<ArticlesMonitorProps> = ({ isActive }) => {
  const [activeTab, setActiveTab] = useState("overview");
  
  // Separate state for each source like NewsMonitor
  const [allArticlesState, setAllArticlesState] = useState<ArticleState>({
    articles: [],
    isLoading: false,
    selectedArticle: null,
    page: 1,
    hasMore: true,
    totalCount: 0,
  });

  const [bisnowState, setBisnowState] = useState<ArticleState>({
    articles: [],
    isLoading: false,
    selectedArticle: null,
    page: 1,
    hasMore: true,
    totalCount: 0,
  });

  const [trdState, setTrdState] = useState<ArticleState>({
    articles: [],
    isLoading: false,
    selectedArticle: null,
    page: 1,
    hasMore: true,
    totalCount: 0,
  });

  const [pincusState, setPincusState] = useState<ArticleState>({
    articles: [],
    isLoading: false,
    selectedArticle: null,
    page: 1,
    hasMore: true,
    totalCount: 0,
  });

  const [globestState, setGlobestState] = useState<ArticleState>({
    articles: [],
    isLoading: false,
    selectedArticle: null,
    page: 1,
    hasMore: true,
    totalCount: 0,
  });

  const [selectedArticle, setSelectedArticle] = useState<Article | null>(null);
  
  const [filters, setFilters] = useState({
    search: "",
    status: "all",
    source: "all",
    sortBy: "created_at",
    sortOrder: "desc" as "asc" | "desc",
  });

  const [stats, setStats] = useState({
    total: 0,
    sources: [] as SourceStats[],
  });

  const [isLoading, setIsLoading] = useState(false);
  const [allLoadMoreRef, allInView] = useInView();
  const [bisnowLoadMoreRef, bisnowInView] = useInView();
  const [trdLoadMoreRef, trdInView] = useInView();
  const [pincusLoadMoreRef, pincusInView] = useInView();
  const [globestLoadMoreRef, globestInView] = useInView();

  const fetchArticles = async (
    source: string,
    page: number = 1,
    reset: boolean = false,
    setState: React.Dispatch<React.SetStateAction<ArticleState>>
  ) => {
    if (reset) {
      setState(prev => ({ ...prev, articles: [], page: 1 }));
    }

    setState(prev => ({ ...prev, isLoading: true }));

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: "20",
        ...(filters.search && { search: filters.search }),
        ...(filters.status !== "all" && { status: filters.status }),
        ...(source !== "all" && { source }),
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
      });

      const response = await fetch(`/api/articles?${params}`);
      if (!response.ok) throw new Error("Failed to fetch articles");

      const data = await response.json();

      setState(prev => ({
        ...prev,
        articles: reset ? data.articles : [...prev.articles, ...data.articles],
        totalCount: data.total,
        hasMore: data.page < data.totalPages,
        page: data.page,
        isLoading: false,
      }));
    } catch (error) {
      console.error("Error fetching articles:", error);
      setState(prev => ({ ...prev, isLoading: false, error: "Failed to fetch articles" }));
    }
  };

  const fetchStats = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/articles?page=1&pageSize=1");
      if (!response.ok) throw new Error("Failed to fetch stats");

      const data = await response.json();
      setStats({
        total: data.total,
        sources: data.sources,
      });
    } catch (error) {
      console.error("Error fetching stats:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isActive) {
      fetchStats();
      // Load articles for the "all" tab by default when component becomes active
      fetchArticles("all", 1, true, setAllArticlesState);
    }
  }, [isActive]);

  useEffect(() => {
    if (allInView && !allArticlesState.isLoading && allArticlesState.hasMore) {
      fetchArticles("all", allArticlesState.page + 1, false, setAllArticlesState);
    }
  }, [allInView, allArticlesState.isLoading, allArticlesState.hasMore]);

  // Handle tab changes
  useEffect(() => {
    if (isActive) {
      if (activeTab === "all") {
        fetchArticles("all", 1, true, setAllArticlesState);
      } else if (activeTab === "bisnow") {
        fetchArticles("bisnow", 1, true, setBisnowState);
      } else if (activeTab === "trd") {
        fetchArticles("therealdeal", 1, true, setTrdState);
      } else if (activeTab === "pincus") {
        fetchArticles("pincus", 1, true, setPincusState);
      } else if (activeTab === "globest") {
        fetchArticles("globest", 1, true, setGlobestState);
      }
    }
  }, [activeTab, isActive]);

  useEffect(() => {
    if (bisnowInView && !bisnowState.isLoading && bisnowState.hasMore) {
      fetchArticles("bisnow", bisnowState.page + 1, false, setBisnowState);
    }
  }, [bisnowInView, bisnowState.isLoading, bisnowState.hasMore]);

  useEffect(() => {
    if (trdInView && !trdState.isLoading && trdState.hasMore) {
      fetchArticles("therealdeal", trdState.page + 1, false, setTrdState);
    }
  }, [trdInView, trdState.isLoading, trdState.hasMore]);

  useEffect(() => {
    if (pincusInView && !pincusState.isLoading && pincusState.hasMore) {
      fetchArticles("pincus", pincusState.page + 1, false, setPincusState);
    }
  }, [pincusInView, pincusState.isLoading, pincusState.hasMore]);

  useEffect(() => {
    if (globestInView && !globestState.isLoading && globestState.hasMore) {
      fetchArticles("globest", globestState.page + 1, false, setGlobestState);
    }
  }, [globestInView, globestState.isLoading, globestState.hasMore]);

  // Handle filter changes
  useEffect(() => {
    if (isActive && activeTab !== "overview") {
      if (activeTab === "all") {
        fetchArticles("all", 1, true, setAllArticlesState);
      } else if (activeTab === "bisnow") {
        fetchArticles("bisnow", 1, true, setBisnowState);
      } else if (activeTab === "trd") {
        fetchArticles("therealdeal", 1, true, setTrdState);
      } else if (activeTab === "pincus") {
        fetchArticles("pincus", 1, true, setPincusState);
      } else if (activeTab === "globest") {
        fetchArticles("globest", 1, true, setGlobestState);
      }
    }
  }, [filters, activeTab, isActive]);

  const handleFilterChange = (newFilters: Partial<typeof filters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const triggerProcessing = async (articleId: number, action: string) => {
    try {
      // Use the existing trigger API with the correct format
      const response = await fetch('/api/processing/trigger', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          action: 'execute_manual',
          stage: action === 'fetch_content' ? 'article_html_fetch' : 'article_enrichment',
          options: {
            singleId: articleId,
            limit: 1
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Processing failed');
      }

      const result = await response.json();
      console.log(`Processing ${action} result:`, result);
      
      // Refresh the current tab data
      if (activeTab === "all") {
        fetchArticles("all", 1, true, setAllArticlesState);
      } else if (activeTab === "bisnow") {
        fetchArticles("bisnow", 1, true, setBisnowState);
      } else if (activeTab === "trd") {
        fetchArticles("therealdeal", 1, true, setTrdState);
      } else if (activeTab === "pincus") {
        fetchArticles("pincus", 1, true, setPincusState);
      } else if (activeTab === "globest") {
        fetchArticles("globest", 1, true, setGlobestState);
      }
      
    } catch (error) {
      console.error(`Error processing ${action}:`, error);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: "bg-yellow-100 text-yellow-800", icon: Clock },
      processing: { color: "bg-blue-100 text-blue-800", icon: Cpu },
      completed: { color: "bg-green-100 text-green-800", icon: Check },
      failed: { color: "bg-red-100 text-red-800", icon: AlertCircle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    const Icon = config.icon;

    return (
      <Badge className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {status}
      </Badge>
    );
  };

  const renderArticleCard = (article: Article, state: ArticleState, setState: React.Dispatch<React.SetStateAction<ArticleState>>) => (
    <div
      key={article.article_id}
      className={`p-4 rounded-lg border cursor-pointer transition-all ${
        state.selectedArticle?.article_id === article.article_id
          ? "bg-blue-50 border-blue-300 shadow-sm"
          : "bg-white border-gray-200 hover:bg-gray-50"
      }`}
      onClick={() => {
        setState(prev => ({ ...prev, selectedArticle: article }));
        setSelectedArticle(article);
      }}
    >
      <div className="flex justify-between items-start">
        <div className="flex-1">
          <h3 className="font-medium text-gray-900 line-clamp-2">
            {article.headline || article.article_url}
          </h3>
          
          <div className="flex items-center gap-2 mt-2">
            {getStatusBadge(article.fetch_status)}
            {getStatusBadge(article.extraction_status)}
            {article.is_relevant && (
              <Badge className="bg-green-100 text-green-800">
                <Target className="w-3 h-3 mr-1" />
                Relevant
              </Badge>
            )}
            {article.is_bad_url && (
              <Badge className="bg-red-100 text-red-800">
                <AlertCircle className="w-3 h-3 mr-1" />
                Bad URL
              </Badge>
            )}
          </div>

          <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
            <span className="flex items-center">
              <Globe className="w-3 h-3 mr-1" />
              {article.publication_name}
            </span>
            {article.publication_date && (
              <span className="flex items-center">
                <CalendarIcon className="w-3 h-3 mr-1" />
                {format(new Date(article.publication_date), "MMM d, yyyy")}
              </span>
            )}
            <span className="flex items-center">
              <Clock className="w-3 h-3 mr-1" />
              {format(new Date(article.created_at), "MMM d, yyyy HH:mm")}
            </span>
          </div>

          <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
            <span className="flex items-center">
              <Building2 className="w-3 h-3 mr-1" />
              {article.properties_count} properties
            </span>
            <span className="flex items-center">
              <TrendingUp className="w-3 h-3 mr-1" />
              {article.metrics_count} metrics
            </span>
            <span className="flex items-center">
              <DollarSign className="w-3 h-3 mr-1" />
              {article.transactions_count} transactions
            </span>
            <span className="flex items-center">
              <Users className="w-3 h-3 mr-1" />
              {article.entities_count} entities
            </span>
          </div>

          {/* Processing buttons */}
          <div className="flex items-center gap-2 mt-3">
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                triggerProcessing(article.article_id, 'fetch_content');
              }}
              disabled={article.fetch_status === 'completed'}
            >
              <Zap className="w-3 h-3 mr-1" />
              {article.fetch_status === 'completed' ? 'Fetched' : 'Fetch Content'}
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                triggerProcessing(article.article_id, 'extract_data');
              }}
              disabled={article.extraction_status === 'completed'}
            >
              <Database className="w-3 h-3 mr-1" />
              {article.extraction_status === 'completed' ? 'Extracted' : 'Extract Data'}
            </Button>
          </div>
        </div>

        <div className="flex items-center gap-2 ml-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              window.open(article.article_url, "_blank");
            }}
            title="Open article"
          >
            <ExternalLink className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );

  const renderArticlesContent = (
    state: ArticleState,
    setState: React.Dispatch<React.SetStateAction<ArticleState>>,
    loadMoreRef: (node?: Element | null) => void,
    sourceName: string
  ) => (
    <div className="space-y-4">
      {/* Enhanced Filters */}
      <div className="bg-gray-50 p-4 rounded-lg border">
        <div className="space-y-4">
          {/* First row - Search and Status */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">Search</label>
              <Input
                placeholder="Search headlines, content, or author..."
                value={filters.search}
                onChange={(e) => handleFilterChange({ search: e.target.value })}
                className="h-9"
              />
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">Processing Status</label>
              <Select value={filters.status} onValueChange={(value) => handleFilterChange({ status: value })}>
                <SelectTrigger className="h-9">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="processing">Processing</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">Article Quality</label>
              <Select value={filters.source || "all"} onValueChange={(value) => handleFilterChange({ source: value === "all" ? "" : value })}>
                <SelectTrigger className="h-9">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Articles</SelectItem>
                  <SelectItem value="relevant">Relevant Only</SelectItem>
                  <SelectItem value="good_urls">Good URLs Only</SelectItem>
                  <SelectItem value="with_content">Has Content</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          {/* Second row - Sort options */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">Sort By</label>
              <Select value={filters.sortBy} onValueChange={(value) => handleFilterChange({ sortBy: value })}>
                <SelectTrigger className="h-9">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="created_at">Created Date</SelectItem>
                  <SelectItem value="updated_at">Updated Date</SelectItem>
                  <SelectItem value="publication_date">Publication Date</SelectItem>
                  <SelectItem value="headline">Headline</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">Order</label>
              <Select value={filters.sortOrder} onValueChange={(value) => handleFilterChange({ sortOrder: value as "asc" | "desc" })}>
                <SelectTrigger className="h-9">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="desc">Newest First</SelectItem>
                  <SelectItem value="asc">Oldest First</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button
                onClick={() => handleFilterChange({ search: "", status: "all", source: "", sortBy: "created_at", sortOrder: "desc" })}
                variant="outline"
                size="sm"
                className="h-9"
              >
                Clear Filters
              </Button>
            </div>

            <div className="flex items-end justify-end">
              <div className="text-xs text-gray-500">
                {state.articles.length} of {state.totalCount} articles
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex gap-4 h-[calc(100vh-300px)]">
        {/* Articles List */}
        <div className="w-1/2 overflow-hidden flex flex-col">
          <div className="flex justify-between items-center sticky top-0 bg-white py-2 border-b z-10">
            <h3 className="text-sm font-medium text-gray-700">
              {sourceName} Articles ({state.articles.length} of {state.totalCount})
            </h3>
            <div className="text-xs text-gray-500">
              {state.isLoading && (
                <div className="flex items-center gap-1">
                  <RefreshCcw className="h-3 w-3 animate-spin" />
                  Loading...
                </div>
              )}
            </div>
          </div>

          <div className="overflow-y-auto flex-1 space-y-3 pr-2 pt-2">
            {state.articles.map(article => renderArticleCard(article, state, setState))}
            
            {state.hasMore && (
              <div ref={loadMoreRef} className="py-4 text-center">
                {state.isLoading && (
                  <div className="flex items-center justify-center gap-2">
                    <RefreshCcw className="h-4 w-4 animate-spin" />
                    Loading more articles...
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Article Detail */}
        <div className="w-1/2 overflow-hidden">
          {selectedArticle ? (
            <ArticleDetail 
              id={selectedArticle.article_id.toString()} 
              onBack={() => setSelectedArticle(null)}
            />
          ) : (
            <div className="h-full flex items-center justify-center text-gray-500 bg-gray-50 rounded-lg border border-gray-200">
              <div className="text-center p-6">
                <Newspaper className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                <p>Select an article to view details</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  if (!isActive) return null;

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      <div className="p-6">
        <Tabs
          defaultValue="overview"
          className="w-full"
          value={activeTab}
          onValueChange={setActiveTab}
        >
          <TabsList className="mb-6 flex flex-wrap gap-2">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="all">All Articles</TabsTrigger>
            <TabsTrigger value="bisnow">Bisnow</TabsTrigger>
            <TabsTrigger value="trd">The Real Deal</TabsTrigger>
            <TabsTrigger value="pincus">Pincus</TabsTrigger>
            <TabsTrigger value="globest">Globest</TabsTrigger>
            <TabsTrigger value="scraping">Scraping</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <RefreshCcw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
                  <p className="text-gray-600">Loading overview data...</p>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {/* Metrics Cards */}
                <div className="grid grid-cols-4 gap-4">
                  <div className="bg-blue-50 rounded-xl p-4 border border-blue-100">
                    <div className="flex items-center gap-2 mb-2 text-blue-600">
                      <FileText className="h-5 w-5" />
                      <h3 className="text-sm font-medium">Total Articles</h3>
                    </div>
                    <p className="text-2xl font-bold text-blue-700">
                      {stats.total.toLocaleString()}
                    </p>
                  </div>

                  <div className="bg-green-50 rounded-xl p-4 border border-green-100">
                    <div className="flex items-center gap-2 mb-2 text-green-600">
                      <Check className="h-5 w-5" />
                      <h3 className="text-sm font-medium">Processed</h3>
                    </div>
                    <p className="text-2xl font-bold text-green-700">
                      {stats.sources.reduce((sum, s) => sum + parseInt(s.processed_count), 0).toLocaleString()}
                    </p>
                  </div>

                  <div className="bg-yellow-50 rounded-xl p-4 border border-yellow-100">
                    <div className="flex items-center gap-2 mb-2 text-yellow-600">
                      <Clock className="h-5 w-5" />
                      <h3 className="text-sm font-medium">Pending</h3>
                    </div>
                    <p className="text-2xl font-bold text-yellow-700">
                      {stats.sources.reduce((sum, s) => sum + parseInt(s.pending_count), 0).toLocaleString()}
                    </p>
                  </div>

                  <div className="bg-red-50 rounded-xl p-4 border border-red-100">
                    <div className="flex items-center gap-2 mb-2 text-red-600">
                      <AlertCircle className="h-5 w-5" />
                      <h3 className="text-sm font-medium">Failed</h3>
                    </div>
                    <p className="text-2xl font-bold text-red-700">
                      {stats.sources.reduce((sum, s) => sum + parseInt(s.failed_count), 0).toLocaleString()}
                    </p>
                  </div>
                </div>

                {/* Source Statistics */}
                <div className="bg-white rounded-lg border p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Source Statistics</h3>
                  <div className="space-y-3">
                    {stats.sources.map((source, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                            <Newspaper className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">{source.publication_name}</h4>
                            <p className="text-sm text-gray-500">
                              {parseInt(source.article_count).toLocaleString()} total articles
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm">
                          <div className="text-center">
                            <div className="text-green-600 font-semibold">{source.processed_count}</div>
                            <div className="text-xs text-gray-500">Processed</div>
                          </div>
                          <div className="text-center">
                            <div className="text-yellow-600 font-semibold">{source.pending_count}</div>
                            <div className="text-xs text-gray-500">Pending</div>
                          </div>
                          <div className="text-center">
                            <div className="text-red-600 font-semibold">{source.failed_count}</div>
                            <div className="text-xs text-gray-500">Failed</div>
                          </div>
                          <div className="text-center">
                            <div className="text-blue-600 font-semibold">+{source.today_count}</div>
                            <div className="text-xs text-gray-500">Today</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Processing Actions */}
                <div className="grid grid-cols-2 gap-4">
                  <Button
                    onClick={() => setActiveTab("scraping")}
                    className="h-16 bg-purple-600 hover:bg-purple-700 text-white flex flex-col items-center justify-center gap-2"
                  >
                    <Globe className="h-6 w-6" />
                    <span>Manage Scraping</span>
                  </Button>
                  
                  <Button
                    onClick={fetchStats}
                    disabled={isLoading}
                    variant="outline"
                    className="h-16 flex flex-col items-center justify-center gap-2"
                  >
                    <RefreshCcw className={`h-6 w-6 ${isLoading ? 'animate-spin' : ''}`} />
                    <span>Refresh Data</span>
                  </Button>
                </div>

                <button
                  className="absolute top-2 right-2 inline-flex items-center gap-1 text-sm text-gray-500 hover:text-gray-700 px-2 py-1 rounded-md hover:bg-gray-100"
                  onClick={fetchStats}
                  title="Refresh data"
                  disabled={isLoading}
                >
                  <RefreshCcw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                  Refresh
                </button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="all">
            {renderArticlesContent(allArticlesState, setAllArticlesState, allLoadMoreRef, "All")}
          </TabsContent>

          <TabsContent value="bisnow">
            {renderArticlesContent(bisnowState, setBisnowState, bisnowLoadMoreRef, "Bisnow")}
          </TabsContent>

          <TabsContent value="trd">
            {renderArticlesContent(trdState, setTrdState, trdLoadMoreRef, "The Real Deal")}
          </TabsContent>

          <TabsContent value="pincus">
            {renderArticlesContent(pincusState, setPincusState, pincusLoadMoreRef, "Pincus")}
          </TabsContent>

          <TabsContent value="globest">
            {renderArticlesContent(globestState, setGlobestState, globestLoadMoreRef, "Globest")}
          </TabsContent>

          <TabsContent value="scraping">
            <ScrapingManager onRefresh={fetchStats} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default ArticlesMonitor;
