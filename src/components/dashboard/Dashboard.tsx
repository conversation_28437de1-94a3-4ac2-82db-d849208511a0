"use client"

import React, { useState, useEffect } from 'react'
import { Navigation } from './Navigation'
import { PasswordCheck } from '../auth/PasswordCheck'
import { usePathname, useRouter } from 'next/navigation'

 // Map route paths to section names
const ROUTE_TO_SECTION = {
  '/dashboard': 'dashboard',
  '/dashboard/people': 'people',
  '/dashboard/companies': 'companies',
  '/dashboard/tech': 'tech',
  '/dashboard/articles': 'articles',
  '/dashboard/news': 'articles', // Map old news route to articles for backward compatibility
  '/dashboard/engagement': 'engagement',
  '/dashboard/sequences': 'sequences',
  '/dashboard/data-quality': 'data-quality',
  // '/dashboard/db': 'db',
  '/dashboard/parker': 'parker',
  '/dashboard/deals': 'deals',
  '/dashboard/extracteddeals': 'extracteddeals',
  '/dashboard/dealnews': 'articles', // Map dealnews to articles section
  '/dashboard/campaigns': 'campaigns',
  '/dashboard/smartlead': 'smartlead',
  '/dashboard/processing': 'processing',
  '/dashboard/projections': 'projections',
  '/dashboard/upload': 'upload',
  '/dashboard/mapping': 'mapping',
  '/dashboard/configuration': 'configuration'
}

export default function Dashboard({ children }: { children?: React.ReactNode }) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const pathname = usePathname() || ''
  const router = useRouter()
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [accessLevel, setAccessLevel] = useState<'admin' | 'restricted' | null>(null)
  
  // Extract the current section from the pathname
  const getActiveSectionFromPath = (path: string): string => {
    // Check if it's a company detail page
    if (path.match(/\/dashboard\/companies\/\d+$/)) {
      return 'companies';
    }
    
    // Check if it's the add company page
    if (path === '/dashboard/companies/add') {
      return 'companies';
    }
    
    // Check if it's a people subsection
    if (path.startsWith('/dashboard/people/')) {
      return 'people';
    }
    
    // Check other sections
    for (const [route, section] of Object.entries(ROUTE_TO_SECTION)) {
      if (path.startsWith(route)) {
        return section;
      }
    }
    
    // Default to people if no match
    return 'people';
  }
  
  const activeSection = getActiveSectionFromPath(pathname)

  // Function to navigate to a section
  const setActiveSection = (section: string) => {
    // Find the corresponding route for this section
    const route = Object.entries(ROUTE_TO_SECTION).find(([_, s]) => s === section)?.[0] || '/dashboard'
    router.push(route)
  }

  // Use useEffect for localStorage which only runs on the client
  useEffect(() => {
    const savedAccess = localStorage.getItem('dashboardAccess')
    if (savedAccess === 'restricted') {
      router.push('/dashboard/projections')
    }
  }, [router])

  useEffect(() => {
    const savedAccess = localStorage.getItem('dashboardAccess')
    if (savedAccess) {
      setIsAuthenticated(true)
      setAccessLevel(savedAccess as 'admin' | 'restricted')
    }
  }, [])

  const handleAuthSuccess = (level: 'admin' | 'restricted') => {
    setIsAuthenticated(true)
    setAccessLevel(level)
    
    // Redirect to appropriate section based on access level
    if (level === 'restricted') {
      router.push('/dashboard/projections')
    } else {
      router.push('/dashboard/people')
    }
  }

  if (!isAuthenticated) {
    return <PasswordCheck onSuccess={handleAuthSuccess} />
  }

  const canAccessSection = (section: string, accessLevel: string | null) => {
    if (accessLevel === 'admin') return true;
    if (accessLevel === 'restricted') {
      return section === 'projections';
    }
    return false;
  };

  // Check if user can access current section
  const currentSection = getActiveSectionFromPath(pathname);
  if (!canAccessSection(currentSection, accessLevel)) {
    // Redirect to projections if user doesn't have access to current section
    router.push('/dashboard/projections');
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation 
        activeSection={activeSection} 
        setActiveSection={setActiveSection}
        isCollapsed={isCollapsed}
        setIsCollapsed={setIsCollapsed}
        accessLevel={accessLevel}
      />
      <div className={`transition-all duration-300 ${
        isCollapsed ? 'ml-16' : 'ml-40'
      }`}>
        <div className="w-full">
          {children}
        </div>
      </div>
    </div>
  )
}