"use client";

import { useState } from "react";
import ArticlesView from "@/components/dashboard/news/ArticlesView";
import ArticlesMonitor from "@/components/dashboard/news/ArticlesMonitor";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FileText, BarChart3, Settings } from "lucide-react";

export default function ArticlesPage() {
  const [activeTab, setActiveTab] = useState("monitor");

  return (
    <div className="min-h-screen bg-white">
      <main className="w-full px-6 py-6">
        {/* Header */}
        <header className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Articles Dashboard
            </h1>
            <p className="text-sm text-gray-500 mt-1">
              Monitor and manage real estate articles from multiple sources with comprehensive data extraction, processing capabilities, and scraping management
            </p>
          </div>
        </header>

        {/* Navigation Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="monitor" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Article Monitor
            </TabsTrigger>
            <TabsTrigger value="articles" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Articles List
            </TabsTrigger>
          </TabsList>

          <TabsContent value="monitor" className="space-y-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
              <h3 className="text-sm font-medium text-green-900 mb-1">Advanced Article Management System</h3>
              <p className="text-xs text-green-700">
                Enhanced article management with structured data extraction, processing controls, comprehensive analytics, and scraping management.
              </p>
            </div>
            <ArticlesMonitor isActive={activeTab === "monitor"} />
          </TabsContent>

          <TabsContent value="articles" className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
              <h3 className="text-sm font-medium text-blue-900 mb-1">Articles List View</h3>
              <p className="text-xs text-blue-700">
                Browse and search through all articles with detailed filtering and sorting options.
              </p>
            </div>
            <ArticlesView />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}
