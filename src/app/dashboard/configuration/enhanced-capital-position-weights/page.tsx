'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Loader2, 
  Plus, 
  Edit, 
  Save, 
  X, 
  Database, 
  Building2, 
  CreditCard, 
  TrendingUp,
  Home,
  AlertCircle,
  CheckCircle,
  Settings
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface CapitalPositionWeight {
  id?: number;
  capital_position: string;
  field_name: string;
  weight: number;
  description: string;
  table_name: string;
  field_category: string;
  is_active: boolean;
}

interface TableBreakdown {
  [tableName: string]: {
    total: number;
    fields: CapitalPositionWeight[];
  };
}

interface EnhancedWeightsData {
  position: string;
  weights: CapitalPositionWeight[];
  tableBreakdown: TableBreakdown;
  totals: {
    position: number;
    positionPercent: number;
  };
  status: {
    weightSource: string;
    isUsingDefaults: boolean;
    hasCustomOverrides: boolean;
    isCommon: boolean;
    tableCoverage: string[];
    debtEquitySplit: {
      debt: number;
      equity: number;
    };
  };
}

interface CapitalPosition {
  capital_position: string;
  field_count: number;
  total_weight_percent: number;
  dealsv2_weight: number;
  debt_weight: number;
  equity_weight: number;
  property_weight: number;
  position_type: string;
  description: string;
}

export default function EnhancedCapitalPositionWeightsPage() {
  const [weights, setWeights] = useState<CapitalPositionWeight[]>([]);
  const [tableBreakdown, setTableBreakdown] = useState<TableBreakdown>({});
  const [capitalPositions, setCapitalPositions] = useState<CapitalPosition[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingWeight, setEditingWeight] = useState<CapitalPositionWeight | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [selectedPosition, setSelectedPosition] = useState<string>('COMMON');
  const [showOverrideModal, setShowOverrideModal] = useState(false);
  const [overrideField, setOverrideField] = useState<CapitalPositionWeight | null>(null);
  const [overridePosition, setOverridePosition] = useState<string>('');
  const [overrideWeight, setOverrideWeight] = useState<number>(0);
  const { toast } = useToast();

  // Table display names and icons
  const tableConfig = {
    dealsv2: { name: 'Deal Fields', icon: Database, color: 'bg-blue-100 text-blue-800' },
    investment_criteria_debt: { name: 'Debt Fields', icon: CreditCard, color: 'bg-red-100 text-red-800' },
    investment_criteria_equity: { name: 'Equity Fields', icon: TrendingUp, color: 'bg-green-100 text-green-800' },
    properties: { name: 'Property Fields', icon: Home, color: 'bg-purple-100 text-purple-800' }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      // Fetch COMMON weights
      const commonResponse = await fetch('/api/dashboard/configuration/capital-position-weights?capital_position=COMMON');
      const commonData = await commonResponse.json();
      
      console.log('COMMON API Response:', commonData);
      
      // Fetch all capital positions
      const positionsResponse = await fetch('/api/dashboard/configuration/capital-position-weights');
      const positionsData = await positionsResponse.json();
      
      console.log('Positions API Response:', positionsData);
      
      if (commonData.success && positionsData.success) {
        console.log('Setting weights:', commonData.data.weights);
        console.log('Setting table breakdown:', commonData.data.tableBreakdown);
        console.log('Setting capital positions:', positionsData.data.positions);
        
        setWeights(commonData.data.weights);
        setTableBreakdown(commonData.data.tableBreakdown);
        setCapitalPositions(positionsData.data.positions);
      } else {
        console.error('API call failed:', { commonData, positionsData });
        toast({
          title: 'Error',
          description: 'Failed to fetch weights data',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch data',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditWeight = (weight: CapitalPositionWeight) => {
    setEditingWeight({ ...weight });
    setIsEditing(true);
  };

  const handleSaveWeight = async () => {
    if (!editingWeight) return;

    try {
      setSaving(true);
      const response = await fetch('/api/dashboard/configuration/capital-position-weights', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(editingWeight),
      });

      const data = await response.json();
      
      if (data.success) {
        toast({
          title: 'Success',
          description: 'Weight updated successfully',
        });
        await fetchData();
        setIsEditing(false);
        setEditingWeight(null);
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Failed to update weight',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating weight:', error);
      toast({
        title: 'Error',
        description: 'Failed to update weight',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditingWeight(null);
  };

  const handleOverrideField = (field: CapitalPositionWeight) => {
    setOverrideField(field);
    setOverridePosition('');
    setOverrideWeight(field.weight);
    setShowOverrideModal(true);
  };

  const handleSaveOverride = async () => {
    if (!overrideField || !overridePosition || overrideWeight < 0 || overrideWeight > 1) return;

    try {
      setSaving(true);
      const overrideData = {
        capital_position: overridePosition,
        field_name: overrideField.field_name,
        weight: overrideWeight,
        description: `Override: ${overrideField.description}`,
        table_name: overrideField.table_name,
        field_category: overrideField.field_category
      };

      const response = await fetch('/api/dashboard/configuration/capital-position-weights', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(overrideData),
      });

      const data = await response.json();
      
      if (data.success) {
        toast({
          title: 'Success',
          description: `Override created for ${overridePosition}`,
        });
        await fetchData();
        setShowOverrideModal(false);
        setOverrideField(null);
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Failed to create override',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error creating override:', error);
      toast({
        title: 'Error',
        description: 'Failed to create override',
        variant: 'destructive',
      });
    } finally {
      setSaving(false);
    }
  };

  const calculateTotalWeight = (tableName: string) => {
    const tableWeights = weights.filter(w => w.table_name === tableName);
    console.log(`Calculating total for ${tableName}:`, tableWeights);
    
    if (tableWeights.length === 0) {
      console.log(`No weights found for table: ${tableName}`);
      return 0;
    }
    
    const total = tableWeights.reduce((sum, weight) => {
      const weightValue = typeof weight.weight === 'number' ? weight.weight : parseFloat(weight.weight) || 0;
      console.log(`Adding weight: ${weight.field_name} = ${weightValue}`);
      return sum + weightValue;
    }, 0);
    
    console.log(`Total for ${tableName}: ${total}`);
    return isNaN(total) ? 0 : total;
  };

  const getTableIcon = (tableName: string) => {
    const config = tableConfig[tableName as keyof typeof tableConfig];
    if (config) {
      const Icon = config.icon;
      return <Icon className="h-4 w-4" />;
    }
    return null;
  };

  const getTableColor = (tableName: string) => {
    const config = tableConfig[tableName as keyof typeof tableConfig];
    return config?.color || 'bg-gray-100 text-gray-800';
  };

  const getOverriddenPositions = (fieldName: string) => {
    return capitalPositions
      .filter(pos => pos.capital_position !== 'COMMON')
      .filter(pos => {
        // This would need to be enhanced to check actual overrides
        // For now, showing all non-COMMON positions
        return true;
      })
      .map(pos => pos.capital_position);
  };

  const getPercentageColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-blue-600';
    if (percentage >= 40) return 'text-yellow-600';
    if (percentage >= 20) return 'text-orange-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading enhanced capital position weights...</span>
      </div>
    );
  }

  const dealFields = weights.filter(w => w.table_name === 'dealsv2');
  const debtFields = weights.filter(w => w.table_name === 'investment_criteria_debt');
  const equityFields = weights.filter(w => w.table_name === 'investment_criteria_equity');
  const propertyFields = weights.filter(w => w.table_name === 'properties');

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Capital Position Weights</h1>
          <p className="text-gray-600 mt-2">
            Manage field weights for deal matching. COMMON weights serve as defaults for all positions.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="outline" className="flex items-center space-x-1">
            <Settings className="h-3 w-3" />
            <span>Enhanced V2 System</span>
          </Badge>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Deal Fields</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getPercentageColor(calculateTotalWeight('dealsv2') * 100)}`}>
              {Math.round(calculateTotalWeight('dealsv2') * 100)}%
            </div>
            <p className="text-xs text-gray-500">{dealFields.length} fields</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Property Fields</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getPercentageColor(calculateTotalWeight('properties') * 100)}`}>
              {Math.round(calculateTotalWeight('properties') * 100)}%
            </div>
            <p className="text-xs text-gray-500">{propertyFields.length} fields</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Debt Fields</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getPercentageColor(calculateTotalWeight('investment_criteria_debt') * 100)}`}>
              {Math.round(calculateTotalWeight('investment_criteria_debt') * 100)}%
            </div>
            <p className="text-xs text-gray-500">{debtFields.length} fields</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Equity Fields</CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getPercentageColor(calculateTotalWeight('investment_criteria_equity') * 100)}`}>
              {Math.round(calculateTotalWeight('investment_criteria_equity') * 100)}%
            </div>
            <p className="text-xs text-gray-500">{equityFields.length} fields</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Tabs */}
      <Tabs defaultValue="deals" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="deals" className="flex items-center space-x-2">
            <Database className="h-4 w-4" />
            <span>Deal Fields</span>
          </TabsTrigger>
          <TabsTrigger value="property" className="flex items-center space-x-2">
            <Home className="h-4 w-4" />
            <span>Property Fields</span>
          </TabsTrigger>
          <TabsTrigger value="debt" className="flex items-center space-x-2">
            <CreditCard className="h-4 w-4" />
            <span>Debt Fields</span>
          </TabsTrigger>
          <TabsTrigger value="equity" className="flex items-center space-x-2">
            <TrendingUp className="h-4 w-4" />
            <span>Equity Fields</span>
          </TabsTrigger>
        </TabsList>

        {/* Deal Fields Tab */}
        <TabsContent value="deals" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5 text-blue-600" />
                <span>Deal Fields (COMMON Weights)</span>
                <Badge variant="secondary" className="ml-2">
                  {Math.round(calculateTotalWeight('dealsv2') * 100)}% Total
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {dealFields.map((field) => (
                  <div key={field.field_name} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{field.field_name}</span>
                        <Badge variant="outline" className="text-xs">
                          {Math.round(field.weight * 100)}%
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{field.description}</p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Badge variant="secondary" className="text-xs">
                          COMMON Default
                        </Badge>
                        {getOverriddenPositions(field.field_name).length > 0 && (
                          <Badge variant="outline" className="text-xs">
                            Overridden for: {getOverriddenPositions(field.field_name).join(', ')}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleOverrideField(field)}
                      >
                        <Settings className="h-4 w-4 mr-1" />
                        Override
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditWeight(field)}
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Property Fields Tab */}
        <TabsContent value="property" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Home className="h-5 w-5 text-purple-600" />
                <span>Property Fields (COMMON Weights)</span>
                <Badge variant="secondary" className="ml-2">
                  {Math.round(calculateTotalWeight('properties') * 100)}% Total
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {propertyFields.map((field) => (
                  <div key={field.field_name} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{field.field_name}</span>
                        <Badge variant="outline" className="text-xs">
                          {Math.round(field.weight * 100)}%
                        </Badge>
                        <span className="text-sm text-gray-600">{field.description}</span>
                      </div>
                      <div className="mt-1">
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          COMMON Default
                        </span>
                        {getOverriddenPositions(field.field_name).length > 0 && (
                          <span className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded ml-2">
                            Overridden for: {getOverriddenPositions(field.field_name).join(', ')}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleOverrideField(field)}
                      >
                        <Settings className="h-4 w-4 mr-1" />
                        Override
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditWeight(field)}
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Debt Fields Tab */}
        <TabsContent value="debt" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CreditCard className="h-5 w-5 text-red-600" />
                <span>Debt Fields (COMMON Weights)</span>
                <Badge variant="secondary" className="ml-2">
                  {Math.round(calculateTotalWeight('investment_criteria_debt') * 100)}% Total
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {debtFields.map((field) => (
                  <div key={field.field_name} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{field.field_name}</span>
                        <Badge variant="outline" className="text-xs">
                          {Math.round(field.weight * 100)}%
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{field.description}</p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Badge variant="secondary" className="text-xs">
                          COMMON Default
                        </Badge>
                        {getOverriddenPositions(field.field_name).length > 0 && (
                          <Badge variant="outline" className="text-xs">
                            Overridden for: {getOverriddenPositions(field.field_name).join(', ')}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleOverrideField(field)}
                      >
                        <Settings className="h-4 w-4 mr-1" />
                        Override
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditWeight(field)}
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Equity Fields Tab */}
        <TabsContent value="equity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <span>Equity Fields (COMMON Weights)</span>
                <Badge variant="secondary" className="ml-2">
                  {Math.round(calculateTotalWeight('investment_criteria_equity') * 100)}% Total
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {equityFields.map((field) => (
                  <div key={field.field_name} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">{field.field_name}</span>
                        <Badge variant="outline" className="text-xs">
                          {Math.round(field.weight * 100)}%
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{field.description}</p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Badge variant="secondary" className="text-xs">
                          COMMON Default
                        </Badge>
                        {getOverriddenPositions(field.field_name).length > 0 && (
                          <Badge variant="outline" className="text-xs">
                            Overridden for: {getOverriddenPositions(field.field_name).join(', ')}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleOverrideField(field)}
                      >
                        <Settings className="h-4 w-4 mr-1" />
                        Override
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditWeight(field)}
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Edit Weight Modal */}
      {isEditing && editingWeight && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Edit Weight</h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="fieldName">Field Name</Label>
                <Input
                  id="fieldName"
                  value={editingWeight.field_name}
                  disabled
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="weight">Weight (0-1)</Label>
                <Input
                  id="weight"
                  type="number"
                  step="0.01"
                  min="0"
                  max="1"
                  value={editingWeight.weight}
                  onChange={(e) => setEditingWeight({
                    ...editingWeight,
                    weight: parseFloat(e.target.value) || 0
                  })}
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={editingWeight.description}
                  onChange={(e) => setEditingWeight({
                    ...editingWeight,
                    description: e.target.value
                  })}
                  className="mt-1"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-2 mt-6">
              <Button variant="outline" onClick={handleCancelEdit}>
                Cancel
              </Button>
              <Button onClick={handleSaveWeight} disabled={saving}>
                {saving ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                Save
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Override Modal */}
      {showOverrideModal && overrideField && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Create Override</h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="overrideField">Field</Label>
                <Input
                  id="overrideField"
                  value={overrideField.field_name}
                  disabled
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="overridePosition">Capital Position</Label>
                <Select value={overridePosition} onValueChange={setOverridePosition}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select position" />
                  </SelectTrigger>
                  <SelectContent>
                    {capitalPositions
                      .filter(pos => pos.capital_position !== 'COMMON')
                      .map(pos => (
                        <SelectItem key={pos.capital_position} value={pos.capital_position}>
                          {pos.capital_position}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="overrideWeight">Weight (0-1)</Label>
                <Input
                  id="overrideWeight"
                  type="number"
                  step="0.01"
                  min="0"
                  max="1"
                  value={overrideWeight}
                  onChange={(e) => setOverrideWeight(parseFloat(e.target.value) || 0)}
                  className="mt-1"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-2 mt-6">
              <Button variant="outline" onClick={() => setShowOverrideModal(false)}>
                Cancel
              </Button>
              <Button 
                onClick={handleSaveOverride} 
                disabled={saving || !overridePosition || overrideWeight < 0 || overrideWeight > 1}
              >
                {saving ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                Create Override
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
