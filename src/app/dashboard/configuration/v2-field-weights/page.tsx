'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/hooks/use-toast';

interface CapitalPositionWeight {
  field_name: string;
  weight: number;
  description: string;
  is_active: boolean;
  weight_type: 'COMMON' | 'POSITION' | 'CUSTOM';
  source?: 'COMMON' | 'POSITION';
}

interface CapitalPositionWeights {
  capital_position: string;
  weights: CapitalPositionWeight[];
  commonWeights: CapitalPositionWeight[];
  totals: {
    position: number;
    common: number;
    final: number;
    positionPercent: number;
    commonPercent: number;
    finalPercent: number;
  };
  status: {
    weightSource: string;
    isUsingDefaults: boolean;
    hasCustomOverrides: boolean;
    isCommon: boolean;
    hasCommonInheritance: boolean;
  };
  message: string;
}

interface PositionSummary {
  capital_position: string;
  field_count: number;
  total_weight_percent: number;
  position_type: 'COMMON' | 'POSITION' | 'CUSTOM';
  description: string;
}

export default function V2FieldWeightsPage() {
  const [positions, setPositions] = useState<PositionSummary[]>([]);
  const [selectedPosition, setSelectedPosition] = useState<string>('');
  const [positionWeights, setPositionWeights] = useState<CapitalPositionWeights | null>(null);
  const [loading, setLoading] = useState(false);
  const [editingWeights, setEditingWeights] = useState<{ [key: string]: number }>({});
  const [isEditing, setIsEditing] = useState(false);

  const capitalPositions = [
    'COMMON',
    'Senior Debt',
    'Mezzanine',
    'Preferred Equity',
    'Common Equity',
    'General Partner (GP)',
    'Limited Partner (LP)',
    'Bridge Loan',
    'Construction Loan',
    'Permanent Loan',
    'Refinance Loan',
    'Joint Venture (JV)',
    'Co-GP'
  ];

  useEffect(() => {
    fetchPositions();
  }, []);

  const fetchPositions = async () => {
    try {
      const response = await fetch('/api/dashboard/configuration/capital-position-weights');
      const data = await response.json();
      if (data.success) {
        setPositions(data.data.positions);
      }
    } catch (error) {
      console.error('Error fetching positions:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch positions',
        variant: 'destructive',
      });
    }
  };

  const fetchPositionWeights = async (position: string) => {
    if (!position) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/dashboard/configuration/capital-position-weights?capital_position=${encodeURIComponent(position)}&include_common=true`);
      const data = await response.json();
      if (data.success) {
        setPositionWeights(data.data);
        // Initialize editing weights with current values
        const editing: { [key: string]: number } = {};
        data.data.weights.forEach((weight: CapitalPositionWeight) => {
          editing[weight.field_name] = weight.weight;
        });
        setEditingWeights(editing);
      }
    } catch (error) {
      console.error('Error fetching position weights:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch position weights',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePositionChange = (position: string) => {
    setSelectedPosition(position);
    if (position) {
      fetchPositionWeights(position);
    } else {
      setPositionWeights(null);
    }
  };

  const updateWeight = async (fieldName: string, newWeight: number) => {
    if (!selectedPosition || !positionWeights) return;

    try {
      const response = await fetch(`/api/matching-v2/capital-position-weights/${encodeURIComponent(selectedPosition)}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          field_name: fieldName,
          weight: newWeight,
          description: `Override: ${fieldName} for ${selectedPosition}`,
          is_active: true
        })
      });

      const data = await response.json();
      if (data.success) {
        toast({
          title: 'Success',
          description: `Weight updated for ${fieldName}`,
        });
        // Refresh the data
        fetchPositionWeights(selectedPosition);
      }
    } catch (error) {
      console.error('Error updating weight:', error);
      toast({
        title: 'Error',
        description: 'Failed to update weight',
        variant: 'destructive',
      });
    }
  };

  const resetToDefaults = async () => {
    if (!selectedPosition || !positionWeights) return;

    try {
      const response = await fetch(`/api/matching-v2/capital-position-weights/${encodeURIComponent(selectedPosition)}?reset_to_defaults=true`, {
        method: 'DELETE'
      });

      const data = await response.json();
      if (data.success) {
        toast({
          title: 'Success',
          description: `${selectedPosition} reset to default weights`,
        });
        // Refresh the data
        fetchPositionWeights(selectedPosition);
      }
    } catch (error) {
      console.error('Error resetting to defaults:', error);
      toast({
        title: 'Error',
        description: 'Failed to reset to defaults',
        variant: 'destructive',
      });
    }
  };

  const saveChanges = async () => {
    if (!selectedPosition || !positionWeights) return;

    try {
      const updates = Object.entries(editingWeights).map(([fieldName, weight]) => ({
        capital_position: selectedPosition,
        field_name: fieldName,
        weight,
        description: `Override: ${fieldName} for ${selectedPosition}`,
        is_active: true
      }));

      const response = await fetch('/api/matching-v2/capital-position-weights', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ updates })
      });

      const data = await response.json();
      if (data.success) {
        toast({
          title: 'Success',
          description: 'All weights updated successfully',
        });
        setIsEditing(false);
        fetchPositionWeights(selectedPosition);
      }
    } catch (error) {
      console.error('Error saving changes:', error);
      toast({
        title: 'Error',
        description: 'Failed to save changes',
        variant: 'destructive',
      });
    }
  };

  const cancelEditing = () => {
    setIsEditing(false);
    if (positionWeights) {
      const editing: { [key: string]: number } = {};
      positionWeights.weights.forEach((weight: CapitalPositionWeight) => {
        editing[weight.field_name] = weight.weight;
      });
      setEditingWeights(editing);
    }
  };

  const getWeightTypeColor = (weightType: string) => {
    switch (weightType) {
      case 'COMMON': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'POSITION': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'CUSTOM': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getWeightTypeLabel = (weightType: string) => {
    switch (weightType) {
      case 'COMMON': return 'COMMON';
      case 'POSITION': return 'POSITION';
      case 'CUSTOM': return 'CUSTOM';
      default: return 'UNKNOWN';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">V2 Field Weights Configuration</h1>
          <p className="text-muted-foreground">
            Configure field weights for capital position matching with COMMON inheritance system
          </p>
        </div>
      </div>

      <Alert>
        <AlertDescription>
          <strong>COMMON Inheritance System:</strong> COMMON contains base weights (55%) that are inherited by all positions. 
          Individual positions can override specific fields (45%) to create their unique matching criteria. 
          Total always equals 100% (COMMON + Position-specific = 100%).
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Positions Overview */}
        <Card>
          <CardHeader>
            <CardTitle>Capital Positions</CardTitle>
            <CardDescription>Select a position to configure weights</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Select value={selectedPosition} onValueChange={handlePositionChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select a capital position" />
              </SelectTrigger>
              <SelectContent>
                {capitalPositions.map((position) => (
                  <SelectItem key={position} value={position}>
                    {position}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {positions.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">System Overview</h4>
                {positions.map((position) => (
                  <div key={position.capital_position} className="flex items-center justify-between p-2 rounded border">
                    <div>
                      <span className="font-medium">{position.capital_position}</span>
                      <Badge className={`ml-2 ${getWeightTypeColor(position.position_type)}`}>
                        {getWeightTypeLabel(position.position_type)}
                      </Badge>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {position.total_weight_percent}%
                    </span>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Weights Configuration */}
        <div className="lg:col-span-2">
          {selectedPosition && positionWeights ? (
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{selectedPosition} Weights</CardTitle>
                    <CardDescription>
                      {positionWeights.status.weightSource} • {positionWeights.totals.finalPercent}% total
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    {isEditing ? (
                      <>
                        <Button onClick={saveChanges} size="sm">Save</Button>
                        <Button onClick={cancelEditing} variant="outline" size="sm">Cancel</Button>
                      </>
                    ) : (
                      <>
                        <Button onClick={() => setIsEditing(true)} size="sm">Edit</Button>
                        {!positionWeights.status.isCommon && (
                          <Button onClick={resetToDefaults} variant="outline" size="sm">
                            Reset to Defaults
                          </Button>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="text-center py-8">Loading...</div>
                ) : (
                  <div className="space-y-6">
                    {/* COMMON Weights Section */}
                    {positionWeights.commonWeights.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-3 flex items-center gap-2">
                          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                            COMMON Base Weights
                          </Badge>
                          <span className="text-muted-foreground">
                            ({positionWeights.totals.commonPercent}% inherited)
                          </span>
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {positionWeights.commonWeights.map((weight) => (
                            <div key={weight.field_name} className="flex items-center justify-between p-3 rounded border bg-blue-50">
                              <div>
                                <Label className="font-medium capitalize">
                                  {weight.field_name.replace(/_/g, ' ')}
                                </Label>
                                <p className="text-sm text-muted-foreground">{weight.description}</p>
                              </div>
                              <div className="text-right">
                                <span className="font-medium">{Math.round(weight.weight * 100)}%</span>
                                <Badge className="ml-2 bg-blue-100 text-blue-800 border-blue-200">
                                  COMMON
                                </Badge>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Position-Specific Weights Section */}
                    {positionWeights.weights.length > 0 && (
                      <div>
                        <h4 className="font-medium mb-3 flex items-center gap-2">
                          <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                            Position-Specific Weights
                          </Badge>
                          <span className="text-muted-foreground">
                            ({positionWeights.totals.positionPercent}% overrides)
                          </span>
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {positionWeights.weights.map((weight) => (
                            <div key={weight.field_name} className="flex items-center justify-between p-3 rounded border bg-orange-50">
                              <div>
                                <Label className="font-medium capitalize">
                                  {weight.field_name.replace(/_/g, ' ')}
                                </Label>
                                <p className="text-sm text-muted-foreground">{weight.description}</p>
                              </div>
                              <div className="text-right">
                                {isEditing ? (
                                  <Input
                                    type="number"
                                    min="0"
                                    max="1"
                                    step="0.01"
                                    value={editingWeights[weight.field_name] || 0}
                                    onChange={(e) => setEditingWeights({
                                      ...editingWeights,
                                      [weight.field_name]: parseFloat(e.target.value) || 0
                                    })}
                                    className="w-20 text-right"
                                  />
                                ) : (
                                  <span className="font-medium">{Math.round(weight.weight * 100)}%</span>
                                )}
                                <Badge className={`ml-2 ${getWeightTypeColor(weight.weight_type)}`}>
                                  {getWeightTypeLabel(weight.weight_type)}
                                </Badge>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Final Calculation */}
                    <Separator />
                    <div className="bg-gray-50 p-4 rounded">
                      <h4 className="font-medium mb-3">Final Weight Calculation</h4>
                      <div className="grid grid-cols-3 gap-4 text-center">
                        <div>
                          <div className="text-2xl font-bold text-blue-600">
                            {positionWeights.totals.commonPercent}%
                          </div>
                          <div className="text-sm text-muted-foreground">COMMON Base</div>
                        </div>
                        <div className="text-2xl font-bold text-orange-600">+</div>
                        <div>
                          <div className="text-2xl font-bold text-orange-600">
                            {positionWeights.totals.positionPercent}%
                          </div>
                          <div className="text-sm text-muted-foreground">Position Overrides</div>
                        </div>
                      </div>
                      <div className="text-center mt-3 pt-3 border-t">
                        <div className="text-3xl font-bold text-green-600">
                          = {positionWeights.totals.finalPercent}%
                        </div>
                        <div className="text-sm text-muted-foreground">Total Weight</div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <p className="text-muted-foreground">Select a capital position to configure weights</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
