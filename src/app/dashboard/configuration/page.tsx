"use client";

import React, { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import EnhancedCapitalPositionWeights from "./enhanced-capital-position-weights/page";

// Types for Gmail accounts, threads, and messages
interface GmailAccount {
  id: string;
  email: string;
  lastSync?: string;
  exportMode?: string; // Added for export mode
}
interface GmailThread {
  id: string;
  provider_thread_id: string;
  subject: string;
  created_at: string;
}
interface GmailMessage {
  id: string;
  sender: string;
  recipients: string[];
  subject: string;
  sent_at: string;
  body: string;
}

// Types for Fireflies accounts and transcripts
interface FirefliesAccount {
  id: string;
  name: string;
  status: string;
  lastSync?: string;
  errorCount?: number;
  lastError?: string;
}
// Types for Fireflies transcripts
interface FirefliesTranscript {
  id: string;
  provider_transcript_id: string;
  title: string;
  meeting_date: string;
  duration: number;
  participants: string[];
  transcript_text: string;
  sentences: Array<{
    text: string;
    raw_text: string;
    speaker_name: string;
    speaker_id: string;
    start_time: string;
    end_time: string;
    index?: number;
    ai_filters?: any;
  }>;
  account_name: string;
}

const fetchGmailAccounts = async (): Promise<GmailAccount[]> => {
  const res = await fetch("/api/dashboard/configuration/gmail");
  if (!res.ok) return [];
  const data = await res.json();
  return data.map((row: any) => ({
    id: row.id,
    email: row.email,
    lastSync: row.updated_at
      ? new Date(row.updated_at).toLocaleString()
      : undefined,
  }));
};

const addGmailAccount = async (email: string) => {
  const res = await fetch("/api/dashboard/configuration/gmail", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ email }),
  });
  if (!res.ok) throw new Error("Failed to add account");
  const row = await res.json();
  return {
    id: row.id,
    email: row.email,
    lastSync: row.updated_at
      ? new Date(row.updated_at).toLocaleString()
      : undefined,
  };
};

const removeGmailAccount = async (id: string) => {
  const res = await fetch("/api/dashboard/configuration/gmail", {
    method: "DELETE",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ id }),
  });
  if (!res.ok) throw new Error("Failed to remove account");
  return true;
};

const fetchThreads = async (accountId: string): Promise<GmailThread[]> => {
  const res = await fetch(
    `/api/dashboard/configuration/gmail/threads?accountId=${accountId}`
  );
  if (!res.ok) return [];
  const result = await res.json();
  return result.data || [];
};

const fetchMessages = async (threadId: string): Promise<GmailMessage[]> => {
  const res = await fetch(
    `/api/dashboard/configuration/gmail/messages?threadId=${threadId}`
  );
  if (!res.ok) return [];
  return await res.json();
};

// Fireflies API functions
const fetchFirefliesAccounts = async (): Promise<FirefliesAccount[]> => {
  const res = await fetch("/api/dashboard/configuration/fireflies");
  if (!res.ok) return [];
  const data = await res.json();
  return data.map((row: any) => ({
    id: row.id,
    name: row.name,
    status: row.status,
    lastSync: row.last_sync_at
      ? new Date(row.last_sync_at).toLocaleString()
      : undefined,
    errorCount: row.error_count,
    lastError: row.last_error,
  }));
};

const addFirefliesAccount = async (name: string, apiKey: string) => {
  const res = await fetch("/api/dashboard/configuration/fireflies", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ name, apiKey }),
  });
  if (!res.ok) throw new Error("Failed to add account");
  const row = await res.json();
  return {
    id: row.id,
    name: row.name,
    status: row.status,
    lastSync: row.last_sync_at
      ? new Date(row.last_sync_at).toLocaleString()
      : undefined,
    errorCount: row.error_count,
    lastError: row.last_error,
  };
};

const removeFirefliesAccount = async (id: string) => {
  const res = await fetch(`/api/dashboard/configuration/fireflies?id=${id}`, {
    method: "DELETE",
  });
  if (!res.ok) throw new Error("Failed to remove account");
  return true;
};

const fetchFirefliesTranscripts = async (accountId?: string): Promise<{
  transcripts: FirefliesTranscript[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}> => {
  const params = new URLSearchParams();
  if (accountId) params.append("accountId", accountId);
  params.append("page", "1");
  params.append("limit", "50");
  
  const res = await fetch(
    `/api/dashboard/configuration/fireflies/transcripts?${params.toString()}`
  );
  if (!res.ok) return { transcripts: [], pagination: { page: 1, limit: 50, total: 0, totalPages: 0 } };
  return await res.json();
};

const TABS = [
  { key: "gmail", label: "Gmail" },
  { key: "fireflies", label: "Fireflies" },
  { key: "field_weights", label: "Investment Criteria Weights" },
  { key: "capital_position_weights", label: "Capital Position Weights" },
  // Add more tabs here for other integrations/settings
];

const ConfigurationPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState("gmail");
  const [gmailAccounts, setGmailAccounts] = useState<GmailAccount[]>([]);
  const [newGmailEmail, setNewGmailEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [expandedAccount, setExpandedAccount] = useState<string | null>(null);
  const [accountThreads, setAccountThreads] = useState<{
    [accountId: string]: GmailThread[];
  }>({});
  const [expandedThread, setExpandedThread] = useState<string | null>(null);
  const [threadMessages, setThreadMessages] = useState<{
    [threadId: string]: GmailMessage[];
  }>({});

  // Field Weights State
  const [fieldWeights, setFieldWeights] = useState<any[]>([]);
  const [fieldWeightsLoading, setFieldWeightsLoading] = useState(false);
  const [fieldWeightsError, setFieldWeightsError] = useState<string | null>(
    null
  );
  const [editingWeights, setEditingWeights] = useState<{
    [field: string]: number;
  }>({});
  const [savingField, setSavingField] = useState<string | null>(null);
  const [budgetExceeded, setBudgetExceeded] = useState(false);

  // Capital Position Weights State
  const [capitalPositionWeights, setCapitalPositionWeights] = useState<any[]>([]);
  const [capitalPositionWeightsLoading, setCapitalPositionWeightsLoading] = useState(false);
  const [capitalPositionWeightsError, setCapitalPositionWeightsError] = useState<string | null>(null);
  const [editingCapitalPositionWeights, setEditingCapitalPositionWeights] = useState<{
    [key: string]: number;
  }>({});
  const [savingCapitalPositionWeight, setSavingCapitalPositionWeight] = useState<string | null>(null);
  const [newCapitalPosition, setNewCapitalPosition] = useState("");
  
  // Common weights state for enhanced UI
  const [commonWeights, setCommonWeights] = useState<{[key: string]: number}>({
    location: 0.25,
    deal_size: 0.20,
    property_types: 0.15,
    strategies: 0.10,
    target_return: 0.10,
    loan_to_value: 0.05,
    loan_to_cost: 0.05,
    interest_rate: 0.03,
    loan_term: 0.03,
    loan_dscr: 0.02,
    hold_period: 0.02
  });
  
  // Enhanced UI state
  const [selectedCapitalPosition, setSelectedCapitalPosition] = useState<string>('all');
  const [capitalPositions] = useState<string[]>([
    'Senior Debt',
    'Mezzanine', 
    'Preferred Equity',
    'Common Equity',
    'General Partner (GP)',
    'Limited Partner (LP)',
    'Stretch Senior',
    'Co-GP',
    'Joint Venture (JV)'
  ]);
  const [newFieldName, setNewFieldName] = useState("");
  const [newWeight, setNewWeight] = useState("");
  const [newDescription, setNewDescription] = useState("");
  const [dealsV2Weights, setDealsV2Weights] = useState<{[key: string]: number}>({
    deal_amount: 0.15,
    property_type: 0.12,
    strategy: 0.15,
    hold_period: 0.10,
    target_irr: 0.12,
    target_multiple: 0.10,
    deal_stage: 0.08,
    deal_status: 0.08,
    capital_raise_timeline: 0.06,
    yield_on_cost: 0.04
  });
  const [debtEquityWeights, setDebtEquityWeights] = useState<{[key: string]: number}>({
    loan_to_value: 0.15,
    loan_to_cost: 0.12,
    debt_service_coverage: 0.15,
    interest_rate: 0.10,
    loan_term: 0.08,
    amortization: 0.06,
    target_return: 0.12,
    equity_multiple: 0.08,
    cash_on_cash: 0.08,
    preferred_return: 0.06,
    hold_period_years: 0.04,
    ownership_requirement: 0.03,
    attachment_point: 0.03,
    max_leverage_tolerance: 0.02
  });

  // Add state for message sort order
  const [messageSortOrder, setMessageSortOrder] = useState<'asc' | 'desc'>('desc');

  // Fireflies state
  const [firefliesAccounts, setFirefliesAccounts] = useState<FirefliesAccount[]>([]);
  const [newFirefliesName, setNewFirefliesName] = useState("");
  const [newFirefliesApiKey, setNewFirefliesApiKey] = useState("");
  const [expandedFirefliesAccount, setExpandedFirefliesAccount] = useState<string | null>(null);
  const [firefliesTranscripts, setFirefliesTranscripts] = useState<{
    [accountId: string]: FirefliesTranscript[];
  }>({});

  // Calculate total weight and budget
  const calculateWeightBudget = () => {
    const nonCapitalFields = fieldWeights.filter(fw => fw.field_name !== 'capital_position');
    let currentTotal = 0;
    
    nonCapitalFields.forEach(fw => {
      const fieldName = fw.field_name;
      const weight = editingWeights[fieldName] !== undefined ? editingWeights[fieldName] : fw.weight;
      currentTotal += Number(weight);
    });
    
    // Round to 6 decimal places to avoid floating-point precision issues
    currentTotal = Math.round(currentTotal * 1000000) / 1000000;
    
    const remaining = 1.0 - currentTotal;
    const percentage = Math.round(currentTotal * 100 * 10) / 10; // Round to 1 decimal place
    
    return {
      currentTotal,
      remaining,
      percentage,
      exceeded: currentTotal > 1.001, // Allow tiny floating-point tolerance
      nearLimit: currentTotal > 0.95 && currentTotal <= 1.001,
      isExactly100: Math.abs(percentage - 100) < 0.05 // More forgiving tolerance (0.05%)
    };
  };

  const weightBudget = calculateWeightBudget();

  const loadGmailAccounts = async () => {
    setLoading(true);
    const accounts = await fetchGmailAccounts();
    setGmailAccounts(accounts);
    setLoading(false);
  };

  // Fetch field weights
  const loadFieldWeights = async () => {
    setFieldWeightsLoading(true);
    setFieldWeightsError(null);
    try {
      const res = await fetch("/api/dashboard/configuration/field-weights");
      if (!res.ok) throw new Error("Failed to fetch field weights");
      const data = await res.json();
      setFieldWeights(data.field_weights || []);
    } catch (e: any) {
      setFieldWeightsError(e.message || "Unknown error");
    }
    setFieldWeightsLoading(false);
  };

  // Fetch capital position weights
  const loadCapitalPositionWeights = async () => {
    setCapitalPositionWeightsLoading(true);
    setCapitalPositionWeightsError(null);
    try {
      const res = await fetch("/api/dashboard/configuration/capital-position-weights");
      if (!res.ok) throw new Error("Failed to fetch capital position weights");
      const data = await res.json();
      if (data.success) {
        setCapitalPositionWeights(data.data.positions || []);
      } else {
        throw new Error(data.message || "Failed to fetch capital position weights");
      }
    } catch (e: any) {
      setCapitalPositionWeightsError(e.message || "Unknown error");
    }
    setCapitalPositionWeightsLoading(false);
  };

  // Save capital position weight
  const saveCapitalPositionWeight = async (capitalPosition: string, fieldName: string, weight: number, description: string) => {
    setSavingCapitalPositionWeight(`${capitalPosition}-${fieldName}`);
    try {
      const res = await fetch("/api/dashboard/configuration/capital-position-weights", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ capitalPosition, fieldName, weight, description }),
      });
      if (!res.ok) throw new Error("Failed to save capital position weight");
      await loadCapitalPositionWeights();
      setEditingCapitalPositionWeights(prev => {
        const newState = { ...prev };
        delete newState[`${capitalPosition}-${fieldName}`];
        return newState;
      });
    } catch (e: any) {
      alert(`Error saving weight: ${e.message}`);
    }
    setSavingCapitalPositionWeight(null);
  };

  // Delete capital position weight
  const deleteCapitalPositionWeight = async (capitalPosition: string, fieldName: string) => {
    if (!confirm("Are you sure you want to delete this weight?")) return;
    
    try {
      const res = await fetch("/api/dashboard/configuration/capital-position-weights", {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ capitalPosition, fieldName }),
      });
      if (!res.ok) throw new Error("Failed to delete capital position weight");
      await loadCapitalPositionWeights();
    } catch (e: any) {
      alert(`Error deleting weight: ${e.message}`);
    }
  };

  // Add new capital position weight
  const addCapitalPositionWeight = async () => {
    if (!newCapitalPosition || !newFieldName || !newWeight || !newDescription) {
      alert("Please fill in all fields");
      return;
    }

    const weight = parseFloat(newWeight);
    if (isNaN(weight) || weight < 0 || weight > 1) {
      alert("Weight must be a number between 0 and 1");
      return;
    }

    try {
      const res = await fetch("/api/dashboard/configuration/capital-position-weights", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ 
          capitalPosition: newCapitalPosition, 
          fieldName: newFieldName, 
          weight, 
          description: newDescription 
        }),
      });
      if (!res.ok) throw new Error("Failed to add capital position weight");
      
      // Clear form
      setNewCapitalPosition("");
      setNewFieldName("");
      setNewWeight("");
      setNewDescription("");
      
      await loadCapitalPositionWeights();
    } catch (e: any) {
      alert(`Error adding weight: ${e.message}`);
    }
  };

  // Enhanced UI helper functions
  const getCommonWeight = (field: string): number => {
    return commonWeights[field] || 0;
  };

  const updateCommonWeight = (field: string, weight: number) => {
    setCommonWeights(prev => ({
      ...prev,
      [field]: weight
    }));
  };

  const getCommonWeightsTotal = (): number => {
    return Object.values(commonWeights).reduce((sum, weight) => sum + weight, 0);
  };

  const getPositionWeight = (position: string, field: string): number => {
    const weight = capitalPositionWeights.find(w => 
      w.capital_position === position && w.field_name === field
    );
    return weight ? weight.weight : getCommonWeight(field);
  };

  const updatePositionWeight = (position: string, field: string, weight: number) => {
    // Update the capitalPositionWeights array
    setCapitalPositionWeights(prev => {
      const existingIndex = prev.findIndex(w => 
        w.capital_position === position && w.field_name === field
      );
      
      if (existingIndex >= 0) {
        // Update existing weight
        const newWeights = [...prev];
        newWeights[existingIndex] = {
          ...newWeights[existingIndex],
          weight: weight
        };
        return newWeights;
      } else {
        // Add new weight
        return [...prev, {
          capital_position: position,
          field_name: field,
          weight: weight,
          description: `Weight for ${field} in ${position}`,
          is_active: true
        }];
      }
    });
  };

  const getPositionTotalWeight = (position: string): number => {
    const fields = ['location', 'deal_size', 'property_types', 'strategies', 'target_return', 'loan_to_value', 'loan_to_cost', 'interest_rate', 'loan_term', 'loan_dscr', 'hold_period'];
    return fields.reduce((sum, field) => sum + getPositionWeight(position, field), 0);
  };

  const applyCommonWeightsToAll = async () => {
    setCapitalPositionWeightsLoading(true);
    try {
      const promises = ['Senior Debt', 'Mezzanine', 'Preferred Equity', 'Common Equity', 'General Partner (GP)', 'Limited Partner (LP)', 'Stretch Senior', 'Co-GP', 'Joint Venture (JV)'].map(async (position) => {
        const fieldWeights = Object.entries(commonWeights).map(([field, weight]) => ({
          capital_position: position,
          field_name: field,
          weight: weight,
          description: `Weight for ${field} in ${position}`,
          is_active: true
        }));

        const response = await fetch("/api/dashboard/configuration/capital-position-weights", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ field_weights: fieldWeights }),
        });

        if (!response.ok) {
          throw new Error(`Failed to update ${position}`);
        }
      });

      await Promise.all(promises);
      await loadCapitalPositionWeights();
    } catch (error: any) {
      setCapitalPositionWeightsError(error.message || "Unknown error");
    } finally {
      setCapitalPositionWeightsLoading(false);
    }
  };

  const resetPositionToCommon = async (position: string) => {
    try {
      const fieldWeights = Object.entries(commonWeights).map(([field, weight]) => ({
        capital_position: position,
        field_name: field,
        weight: weight,
        description: `Weight for ${field} in ${position}`,
        is_active: true
      }));

      const response = await fetch("/api/dashboard/configuration/capital-position-weights", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ field_weights: fieldWeights }),
      });

      if (!response.ok) {
        throw new Error(`Failed to reset ${position}`);
      }

      await loadCapitalPositionWeights();
    } catch (error: any) {
      setCapitalPositionWeightsError(error.message || "Unknown error");
    }
  };

  const savePositionWeights = async (position: string) => {
    try {
      const fieldWeights = ['location', 'deal_size', 'property_types', 'strategies', 'target_return', 'loan_to_value', 'loan_to_cost', 'interest_rate', 'loan_term', 'loan_dscr', 'hold_period'].map(field => ({
        capital_position: position,
        field_name: field,
        weight: getPositionWeight(position, field),
        description: `Weight for ${field} in ${position}`,
        is_active: true
      }));

      const response = await fetch("/api/dashboard/configuration/capital-position-weights", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ field_weights: fieldWeights }),
      });

      if (!response.ok) {
        throw new Error(`Failed to save ${position} weights`);
      }

      await loadCapitalPositionWeights();
    } catch (error: any) {
      setCapitalPositionWeightsError(error.message || "Unknown error");
    }
  };

  // Additional helper functions for enhanced UI
  const getFieldDescription = (field: string): string => {
    const descriptions: {[key: string]: string} = {
      'location': 'Geographic matching (region/state/city hierarchy)',
      'deal_size': 'Deal size overlap with complex fuzzy logic',
      'property_types': 'Property type array matching',
      'strategies': 'Investment strategies array matching',
      'target_return': 'Target return/IRR range matching',
      'loan_to_value': 'LTV range overlap matching',
      'loan_to_cost': 'LTC range overlap matching',
      'interest_rate': 'Interest rate comparison matching',
      'loan_term': 'Loan term range matching',
      'loan_dscr': 'DSCR range overlap matching',
      'hold_period': 'Hold period range matching'
    };
    return descriptions[field] || `Weight for ${field}`;
  };

  const savePositionWeight = async (position: string, field: string, weight: number) => {
    try {
      const response = await fetch("/api/dashboard/configuration/capital-position-weights", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          capital_position: position,
          field_name: field,
          weight: weight,
          description: `Weight for ${field} in ${position}`,
          is_active: true
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to save ${field} weight for ${position}`);
      }

      // Clear editing state
      setEditingWeights(prev => {
        const newState = { ...prev };
        delete newState[`${position}-${field}`];
        return newState;
      });

      await loadCapitalPositionWeights();
    } catch (error: any) {
      setCapitalPositionWeightsError(error.message || "Unknown error");
    }
  };

  // Additional helper functions for enhanced table UI
  const saveCommonWeight = async (field: string, weight: number) => {
    try {
      // Update common weight in state
      updateCommonWeight(field, weight);
      
      // Clear editing state
      setEditingWeights(prev => {
        const newState = { ...prev };
        delete newState[`common-${field}`];
        return newState;
      });

      // Apply to all positions
      await applyCommonWeightsToAll();
    } catch (error: any) {
      setCapitalPositionWeightsError(error.message || "Unknown error");
    }
  };

  const applyCommonWeightToAllPositions = async (field: string) => {
    try {
      const commonWeight = getCommonWeight(field);
      
      const promises = capitalPositions.map(async (position) => {
        const response = await fetch("/api/dashboard/configuration/capital-position-weights", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            capital_position: position,
            field_name: field,
            weight: commonWeight,
            description: `Weight for ${field} in ${position}`,
            is_active: true
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to update ${field} for ${position}`);
        }
      });

      await Promise.all(promises);
      await loadCapitalPositionWeights();
    } catch (error: any) {
      setCapitalPositionWeightsError(error.message || "Unknown error");
    }
  };

  const resetFieldToCommon = async (field: string) => {
    try {
      const commonWeight = getCommonWeight(field);
      
      const promises = capitalPositions.map(async (position) => {
        const response = await fetch("/api/dashboard/configuration/capital-position-weights", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            capital_position: position,
            field_name: field,
            weight: commonWeight,
            description: `Weight for ${field} in ${position}`,
            is_active: true
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to reset ${field} for ${position}`);
        }
      });

      await Promise.all(promises);
      await loadCapitalPositionWeights();
    } catch (error: any) {
      setCapitalPositionWeightsError(error.message || "Unknown error");
    }
  };

  const resetAllPositionsToCommon = async () => {
    if (!confirm("Are you sure you want to reset ALL positions back to common weights? This will undo all customizations.")) {
      return;
    }

    setCapitalPositionWeightsLoading(true);
    try {
      const promises = capitalPositions.map(async (position) => {
        const fieldWeights = Object.entries(commonWeights).map(([field, weight]) => ({
          capital_position: position,
          field_name: field,
          weight: weight,
          description: `Weight for ${field} in ${position}`,
          is_active: true
        }));

        const response = await fetch("/api/dashboard/configuration/capital-position-weights", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ field_weights: fieldWeights }),
        });

        if (!response.ok) {
          throw new Error(`Failed to reset ${position}`);
        }
      });

      await Promise.all(promises);
      await loadCapitalPositionWeights();
    } catch (error: any) {
      setCapitalPositionWeightsError(error.message || "Unknown error");
    } finally {
      setCapitalPositionWeightsLoading(false);
    }
  };

  // Additional helper functions for DealsV2 and Debt/Equity tabs
  const getDealsV2FieldDescription = (field: string): string => {
    const descriptions: {[key: string]: string} = {
      'deal_amount': 'Deal size and investment amount matching',
      'property_type': 'Property type classification matching',
      'strategy': 'Investment strategy and approach',
      'hold_period': 'Investment hold period matching',
      'target_irr': 'Target internal rate of return',
      'target_multiple': 'Target equity multiple',
      'deal_stage': 'Current stage of the deal',
      'deal_status': 'Status of the deal',
      'capital_raise_timeline': 'Timeline for capital raise',
      'yield_on_cost': 'Yield on cost percentage'
    };
    return descriptions[field] || `Weight for ${field}`;
  };

  const getDealsV2Weight = (field: string): number => {
    return dealsV2Weights[field] || 0.1;
  };

  const updateDealsV2Weight = (field: string, weight: number) => {
    setDealsV2Weights(prev => ({
      ...prev,
      [field]: weight
    }));
  };

  const getDealsV2WeightsTotal = (): number => {
    return Object.values(dealsV2Weights).reduce((sum: number, weight: number) => sum + weight, 0);
  };

  const saveDealsV2Weights = async () => {
    try {
      // Save DealsV2 weights logic here
      console.log('Saving DealsV2 weights:', dealsV2Weights);
      // You can implement the actual save logic based on your API
    } catch (error: any) {
      setCapitalPositionWeightsError(error.message || "Unknown error");
    }
  };

  const getDebtEquityFieldDescription = (field: string): string => {
    const descriptions: {[key: string]: string} = {
      'loan_to_value': 'Loan to value ratio matching',
      'loan_to_cost': 'Loan to cost ratio matching',
      'debt_service_coverage': 'Debt service coverage ratio',
      'interest_rate': 'Interest rate and pricing matching',
      'loan_term': 'Loan term and duration matching',
      'amortization': 'Amortization schedule matching',
      'target_return': 'Target return and yield matching',
      'equity_multiple': 'Equity multiple expectations',
      'cash_on_cash': 'Cash on cash return matching',
      'preferred_return': 'Preferred return requirements',
      'hold_period_years': 'Investment hold period in years',
      'ownership_requirement': 'Ownership and control requirements',
      'attachment_point': 'Attachment point for mezzanine debt',
      'max_leverage_tolerance': 'Maximum leverage tolerance'
    };
    return descriptions[field] || `Weight for ${field}`;
  };

  const getDebtEquityWeight = (field: string): number => {
    return debtEquityWeights[field] || 0.1;
  };

  const updateDebtEquityWeight = (field: string, weight: number) => {
    setDebtEquityWeights(prev => ({
      ...prev,
      [field]: weight
    }));
  };

  const getDebtEquityWeightsTotal = (): number => {
    return Object.values(debtEquityWeights).reduce((sum: number, weight: number) => sum + weight, 0);
  };

  const saveDebtEquityWeights = async () => {
    try {
      // Save Debt & Equity weights logic here
      console.log('Saving Debt & Equity weights:', debtEquityWeights);
      // You can implement the actual save logic based on your API
    } catch (error: any) {
      setCapitalPositionWeightsError(error.message || "Unknown error");
    }
  };

  useEffect(() => {
    if (activeTab === "gmail") {
      loadGmailAccounts();
    }
    if (activeTab === "fireflies") {
      loadFirefliesAccounts();
    }
    if (activeTab === "field_weights") {
      loadFieldWeights();
    }
    if (activeTab === "capital_position_weights") {
      loadCapitalPositionWeights();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab]);

  const handleAddGmail = async () => {
    const email = newGmailEmail.trim();
    if (!email) return;
    setLoading(true);
    try {
      await addGmailAccount(email);
      setNewGmailEmail("");
      await loadGmailAccounts();
    } catch (e) {
      // Optionally show error
    }
    setLoading(false);
  };

  // Fireflies handlers
  const loadFirefliesAccounts = async () => {
    setLoading(true);
    const accounts = await fetchFirefliesAccounts();
    setFirefliesAccounts(accounts);
    setLoading(false);
  };

  const handleAddFireflies = async () => {
    const name = newFirefliesName.trim();
    const apiKey = newFirefliesApiKey.trim();
    if (!name || !apiKey) return;
    setLoading(true);
    try {
      await addFirefliesAccount(name, apiKey);
      setNewFirefliesName("");
      setNewFirefliesApiKey("");
      await loadFirefliesAccounts();
    } catch (e) {
      // Optionally show error
    }
    setLoading(false);
  };

  const handleRemoveFireflies = async (id: string) => {
    setLoading(true);
    try {
      await removeFirefliesAccount(id);
      await loadFirefliesAccounts();
    } catch (e) {
      // Optionally show error
    }
    setLoading(false);
  };

  const handleExpandFirefliesAccount = async (accountId: string) => {
    if (expandedFirefliesAccount === accountId) {
      setExpandedFirefliesAccount(null);
    } else {
      setExpandedFirefliesAccount(accountId);
      if (!firefliesTranscripts[accountId]) {
        const result = await fetchFirefliesTranscripts(accountId);
        setFirefliesTranscripts(prev => ({
          ...prev,
          [accountId]: result.transcripts
        }));
      }
    }
  };

  const handleRemoveGmail = async (id: string) => {
    setLoading(true);
    try {
      await removeGmailAccount(id);
      await loadGmailAccounts();
    } catch (e) {
      // Optionally show error
    }
    setLoading(false);
  };

  const handleExpandAccount = async (accountId: string) => {
    if (expandedAccount === accountId) {
      setExpandedAccount(null);
      setExpandedThread(null);
      return;
    }
    setExpandedAccount(accountId);
    setExpandedThread(null);
    if (!accountThreads[accountId]) {
      const threads = await fetchThreads(accountId);
      setAccountThreads((prev) => ({ ...prev, [accountId]: threads }));
    }
  };

  const handleExpandThread = async (threadId: string) => {
    if (expandedThread === threadId) {
      setExpandedThread(null);
      return;
    }
    setExpandedThread(threadId);
    if (!threadMessages[threadId]) {
      const messages = await fetchMessages(threadId);
      setThreadMessages((prev) => ({ ...prev, [threadId]: messages }));
    }
  };

  // Handle weight edit with budget validation
  const handleWeightChange = (field: string, value: string) => {
    const num = parseFloat(value);
    if (isNaN(num) || num < 0) return;
    
    // Calculate what the new total would be
    const tempWeights = { ...editingWeights, [field]: num };
    const nonCapitalFields = fieldWeights.filter(fw => fw.field_name !== 'capital_position');
    let newTotal = 0;
    
    nonCapitalFields.forEach(fw => {
      const fieldName = fw.field_name;
      const weight = tempWeights[fieldName] !== undefined ? tempWeights[fieldName] : fw.weight;
      newTotal += Number(weight);
    });
    
    // Allow the change even if it exceeds 100% (for user feedback), but prevent saving
    setEditingWeights((prev) => ({ ...prev, [field]: num }));
    setBudgetExceeded(newTotal > 1.0);
  };

  // Save all weights at once (batch update)
  const handleSaveAllWeights = async () => {
    const budget = calculateWeightBudget();
    if (budget.exceeded) {
      alert(`Cannot save: Total weights (${budget.percentage.toFixed(1)}%) exceed 100%. Please adjust weights to total exactly 100%.`);
      return;
    }
    
    if (!budget.isExactly100) {
      alert(`Cannot save: Total weights must equal exactly 100%. Current total: ${budget.percentage.toFixed(1)}%`);
      return;
    }

    setSavingField("all");
    try {
      // Update all edited weights
      for (const [field, weight] of Object.entries(editingWeights)) {
        const res = await fetch("/api/dashboard/configuration/field-weights", {
          method: "POST", 
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ field_name: field, weight }),
        });
        if (!res.ok) throw new Error(`Failed to save weight for ${field}`);
      }
      
      await loadFieldWeights();
      setEditingWeights({});
      setBudgetExceeded(false);
    } catch (e: any) {
      alert(`Error saving weights: ${e.message}`);
    }
    setSavingField(null);
  };

  // Reset all changes
  const handleResetWeights = () => {
    setEditingWeights({});
    setBudgetExceeded(false);
  };

  // Define known account emails and names (customize as needed)
  const userEmails = gmailAccounts.map(acc => acc.email.toLowerCase());

  return (
    <div className="max-w-2xl mx-auto p-8">
      <h1 className="text-2xl font-bold mb-6">Configuration</h1>
      <div className="flex border-b mb-6">
        {TABS.map((tab) => (
          <button
            key={tab.key}
            className={`px-4 py-2 -mb-px border-b-2 font-medium focus:outline-none ${
              activeTab === tab.key
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-blue-500"
            }`}
            onClick={() => setActiveTab(tab.key)}
          >
            {tab.label}
          </button>
        ))}
      </div>
      {activeTab === "gmail" && (
        <div>
          <h2 className="text-xl font-semibold mb-4">Gmail Accounts</h2>
          <ul className="mb-4 space-y-4">
            {gmailAccounts.length === 0 && (
              <li className="text-gray-500">No Gmail accounts configured.</li>
            )}
            {gmailAccounts.map((account) => (
              <li key={account.id} className="">
                <div className="flex items-center justify-between py-2 px-3 bg-white rounded shadow-sm border border-gray-200">
                  <span
                    className="font-medium cursor-pointer text-lg"
                    onClick={() => handleExpandAccount(account.id)}
                  >
                    {expandedAccount === account.id ? "▼" : "▶"} {account.email}
                  </span>
                  <span className="text-xs text-gray-400 ml-2">
                    {account.lastSync ? `Last sync: ${account.lastSync}` : ""}
                  </span>
                  <button
                    className="ml-4 text-red-500 hover:underline text-sm"
                    onClick={() => handleRemoveGmail(account.id)}
                    disabled={loading}
                  >
                    Remove
                  </button>
                  {/* Export Inactive Contacts Button and Mode Dropdown */}
                  <div className="ml-4 flex items-center space-x-2">
                    <select
                      className="border rounded px-2 py-1 text-sm"
                      value={account.exportMode || "all"}
                      onChange={(e) => {
                        setGmailAccounts((prev) =>
                          prev.map((a) =>
                            a.id === account.id
                              ? { ...a, exportMode: e.target.value }
                              : a
                          )
                        );
                      }}
                    >
                      <option value="all">All</option>
                      <option value="sent">Sent</option>
                      <option value="received">Received</option>
                    </select>
                    <button
                      className="bg-green-500 text-white px-3 py-1 rounded text-sm shadow hover:bg-green-600"
                      onClick={async () => {
                        const mode = account.exportMode || "all";
                        const res = await fetch(
                          `/api/email/inactive-contacts/${account.id}?mode=${mode}`
                        );
                        if (!res.ok) {
                          alert("Failed to export inactive contacts");
                          return;
                        }
                        const blob = await res.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement("a");
                        a.href = url;
                        a.download = `inactive_contacts_${account.email}_${mode}.csv`;
                        document.body.appendChild(a);
                        a.click();
                        a.remove();
                        window.URL.revokeObjectURL(url);
                      }}
                    >
                      Export Inactive Contacts
                    </button>
                  </div>
                </div>
                {expandedAccount === account.id && (
                  <div className="ml-4 mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <h3 className="font-semibold mb-2 text-base flex items-center justify-between">
                      Threads
                      <button className="ml-2 px-2 py-1 rounded bg-gray-200 text-xs" onClick={() => setMessageSortOrder(messageSortOrder === 'asc' ? 'desc' : 'asc')}>
                        Sort: {messageSortOrder === 'asc' ? 'Oldest First' : 'Newest First'}
                      </button>
                    </h3>
                    <ul className="space-y-2">
                      {(accountThreads[account.id] || []).length === 0 && (
                        <li className="text-gray-400">No threads found.</li>
                      )}
                      {(accountThreads[account.id] || []).map((thread) => (
                        <li key={thread.id} className="">
                          <div className="flex items-center">
                            <span className="cursor-pointer text-blue-600 font-medium text-base" onClick={() => handleExpandThread(thread.id)}>
                              {expandedThread === thread.id ? "▼" : "▶"} {thread.subject || "(No Subject)"}
                            </span>
                            <span className="text-xs text-gray-400 ml-2">{new Date(thread.created_at).toLocaleString()}</span>
                          </div>
                          {expandedThread === thread.id && (
                            <div className="ml-4 mt-2 p-3 bg-white rounded border border-gray-100">
                              <h4 className="font-semibold mb-2 text-sm">Messages</h4>
                              <ul className="space-y-3">
                                {[...(threadMessages[thread.id] || [])]
                                  .sort((a, b) => {
                                    const aTime = new Date(a.sent_at || a.created_at).getTime();
                                    const bTime = new Date(b.sent_at || b.created_at).getTime();
                                    return messageSortOrder === 'asc' ? aTime - bTime : bTime - aTime;
                                  })
                                  .map((msg) => {
                                    const isSent = userEmails.includes((msg.sender || '').toLowerCase());
                                    const senderEmail = msg.sender;
                                    const recipientEmails = (msg.recipients || []).join(', ');
                                    return (
                                      <li key={msg.id} className={`mb-2 p-3 border rounded flex flex-col sm:flex-row gap-4 ${isSent ? 'bg-blue-50' : 'bg-green-50'}`}>
                                        <div className="flex-shrink-0 flex flex-col items-center">
                                          <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center mb-1">
                                            <span className="text-xs font-bold text-gray-700">{(isSent ? senderEmail : recipientEmails)?.[0]?.toUpperCase() || '?'}</span>
                                          </div>
                                          <span className={`text-xs px-2 py-0.5 rounded ${isSent ? 'bg-blue-600 text-white' : 'bg-green-600 text-white'}`}>{isSent ? `Sent by ${senderEmail}` : `Received by ${senderEmail}`}</span>
                                        </div>
                                        <div className="flex-1 min-w-0">
                                          <div className="flex flex-wrap gap-2 text-xs text-gray-500 mb-1">
                                            <span>From: <span className="font-medium text-gray-700">{senderEmail}</span></span>
                                            <span>To: <span className="font-medium text-gray-700">{recipientEmails}</span></span>
                                            <span>Date: <span className="font-medium text-gray-700">{new Date(msg.sent_at || msg.created_at).toLocaleString()}</span></span>
                                          </div>
                                          <div className="font-semibold mb-1 text-base">{msg.subject}</div>
                                          <div className="text-sm mt-2 prose max-w-none" dangerouslySetInnerHTML={{ __html: msg.body }} />
                                        </div>
                                      </li>
                                    );
                                  })}
                              </ul>
                            </div>
                          )}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </li>
            ))}
          </ul>
          <div className="flex items-center mt-4">
            <input
              type="email"
              placeholder="Add Gmail account email"
              className="border rounded px-3 py-2 mr-3 flex-1 shadow-sm"
              value={newGmailEmail}
              onChange={(e) => setNewGmailEmail(e.target.value)}
              disabled={loading}
            />
            <button
              className="bg-blue-500 text-white px-4 py-2 rounded shadow disabled:opacity-50"
              onClick={handleAddGmail}
              disabled={loading || !newGmailEmail.trim()}
            >
              Add
            </button>
          </div>
        </div>
      )}
      {activeTab === "fireflies" && (
        <div>
          <h2 className="text-xl font-semibold mb-4">Fireflies Accounts</h2>
          <ul className="mb-4 space-y-4">
            {firefliesAccounts.length === 0 && (
              <li className="text-gray-500">No Fireflies accounts configured.</li>
            )}
            {firefliesAccounts.map((account) => (
              <li key={account.id} className="">
                <div className="flex items-center justify-between py-2 px-3 bg-white rounded shadow-sm border border-gray-200">
                  <span
                    className="font-medium cursor-pointer text-lg"
                    onClick={() => handleExpandFirefliesAccount(account.id)}
                  >
                    {expandedFirefliesAccount === account.id ? "▼" : "▶"} {account.name}
                  </span>
                  <div className="flex items-center space-x-4">
                    <span className={`text-xs px-2 py-1 rounded ${
                      account.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {account.status}
                    </span>
                    <span className="text-xs text-gray-400">
                      {account.lastSync ? `Last sync: ${account.lastSync}` : "Never synced"}
                    </span>
                    {account.errorCount && account.errorCount > 0 && (
                      <span className="text-xs text-red-500">
                        Errors: {account.errorCount}
                      </span>
                    )}
                    <button
                      className="ml-4 text-red-500 hover:underline text-sm"
                      onClick={() => handleRemoveFireflies(account.id)}
                      disabled={loading}
                    >
                      Remove
                    </button>
                  </div>
                </div>
                {expandedFirefliesAccount === account.id && (
                  <div className="ml-4 mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    <h3 className="font-semibold mb-2 text-base">Transcripts</h3>
                    {account.lastError && (
                      <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                        <strong>Last Error:</strong> {account.lastError}
                      </div>
                    )}
                    <ul className="space-y-2">
                      {(firefliesTranscripts[account.id] || []).length === 0 && (
                        <li className="text-gray-400">No transcripts found.</li>
                      )}
                      {(firefliesTranscripts[account.id] || []).map((transcript) => (
                        <li key={transcript.id} className="p-3 bg-white rounded border border-gray-100">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h4 className="font-semibold text-base mb-1">{transcript.title}</h4>
                              <div className="flex flex-wrap gap-2 text-xs text-gray-500 mb-2">
                                <span>Date: <span className="font-medium text-gray-700">
                                  {new Date(transcript.meeting_date).toLocaleString()}
                                </span></span>
                                <span>Duration: <span className="font-medium text-gray-700">
                                  {transcript.duration}m
                                </span></span>
                                <span>Participants: <span className="font-medium text-gray-700">
                                  {transcript.participants.length}
                                </span></span>
                              </div>
                              <div className="text-sm text-gray-600 mb-2">
                                <strong>Participants:</strong>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {transcript.participants.map((participant: string, index: number) => (
                                    <span 
                                      key={index} 
                                      className="inline-block px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                                    >
                                      {participant}
                                    </span>
                                  ))}
                                </div>
                              </div>
                              <div className="text-sm text-gray-700 max-h-64 overflow-y-auto border border-gray-200 rounded p-3 bg-white">
                                <div className="flex items-center justify-between mb-2">
                                  <strong>Transcript:</strong>
                                  <span className="text-xs text-gray-500">
                                    {transcript.transcript_text ? 
                                      `${Math.ceil(transcript.transcript_text.length / 1000)}k characters` : 
                                      'No content'
                                    }
                                  </span>
                                </div>
                                <div 
                                  className="mt-1 whitespace-pre-wrap text-xs leading-relaxed font-mono bg-gray-50 p-2 rounded border"
                                  style={{ 
                                    maxHeight: '300px',
                                    overflowY: 'auto'
                                  }}
                                >
                                  {transcript.sentences && transcript.sentences.length > 0 ? (
                                    transcript.sentences.map((sentence: any, index: number) => (
                                      <div key={index} className="mb-2 p-2 bg-white rounded border-l-4 border-blue-200">
                                        <div className="font-semibold text-blue-700 mb-1">
                                          {sentence.speaker_name || 'Unknown Speaker'}:
                                        </div>
                                        <div className="text-gray-800">
                                          {sentence.text || sentence.raw_text || ''}
                                        </div>
                                      </div>
                                    ))
                                  ) : transcript.transcript_text ? (
                                    transcript.transcript_text.split('\n\n').map((paragraph: string, index: number) => (
                                      <div key={index} className="mb-2 p-2 bg-white rounded border-l-4 border-blue-200">
                                        {paragraph}
                                      </div>
                                    ))
                                  ) : (
                                    <span className="text-gray-500 italic">No transcript content available</span>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </li>
            ))}
          </ul>
          <div className="flex items-center space-x-3 mt-4">
            <input
              type="text"
              placeholder="Account name"
              className="border rounded px-3 py-2 flex-1 shadow-sm"
              value={newFirefliesName}
              onChange={(e) => setNewFirefliesName(e.target.value)}
              disabled={loading}
            />
            <input
              type="password"
              placeholder="API key"
              className="border rounded px-3 py-2 flex-1 shadow-sm"
              value={newFirefliesApiKey}
              onChange={(e) => setNewFirefliesApiKey(e.target.value)}
              disabled={loading}
            />
            <button
              className="bg-blue-500 text-white px-4 py-2 rounded shadow disabled:opacity-50"
              onClick={handleAddFireflies}
              disabled={loading || !newFirefliesName.trim() || !newFirefliesApiKey.trim()}
            >
              Add
            </button>
          </div>
        </div>
      )}
      {activeTab === "field_weights" && (
        <div>
          <h2 className="text-xl font-semibold mb-4">
            Investment Criteria Field Weights
          </h2>
          
          {fieldWeightsLoading ? (
            <div>Loading...</div>
          ) : fieldWeightsError ? (
            <div className="text-red-500">{fieldWeightsError}</div>
          ) : (
            <div className="space-y-4">
              {/* Budget Display */}
              <div className={`p-4 rounded-lg border-2 ${
                weightBudget.exceeded 
                  ? 'bg-red-50 border-red-200' 
                  : weightBudget.nearLimit 
                    ? 'bg-yellow-50 border-yellow-200'
                    : weightBudget.isExactly100 
                      ? 'bg-green-50 border-green-200'
                      : 'bg-blue-50 border-blue-200'
              }`}>
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold text-lg">Weight Budget</h3>
                    <p className="text-sm text-gray-600">
                      Total weights must equal exactly 100% (excluding Capital Position binary gate)
                    </p>
                  </div>
                  <div className="text-right">
                    <div className={`text-2xl font-bold ${
                      weightBudget.exceeded 
                        ? 'text-red-600' 
                        : weightBudget.nearLimit 
                          ? 'text-yellow-600'
                          : weightBudget.isExactly100 
                            ? 'text-green-600'
                            : 'text-blue-600'
                    }`}>
                      {weightBudget.percentage.toFixed(1)}%
                    </div>
                    <div className="text-sm text-gray-500">
                      {weightBudget.exceeded ? (
                        <span className="text-red-600 font-medium">
                          Over by {(weightBudget.percentage - 100).toFixed(1)}%
                        </span>
                      ) : weightBudget.isExactly100 ? (
                        <span className="text-green-600 font-medium">Perfect!</span>
                      ) : (
                        <span>
                          {weightBudget.remaining.toFixed(2)} remaining ({(100 - weightBudget.percentage).toFixed(1)}%)
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                
                {/* Progress Bar */}
                <div className="mt-3">
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div 
                      className={`h-3 rounded-full transition-all duration-300 ${
                        weightBudget.exceeded 
                          ? 'bg-red-500' 
                          : weightBudget.nearLimit 
                            ? 'bg-yellow-500'
                            : weightBudget.isExactly100 
                              ? 'bg-green-500'
                              : 'bg-blue-500'
                      }`}
                      style={{ 
                        width: `${Math.min(weightBudget.percentage, 100)}%`
                      }}
                    />
                    {weightBudget.exceeded && (
                      <div 
                        className="h-3 bg-red-300 rounded-full -mt-3"
                        style={{ 
                          width: `${Math.min(weightBudget.percentage - 100, 20)}%`,
                          marginLeft: '100%'
                        }}
                      />
                    )}
                  </div>
                </div>
              </div>

              {/* Control Buttons */}
              <div className="flex gap-3">
                <button
                  className={`px-6 py-2 rounded font-medium transition-colors ${
                    Object.keys(editingWeights).length === 0 || weightBudget.exceeded || !weightBudget.isExactly100
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-green-600 hover:bg-green-700 text-white'
                  }`}
                  onClick={handleSaveAllWeights}
                  disabled={
                    Object.keys(editingWeights).length === 0 || 
                    weightBudget.exceeded || 
                    !weightBudget.isExactly100 ||
                    savingField === "all"
                  }
                >
                  {savingField === "all" ? "Saving..." : "Save All Changes"}
                </button>
                
                <button
                  className="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded font-medium transition-colors"
                  onClick={handleResetWeights}
                  disabled={Object.keys(editingWeights).length === 0}
                >
                  Reset Changes
                </button>
              </div>

              {/* Weights Table */}
              <div className="overflow-x-auto">
                <table className="min-w-full border rounded bg-white">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="px-4 py-3 border-b text-left font-semibold">Field</th>
                      <th className="px-4 py-3 border-b text-left font-semibold">Description</th>
                      <th className="px-4 py-3 border-b text-left font-semibold">Weight (%)</th>
                      <th className="px-4 py-3 border-b text-left font-semibold">Raw Value</th>
                    </tr>
                  </thead>
                  <tbody>
                    {fieldWeights
                      .sort((a, b) => {
                        // Capital position first, then by weight desc
                        if (a.field_name === 'capital_position') return -1;
                        if (b.field_name === 'capital_position') return 1;
                        return Number(b.weight) - Number(a.weight);
                      })
                      .map((fw, index) => {
                        const isCapitalPosition = fw.field_name === 'capital_position';
                        const currentWeight = editingWeights[fw.field_name] !== undefined 
                          ? editingWeights[fw.field_name] 
                          : Number(fw.weight);
                        const hasChanged = editingWeights[fw.field_name] !== undefined;
                        
                        return (
                          <tr key={fw.field_name} className={`${
                            index % 2 === 0 ? 'bg-white' : 'bg-gray-25'
                          } ${isCapitalPosition ? 'bg-blue-50' : ''}`}>
                            <td className="px-4 py-3 border-b">
                              <div className="flex items-center gap-2">
                                <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                                  {fw.field_name}
                                </code>
                                {isCapitalPosition && (
                                  <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                    Binary Gate
                                  </span>
                                )}
                                {hasChanged && !isCapitalPosition && (
                                  <span className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded">
                                    Modified
                                  </span>
                                )}
                              </div>
                            </td>
                            <td className="px-4 py-3 border-b text-sm text-gray-600">
                              {fw.description}
                            </td>
                            <td className="px-4 py-3 border-b">
                              {isCapitalPosition ? (
                                <span className="text-sm text-blue-600 font-medium">
                                  Binary Gate (Not counted)
                                </span>
                              ) : (
                                <span className={`text-lg font-semibold ${
                                  hasChanged ? 'text-orange-600' : 'text-gray-800'
                                }`}>
                                  {(currentWeight * 100).toFixed(1)}%
                                </span>
                              )}
                            </td>
                            <td className="px-4 py-3 border-b">
                              <input
                                type="number"
                                min={0}
                                max={isCapitalPosition ? 1 : undefined}
                                step={0.01}
                                value={currentWeight.toFixed(3)}
                                onChange={(e) =>
                                  handleWeightChange(fw.field_name, e.target.value)
                                }
                                className={`border rounded px-3 py-2 w-24 text-sm ${
                                  hasChanged ? 'border-orange-400 bg-orange-50' : 'border-gray-300'
                                } ${isCapitalPosition ? 'bg-blue-50 border-blue-200' : ''}`}
                                disabled={savingField === "all" || isCapitalPosition}
                                readOnly={isCapitalPosition}
                              />
                            </td>
                          </tr>
                        );
                      })}
                  </tbody>
                </table>
              </div>
            </div>
          )}
          
          <div className="text-sm text-gray-600 mt-4 p-3 bg-gray-50 rounded">
            <h4 className="font-medium mb-2">How It Works:</h4>
            <ul className="text-xs space-y-1 list-disc list-inside">
              <li><strong>Capital Position</strong> acts as a binary gate - deals/contacts must have matching capital positions to be scored</li>
              <li><strong>All other weights</strong> must sum to exactly 100% and are used for scoring calculations</li>
              <li>Higher weights mean more importance in the matching algorithm</li>
              <li>Changes are only saved when the total equals 100%</li>
            </ul>
          </div>
        </div>
      )}

            {/* Capital Position Weights Tab */}
      {activeTab === "capital_position_weights" && (
        <div>
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-2">Enhanced Capital Position Weights</h2>
            <p className="text-gray-600">
              Manage field weights for deal matching with COMMON defaults and position-specific overrides.
            </p>
          </div>

          {/* Use the enhanced component */}
          <EnhancedCapitalPositionWeights />
        </div>
      )}

      {/* Add more tab content here as needed */}
    </div>
  );
};

export default ConfigurationPage;
