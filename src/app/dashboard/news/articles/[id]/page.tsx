"use client";

import ArticleDetail from "@/components/dashboard/news/ArticleDetail";

interface PageProps {
  params: {
    id: string;
  };
}

export default function ArticlePage({ params }: PageProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <main className="w-full px-6 py-6">
        {/* Header */}
        <header className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Article Details
            </h1>
            <p className="text-sm text-gray-500 mt-1">
              View and manage article content, extraction data, and processing
            </p>
          </div>
        </header>

        {/* Article Detail Component */}
        <ArticleDetail id={params.id} />
      </main>
    </div>
  );
}
