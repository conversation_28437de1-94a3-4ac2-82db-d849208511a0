import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const client = await pool.connect()

    try {
      const query = `
        SELECT
          DATE(created_at) as date,
          COUNT(*) as articles,
          COUNT(CASE WHEN extraction_status = 'completed' THEN 1 END) as enriched,
          ROUND(
            (COUNT(CASE WHEN extraction_status = 'completed' THEN 1 END) * 100.0 / COUNT(*)),
            1
          ) as success_rate
        FROM article
        WHERE is_bad_url IS NULL OR is_bad_url != true
          AND created_at >= CURRENT_DATE - INTERVAL '30 days'
        GROUP BY DATE(created_at)
        ORDER BY date DESC
        LIMIT 30
      `

      const result = await client.query(query)

      const trendData = result.rows.map(row => ({
        date: row.date,
        articles: parseInt(row.articles),
        enriched: parseInt(row.enriched),
        successRate: parseFloat(row.success_rate)
      })).reverse() // Reverse to show oldest to newest

      return NextResponse.json(trendData)

    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error fetching trends:', error)
    return NextResponse.json(
      { error: 'Failed to fetch trends' },
      { status: 500 }
    )
  }
}