import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const client = await pool.connect()

    try {
      const query = `
        SELECT
          publication_name as source,
          COUNT(*) as total,
          COUNT(CASE WHEN extraction_status = 'completed' THEN 1 END) as enriched,
          ROUND(
            (COUNT(CASE WHEN extraction_status = 'completed' THEN 1 END) * 100.0 / COUNT(*)),
            1
          ) as success_rate
        FROM article
        WHERE (is_bad_url IS NULL OR is_bad_url != true)
          AND publication_name IS NOT NULL
        GROUP BY publication_name
        HAVING COUNT(*) >= 5  -- Only show sources with at least 5 articles
        ORDER BY success_rate DESC, total DESC
        LIMIT 20
      `

      const result = await client.query(query)

      const sourceMetrics = result.rows.map(row => ({
        source: row.source || 'Unknown',
        total: parseInt(row.total),
        enriched: parseInt(row.enriched),
        successRate: parseFloat(row.success_rate)
      }))

      return NextResponse.json(sourceMetrics)

    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error fetching source metrics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch source metrics' },
      { status: 500 }
    )
  }
}