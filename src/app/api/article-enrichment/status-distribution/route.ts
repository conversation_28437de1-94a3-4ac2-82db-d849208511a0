import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const client = await pool.connect()

    try {
      const query = `
        WITH status_counts AS (
          SELECT
            CASE
              WHEN extraction_status = 'completed' THEN 'Enriched'
              WHEN fetch_status = 'completed' AND extraction_status = 'pending' THEN 'Fetched'
              WHEN fetch_status = 'pending' THEN 'Pending Fetch'
              WHEN fetch_status = 'failed' OR extraction_status = 'failed' THEN 'Failed'
              WHEN fetch_status = 'running' OR extraction_status = 'running' THEN 'Processing'
              ELSE 'Unknown'
            END as status,
            COUNT(*) as count
          FROM article
          WHERE is_bad_url IS NULL OR is_bad_url != true
          GROUP BY
            CASE
              WHEN extraction_status = 'completed' THEN 'Enriched'
              WHEN fetch_status = 'completed' AND extraction_status = 'pending' THEN 'Fetched'
              WHEN fetch_status = 'pending' THEN 'Pending Fetch'
              WHEN fetch_status = 'failed' OR extraction_status = 'failed' THEN 'Failed'
              WHEN fetch_status = 'running' OR extraction_status = 'running' THEN 'Processing'
              ELSE 'Unknown'
            END
        ),
        total_count AS (
          SELECT SUM(count) as total FROM status_counts
        )
        SELECT
          sc.status,
          sc.count,
          ROUND((sc.count * 100.0 / tc.total), 1) as percentage
        FROM status_counts sc
        CROSS JOIN total_count tc
        ORDER BY sc.count DESC
      `

      const result = await client.query(query)

      const statusDistribution = result.rows.map(row => ({
        status: row.status,
        count: parseInt(row.count),
        percentage: parseFloat(row.percentage)
      }))

      return NextResponse.json(statusDistribution)

    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error fetching status distribution:', error)
    return NextResponse.json(
      { error: 'Failed to fetch status distribution' },
      { status: 500 }
    )
  }
}