import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const client = await pool.connect()

    try {
      // Get overall article metrics
      const metricsQuery = `
        SELECT
          COUNT(*) as total_articles,
          COUNT(CASE WHEN fetch_status = 'completed' THEN 1 END) as fetched_articles,
          COUNT(CASE WHEN extraction_status = 'completed' THEN 1 END) as enriched_articles,
          COUNT(CASE WHEN fetch_status = 'failed' OR extraction_status = 'failed' THEN 1 END) as failed_articles,
          COUNT(CASE WHEN fetch_status = 'pending' OR extraction_status = 'pending' THEN 1 END) as pending_articles,
          AVG(CASE WHEN source_confidence IS NOT NULL THEN source_confidence ELSE 0 END) as avg_confidence_score
        FROM article
        WHERE (is_bad_url IS NULL OR is_bad_url != true)
      `

      const metricsResult = await client.query(metricsQuery)
      const metrics = metricsResult.rows[0]

      // Get extraction counts from related tables
      const extractionQuery = `
        SELECT
          (SELECT COUNT(*) FROM article_properties) as properties_extracted,
          (SELECT COUNT(*) FROM article_transactions) as transactions_extracted,
          (SELECT COUNT(*) FROM articles_entities) as entities_extracted,
          (SELECT COUNT(*) FROM article_market_metrics) as market_metrics_extracted
      `

      const extractionResult = await client.query(extractionQuery)
      const extraction = extractionResult.rows[0]

      const response = {
        totalArticles: parseInt(metrics.total_articles) || 0,
        fetchedArticles: parseInt(metrics.fetched_articles) || 0,
        enrichedArticles: parseInt(metrics.enriched_articles) || 0,
        failedArticles: parseInt(metrics.failed_articles) || 0,
        pendingArticles: parseInt(metrics.pending_articles) || 0,
        averageConfidenceScore: parseFloat(metrics.avg_confidence_score) || 0,
        propertiesExtracted: parseInt(extraction.properties_extracted) || 0,
        transactionsExtracted: parseInt(extraction.transactions_extracted) || 0,
        entitiesExtracted: parseInt(extraction.entities_extracted) || 0,
        marketMetricsExtracted: parseInt(extraction.market_metrics_extracted) || 0
      }

      return NextResponse.json(response)

    } finally {
      client.release()
    }
  } catch (error) {
    console.error('Error fetching article enrichment metrics:', error)
    return NextResponse.json(
      { error: 'Failed to fetch metrics' },
      { status: 500 }
    )
  }
}