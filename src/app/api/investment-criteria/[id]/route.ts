import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: criteriaId } = await params

    const criteriaQuery = `
      SELECT 
        ic.*,
        CASE 
          WHEN ic.entity_type LIKE 'Company%' THEN c.company_name
          WHEN ic.entity_type = 'Contact' THEN CONCAT(p.first_name, ' ', p.last_name)
          ELSE ic.entity_id
        END as entity_name,
        CASE 
          WHEN ic.entity_type LIKE 'Company%' THEN c.company_city
          WHEN ic.entity_type = 'Contact' THEN CONCAT(p.contact_city, ', ', p.contact_state)
          ELSE NULL
        END as entity_location,
        CASE 
          WHEN ic.entity_type LIKE 'Company%' THEN c.company_website
          ELSE NULL
        END as entity_website,
        CASE 
          WHEN ic.entity_type LIKE 'Company%' THEN c.industry
          ELSE NULL
        END as entity_industry
      FROM investment_criteria ic
      LEFT JOIN companies c ON ic.entity_type LIKE 'Company%' AND ic.entity_id = c.company_id::text
      LEFT JOIN contacts p ON ic.entity_type = 'Contact' AND ic.entity_id = p.contact_id::text
      WHERE ic.criteria_id = $1 AND ic.is_active = true
    `

    const result = await pool.query(criteriaQuery, [criteriaId])

    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Investment criteria not found' },
        { status: 404 }
      )
    }

    const criteria = result.rows[0]
    
    // Debug: Log the raw criteria data
    console.log('Raw criteria from database:', JSON.stringify(criteria, null, 2))

    // Helper function to convert PostgreSQL array string to JavaScript array
    const parsePostgresArray = (value: any): string[] | null => {
      if (!value || typeof value !== 'string') return null
      // Remove curly braces and split by comma
      const cleanValue = value.replace(/^{|}$/g, '')
      if (!cleanValue) return []
      return cleanValue.split(',').map(item => item.replace(/^"|"$/g, '').trim())
    }

    // Array fields that need conversion from PostgreSQL format
    const arrayFields = [
      'property_types', 'property_sub_categories', 'strategies', 'financial_products',
      'country', 'region', 'state', 'city', 'loan_program', 'loan_type',
      'structured_loan_tranche', 'recourse_loan', 'capital_position', 'loan_type_normalized'
    ]

    // Format the response with better structure
    const formattedCriteria = {
      ...criteria,
      // Format numeric values
      minimum_deal_size: criteria.minimum_deal_size ? Number(criteria.minimum_deal_size) : null,
      maximum_deal_size: criteria.maximum_deal_size ? Number(criteria.maximum_deal_size) : null,
      target_return: criteria.target_return ? Number(criteria.target_return) : null,
      historical_irr: criteria.historical_irr ? Number(criteria.historical_irr) : null,
      historical_em: criteria.historical_em ? Number(criteria.historical_em) : null,
      loan_to_value_min: criteria.loan_to_value_min ? Number(criteria.loan_to_value_min) : null,
      loan_to_value_max: criteria.loan_to_value_max ? Number(criteria.loan_to_value_max) : null,
      loan_to_cost_min: criteria.loan_to_cost_min ? Number(criteria.loan_to_cost_min) : null,
      loan_to_cost_max: criteria.loan_to_cost_max ? Number(criteria.loan_to_cost_max) : null,
      interest_rate: criteria.interest_rate ? Number(criteria.interest_rate) : null,
      interest_rate_sofr: criteria.interest_rate_sofr ? Number(criteria.interest_rate_sofr) : null,
      interest_rate_wsj: criteria.interest_rate_wsj ? Number(criteria.interest_rate_wsj) : null,
      interest_rate_prime: criteria.interest_rate_prime ? Number(criteria.interest_rate_prime) : null,
      // Format array fields
      ...Object.fromEntries(
        arrayFields.map(field => [field, parsePostgresArray(criteria[field])])
      ),
      // Format date fields
      created_at: criteria.created_at ? new Date(criteria.created_at).toISOString() : null,
      updated_at: criteria.updated_at ? new Date(criteria.updated_at).toISOString() : null,
    }

    return NextResponse.json(formattedCriteria)

  } catch (error) {
    console.error('Error fetching investment criteria:', error)
    return NextResponse.json(
      { error: 'Failed to fetch investment criteria' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const criteriaId = parseInt(id)
    const updateData = await request.json()

    if (!criteriaId) {
      return NextResponse.json(
        { error: 'Investment criteria ID is required' },
        { status: 400 }
      )
    }

    // Convert JavaScript arrays to PostgreSQL array format
    const arrayToPostgresArray = (arr: string[] | null | undefined): string | null => {
      if (!arr || arr.length === 0) {
        return null
      }
      
      const escapedValues = arr.map(value => {
        if (typeof value !== 'string') {
          return String(value)
        }
        
        if (value.includes(' ') || value.includes(',') || value.includes('"') || value.includes('\'') || value.includes('{') || value.includes('}')) {
          return `"${value.replace(/"/g, '\\"')}"` 
        }
        
        return value
      })
      
      return `{${escapedValues.join(',')}}`
    }

    // Update the investment criteria central table
    const updateQuery = `
      UPDATE investment_criteria_central 
      SET 
        capital_position = $1,
        minimum_deal_size = $2,
        maximum_deal_size = $3,
        country = $4,
        region = $5,
        state = $6,
        city = $7,
        property_types = $8,
        property_subcategories = $9,
        strategies = $10,
        decision_making_process = $11,
        notes = $12,
        updated_at = NOW()
      WHERE investment_criteria_id = $13
      RETURNING *
    `

    const values = [
      updateData.capital_position || null,
      updateData.minimum_deal_size ? parseFloat(updateData.minimum_deal_size) : null,
      updateData.maximum_deal_size ? parseFloat(updateData.maximum_deal_size) : null,
      arrayToPostgresArray(updateData.country),
      arrayToPostgresArray(updateData.region),
      arrayToPostgresArray(updateData.state),
      arrayToPostgresArray(updateData.city),
      arrayToPostgresArray(updateData.property_types),
      arrayToPostgresArray(updateData.property_subcategories),
      arrayToPostgresArray(updateData.strategies),
      updateData.decision_making_process || null,
      updateData.notes || null,
      criteriaId
    ]

    const result = await pool.query(updateQuery, values)
    
    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Investment criteria not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Investment criteria updated successfully',
      data: result.rows[0]
    })

  } catch (error) {
    console.error('Error updating investment criteria:', error)
    return NextResponse.json(
      { error: 'Failed to update investment criteria', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: criteriaId } = await params
    console.log('DELETE request received for [id] route:', { criteriaId })
    
    // Check if this is actually an entity route (id = "entity")
    if (criteriaId === 'entity') {
      return NextResponse.json(
        { error: 'Invalid route - use /api/investment-criteria/entity/[entityType]/[entityId] for entity-specific operations' },
        { status: 400 }
      )
    }
    
    // Convert to integer and validate
    const criteriaIdInt = parseInt(criteriaId, 10)
    if (isNaN(criteriaIdInt)) {
      return NextResponse.json(
        { error: 'Invalid criteria ID format' },
        { status: 400 }
      )
    }

    console.log('Attempting to delete criteria ID:', criteriaIdInt, 'Type:', typeof criteriaIdInt)

    // First check if the criteria exists and is active
    const checkQuery = `SELECT criteria_id, is_active, entity_type, entity_id FROM investment_criteria WHERE criteria_id = $1`
    const checkResult = await pool.query(checkQuery, [criteriaIdInt])
    
    if (checkResult.rows.length === 0) {
      console.log('Criteria not found in database')
      return NextResponse.json(
        { error: 'Investment criteria not found' },
        { status: 404 }
      )
    }
    
    const criteria = checkResult.rows[0]
    console.log('Found criteria:', criteria)
    


    // Hard delete the criteria
    const deleteQuery = `
      DELETE FROM investment_criteria 
      WHERE criteria_id = $1
      RETURNING criteria_id
    `

    const result = await pool.query(deleteQuery, [criteriaIdInt])
    // console.log('Delete query result:', result.rows.length, 'rows affected')

    if (result.rows.length === 0) {
      console.log('No rows were deleted - criteria may have been deleted by another process')
      return NextResponse.json(
        { error: 'Investment criteria could not be deleted - it may have been deleted by another process' },
        { status: 409 }
      )
    }

    return NextResponse.json({ 
      message: 'Investment criteria deleted successfully',
      criteria_id: result.rows[0].criteria_id 
    })

  } catch (error) {
    console.error('Error deleting investment criteria:', error)
    return NextResponse.json(
      { error: 'Failed to delete investment criteria' },
      { status: 500 }
    )
  }
} 