import { NextRequest, NextResponse } from 'next/server'
import { DataNormalizationService } from '@/lib/services/dataNormalizationService'

interface NormalizeRequest {
  type: 'company' | 'contact' | 'both'
  recordIds?: number[]
  batchSize?: number
}

interface NormalizeResponse {
  success: boolean
  stats: {
    companies?: {
      processed: number
      errors: number
    }
    contacts?: {
      processed: number
      errors: number
    }
  }
  processingTime: number
  error?: string
}

export async function POST(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    const { type, recordIds, batchSize = 100 }: NormalizeRequest = await request.json()

    if (!type || !['company', 'contact', 'both'].includes(type)) {
      return NextResponse.json(
        { error: 'Invalid type. Must be "company", "contact", or "both"' },
        { status: 400 }
      )
    }

    const stats: any = {}

    // Normalize specific records if provided
    if (recordIds && recordIds.length > 0) {
      if (type === 'company' || type === 'both') {
        let processed = 0
        let errors = 0
        
        for (const recordId of recordIds) {
          try {
            await DataNormalizationService.normalizeCompany(recordId)
            processed++
          } catch (error) {
            console.error(`Error normalizing company ${recordId}:`, error)
            errors++
          }
        }
        
        stats.companies = { processed, errors }
      }

      if (type === 'contact' || type === 'both') {
        let processed = 0
        let errors = 0
        
        for (const recordId of recordIds) {
          try {
            await DataNormalizationService.normalizeContact(recordId)
            processed++
          } catch (error) {
            console.error(`Error normalizing contact ${recordId}:`, error)
            errors++
          }
        }
        
        stats.contacts = { processed, errors }
      }
    } else {
      // Normalize all records
      if (type === 'company' || type === 'both') {
        console.log('Starting company normalization...')
        stats.companies = await DataNormalizationService.normalizeAllCompanies(batchSize)
      }

      if (type === 'contact' || type === 'both') {
        console.log('Starting contact normalization...')
        stats.contacts = await DataNormalizationService.normalizeAllContacts(batchSize)
      }
    }

    const processingTime = Date.now() - startTime

    return NextResponse.json({
      success: true,
      stats,
      processingTime
    })

  } catch (error) {
    console.error('Error in data normalization:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        success: false,
        stats: {},
        processingTime: Date.now() - startTime
      },
      { status: 500 }
    )
  }
}

// GET: Check normalization status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // 'company' | 'contact' | 'both'

    const { pool } = await import('@/lib/db')
    const client = await pool.connect()

    try {
      const results: any = {}

      if (!type || type === 'company' || type === 'both') {
        const companyStats = await client.query(`
          SELECT 
            (SELECT COUNT(*) FROM companies) as total_companies,
            (SELECT COUNT(*) FROM company_normalized_data) as normalized_companies,
            (SELECT COUNT(*) FROM company_normalized_data WHERE updated_at > created_at) as updated_companies,
            (SELECT MAX(updated_at) FROM company_normalized_data) as last_normalized
        `)
        
        results.companies = companyStats.rows[0]
      }

      if (!type || type === 'contact' || type === 'both') {
        const contactStats = await client.query(`
          SELECT 
            (SELECT COUNT(*) FROM contacts) as total_contacts,
            (SELECT COUNT(*) FROM contact_normalized_data) as normalized_contacts,
            (SELECT COUNT(*) FROM contact_normalized_data WHERE updated_at > created_at) as updated_contacts,
            (SELECT MAX(updated_at) FROM contact_normalized_data) as last_normalized
        `)
        
        results.contacts = contactStats.rows[0]
      }

      return NextResponse.json({
        success: true,
        stats: results
      })

    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error getting normalization status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
