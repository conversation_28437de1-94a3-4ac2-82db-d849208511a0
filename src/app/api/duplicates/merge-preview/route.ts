import { NextRequest, NextResponse } from 'next/server'
import { DuplicateResolutionService } from '@/lib/services/duplicateResolutionService'

interface MergePreviewRequest {
  primaryId: number
  duplicateId: number
  recordType: 'company' | 'contact'
}

export async function POST(request: NextRequest) {
  try {
    const { primaryId, duplicateId, recordType }: MergePreviewRequest = await request.json()

    if (!primaryId || !duplicateId || !recordType) {
      return NextResponse.json(
        { error: 'primaryId, duplicateId, and recordType are required' },
        { status: 400 }
      )
    }

    if (!['company', 'contact'].includes(recordType)) {
      return NextResponse.json(
        { error: 'recordType must be "company" or "contact"' },
        { status: 400 }
      )
    }

    const preview = await DuplicateResolutionService.generateMergePreview(
      primaryId,
      duplicateId,
      recordType
    )

    return NextResponse.json({
      success: true,
      preview
    })

  } catch (error) {
    console.error('Error generating merge preview:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        success: false
      },
      { status: 500 }
    )
  }
}
