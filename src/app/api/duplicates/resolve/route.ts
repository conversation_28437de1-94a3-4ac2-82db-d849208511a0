import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

interface ResolveDuplicatesRequest {
  duplicateId: number
  action: 'merge' | 'keep_separate' | 'mark_false_positive'
  mergeStrategy?: {
    primaryRecordId: number
    fieldsToMerge: string[]
    customValues?: Record<string, any>
  }
  resolvedBy?: string
  resolutionNotes?: string
}

interface MergeResult {
  success: boolean
  mergedRecordId: number
  deletedRecordId: number
  mergedData: any
}

export async function POST(request: NextRequest) {
  try {
    const { 
      duplicateId, 
      action, 
      mergeStrategy, 
      resolvedBy, 
      resolutionNotes 
    }: ResolveDuplicatesRequest = await request.json()

    if (!duplicateId || !action) {
      return NextResponse.json(
        { error: 'duplicateId and action are required' },
        { status: 400 }
      )
    }

    if (!['merge', 'keep_separate', 'mark_false_positive'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be "merge", "keep_separate", or "mark_false_positive"' },
        { status: 400 }
      )
    }

    const client = await pool.connect()

    try {
      await client.query('BEGIN')

      // Get the duplicate record
      const duplicateResult = await client.query(`
        SELECT * FROM duplicate_records WHERE id = $1
      `, [duplicateId])

      if (duplicateResult.rows.length === 0) {
        await client.query('ROLLBACK')
        return NextResponse.json(
          { error: 'Duplicate record not found' },
          { status: 404 }
        )
      }

      const duplicateRecord = duplicateResult.rows[0]
      let result: any = { success: true }

      switch (action) {
        case 'merge':
          if (!mergeStrategy) {
            await client.query('ROLLBACK')
            return NextResponse.json(
              { error: 'mergeStrategy is required for merge action' },
              { status: 400 }
            )
          }
          result = await performMerge(client, duplicateRecord, mergeStrategy)
          break

        case 'keep_separate':
          result = await markAsSeparate(client, duplicateRecord, resolvedBy, resolutionNotes)
          break

        case 'mark_false_positive':
          result = await markAsFalsePositive(client, duplicateRecord, resolvedBy, resolutionNotes)
          break
      }

      if (result.success) {
        await client.query('COMMIT')
        return NextResponse.json(result)
      } else {
        await client.query('ROLLBACK')
        return NextResponse.json(
          { error: result.error || 'Resolution failed' },
          { status: 500 }
        )
      }

    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }

  } catch (error) {
    console.error('Error resolving duplicate:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function performMerge(
  client: any, 
  duplicateRecord: any, 
  mergeStrategy: any
): Promise<MergeResult> {
  const { record_type, primary_record_id, duplicate_record_id } = duplicateRecord
  const { primaryRecordId, fieldsToMerge, customValues } = mergeStrategy

  // Determine which record to keep and which to merge
  const keepRecordId = primaryRecordId
  const mergeRecordId = keepRecordId === primary_record_id ? duplicate_record_id : primary_record_id

  if (record_type === 'company') {
    return await mergeCompanies(client, keepRecordId, mergeRecordId, fieldsToMerge, customValues)
  } else if (record_type === 'contact') {
    return await mergeContacts(client, keepRecordId, mergeRecordId, fieldsToMerge, customValues)
  } else {
    throw new Error(`Unsupported record type: ${record_type}`)
  }
}

async function mergeCompanies(
  client: any,
  keepId: number,
  mergeId: number,
  fieldsToMerge: string[],
  customValues?: Record<string, any>
): Promise<MergeResult> {
  // Get both company records
  const [keepCompany, mergeCompany] = await Promise.all([
    client.query('SELECT * FROM companies WHERE company_id = $1', [keepId]),
    client.query('SELECT * FROM companies WHERE company_id = $1', [mergeId])
  ])

  if (keepCompany.rows.length === 0 || mergeCompany.rows.length === 0) {
    return { success: false, error: 'One or both companies not found' } as any
  }

  const keepData = keepCompany.rows[0]
  const mergeData = mergeCompany.rows[0]

  // Build update query for the company to keep
  const updateFields: string[] = []
  const updateValues: any[] = []
  let paramIndex = 1

  // Merge specified fields
  for (const field of fieldsToMerge) {
    if (field in mergeData && mergeData[field] && !keepData[field]) {
      updateFields.push(`${field} = $${paramIndex}`)
      updateValues.push(mergeData[field])
      paramIndex++
    }
  }

  // Apply custom values
  if (customValues) {
    for (const [field, value] of Object.entries(customValues)) {
      updateFields.push(`${field} = $${paramIndex}`)
      updateValues.push(value)
      paramIndex++
    }
  }

  // Update the company to keep
  if (updateFields.length > 0) {
    updateValues.push(keepId)
    await client.query(`
      UPDATE companies 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE company_id = $${paramIndex}
    `, updateValues)
  }

  // Update all contacts that reference the merged company
  await client.query(`
    UPDATE contacts 
    SET company_id = $1, updated_at = CURRENT_TIMESTAMP
    WHERE company_id = $2
  `, [keepId, mergeId])

  // Update investment criteria
  await client.query(`
    UPDATE investment_criteria 
    SET entity_id = $1, updated_at = CURRENT_TIMESTAMP
    WHERE entity_type = 'Company' AND entity_id = $2
  `, [keepId.toString(), mergeId.toString()])

  // Delete the merged company
  await client.query('DELETE FROM companies WHERE company_id = $1', [mergeId])

  // Update duplicate record status
  await client.query(`
    UPDATE duplicate_records 
    SET status = 'merged', resolved_at = CURRENT_TIMESTAMP
    WHERE (primary_record_id = $1 AND duplicate_record_id = $2) 
       OR (primary_record_id = $2 AND duplicate_record_id = $1)
       AND record_type = 'company'
  `, [keepId, mergeId])

  // Get the updated company data
  const updatedResult = await client.query('SELECT * FROM companies WHERE company_id = $1', [keepId])

  return {
    success: true,
    mergedRecordId: keepId,
    deletedRecordId: mergeId,
    mergedData: updatedResult.rows[0]
  }
}

async function mergeContacts(
  client: any,
  keepId: number,
  mergeId: number,
  fieldsToMerge: string[],
  customValues?: Record<string, any>
): Promise<MergeResult> {
  // Get both contact records
  const [keepContact, mergeContact] = await Promise.all([
    client.query('SELECT * FROM contacts WHERE contact_id = $1', [keepId]),
    client.query('SELECT * FROM contacts WHERE contact_id = $1', [mergeId])
  ])

  if (keepContact.rows.length === 0 || mergeContact.rows.length === 0) {
    return { success: false, error: 'One or both contacts not found' } as any
  }

  const keepData = keepContact.rows[0]
  const mergeData = mergeContact.rows[0]

  // Build update query for the contact to keep
  const updateFields: string[] = []
  const updateValues: any[] = []
  let paramIndex = 1

  // Merge specified fields
  for (const field of fieldsToMerge) {
    if (field in mergeData && mergeData[field] && !keepData[field]) {
      updateFields.push(`${field} = $${paramIndex}`)
      updateValues.push(mergeData[field])
      paramIndex++
    }
  }

  // Apply custom values
  if (customValues) {
    for (const [field, value] of Object.entries(customValues)) {
      updateFields.push(`${field} = $${paramIndex}`)
      updateValues.push(value)
      paramIndex++
    }
  }

  // Update the contact to keep
  if (updateFields.length > 0) {
    updateValues.push(keepId)
    await client.query(`
      UPDATE contacts 
      SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE contact_id = $${paramIndex}
    `, updateValues)
  }

  // Delete the merged contact
  await client.query('DELETE FROM contacts WHERE contact_id = $1', [mergeId])

  // Update duplicate record status
  await client.query(`
    UPDATE duplicate_records 
    SET status = 'merged', resolved_at = CURRENT_TIMESTAMP
    WHERE (primary_record_id = $1 AND duplicate_record_id = $2) 
       OR (primary_record_id = $2 AND duplicate_record_id = $1)
       AND record_type = 'contact'
  `, [keepId, mergeId])

  // Get the updated contact data
  const updatedResult = await client.query('SELECT * FROM contacts WHERE contact_id = $1', [keepId])

  return {
    success: true,
    mergedRecordId: keepId,
    deletedRecordId: mergeId,
    mergedData: updatedResult.rows[0]
  }
}

async function markAsSeparate(
  client: any,
  duplicateRecord: any,
  resolvedBy?: string,
  resolutionNotes?: string
): Promise<{ success: boolean }> {
  await client.query(`
    UPDATE duplicate_records 
    SET status = 'confirmed', 
        resolved_at = CURRENT_TIMESTAMP,
        resolved_by = $1,
        resolution_notes = $2
    WHERE id = $3
  `, [resolvedBy, resolutionNotes, duplicateRecord.id])

  return { success: true }
}

async function markAsFalsePositive(
  client: any,
  duplicateRecord: any,
  resolvedBy?: string,
  resolutionNotes?: string
): Promise<{ success: boolean }> {
  await client.query(`
    UPDATE duplicate_records 
    SET status = 'false_positive', 
        resolved_at = CURRENT_TIMESTAMP,
        resolved_by = $1,
        resolution_notes = $2
    WHERE id = $3
  `, [resolvedBy, resolutionNotes, duplicateRecord.id])

  return { success: true }
}
