import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";
import { calculateV2MatchScore } from "../../_lib/matching-utils-v2";

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ contactId: string }> }
) {
  try {
    const { contactId } = await context.params;
    
    // Parse query parameters
    const url = new URL(req.url);
    const showAllMatches = url.searchParams.get('show_all') === 'true';
    const minScoreThreshold = showAllMatches ? 0 : 50; // Default to 50% if not showing all
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    console.log(`V2: Fetching matching deals for contact ${contactId} using central investment criteria tables`);

    // Get contact data and investment criteria using the same comprehensive approach as contacts-for-deal
    const contactQuery = `
      WITH matching_contact_criteria AS (
        SELECT DISTINCT 
          icc.investment_criteria_id,
          icc.entity_type,
          icc.entity_id,
          icc.capital_position,
          icc.minimum_deal_size,
          icc.maximum_deal_size,
          icc.property_types,
          icc.strategies,
          -- ICC Location (Highest Priority)
          icc.region as icc_region,
          icc.state as icc_state,
          icc.city as icc_city,
          icc.country as icc_country,
          -- Debt-specific fields (only populated for debt positions)
          icd.loan_to_value_min,
          icd.loan_to_value_max,
          icd.loan_to_cost_min,
          icd.loan_to_cost_max,
          icd.min_loan_term,
          icd.max_loan_term,
          icd.min_loan_dscr,
          icd.max_loan_dscr,
          icd.loan_interest_rate,
          icd.loan_type,
          icd.amortization,
          icd.recourse_loan,
          -- Equity-specific fields (only populated for equity positions)
          ice.target_return,
          ice.min_hold_period_years,
          ice.max_hold_period_years,
          ice.minimum_equity_multiple,
          ice.minimum_yield_on_cost,
          ice.ownership_requirement,
          ice.attachment_point,
          ice.max_leverage_tolerance,
          -- Determine if this is debt or equity based on capital position
          CASE 
            WHEN icc.capital_position = ANY(ARRAY['Senior Debt', 'Mezzanine', 'Bridge Loan', 'Construction Loan', 'Permanent Loan', 'Refinance Loan']) THEN 'debt'
            WHEN icc.capital_position = ANY(ARRAY['Common Equity', 'Preferred Equity', 'General Partner (GP)', 'Limited Partner (LP)', 'Joint Venture (JV)', 'Co-GP']) THEN 'equity'
            ELSE 'unknown'
          END as position_type,
          'Contact' as criteria_source
        FROM investment_criteria_central icc
        LEFT JOIN investment_criteria_debt icd ON icc.investment_criteria_id = icd.investment_criteria_id
        LEFT JOIN investment_criteria_equity ice ON icc.investment_criteria_id = ice.investment_criteria_id
        WHERE icc.entity_type = 'contact' 
          AND icc.entity_id = $1
      ),
      matching_company_criteria AS (
        SELECT DISTINCT 
          icc.investment_criteria_id,
          icc.entity_type,
          icc.entity_id,
          icc.capital_position,
          icc.minimum_deal_size,
          icc.maximum_deal_size,
          icc.property_types,
          icc.strategies,
          -- ICC Location (Highest Priority)
          icc.region as icc_region,
          icc.state as icc_state,
          icc.city as icc_city,
          icc.country as icc_country,
          -- Debt-specific fields (only populated for debt positions)
          icd.loan_to_value_min,
          icd.loan_to_value_max,
          icd.loan_to_cost_min,
          icd.loan_to_cost_max,
          icd.min_loan_term,
          icd.max_loan_term,
          icd.min_loan_dscr,
          icd.max_loan_dscr,
          icd.loan_interest_rate,
          icd.loan_type,
          icd.amortization,
          icd.recourse_loan,
          -- Equity-specific fields (only populated for equity positions)
          ice.target_return,
          ice.min_hold_period_years,
          ice.max_hold_period_years,
          ice.minimum_equity_multiple,
          ice.minimum_yield_on_cost,
          ice.ownership_requirement,
          ice.attachment_point,
          ice.max_leverage_tolerance,
          -- Determine if this is debt or equity based on capital position
          CASE 
            WHEN icc.capital_position = ANY(ARRAY['Senior Debt', 'Mezzanine', 'Bridge Loan', 'Construction Loan', 'Permanent Loan', 'Refinance Loan']) THEN 'debt'
            WHEN icc.capital_position = ANY(ARRAY['Common Equity', 'Preferred Equity', 'General Partner (GP)', 'Limited Partner (LP)', 'Joint Venture (JV)', 'Co-GP']) THEN 'equity'
            ELSE 'unknown'
          END as position_type,
          'Company' as criteria_source
        FROM investment_criteria_central icc
        LEFT JOIN investment_criteria_debt icd ON icc.investment_criteria_id = icd.investment_criteria_id
        LEFT JOIN investment_criteria_equity ice ON icc.investment_criteria_id = ice.investment_criteria_id
        INNER JOIN contacts ct ON icc.entity_id = ct.company_id
        WHERE icc.entity_type = 'company'
          AND ct.contact_id = $1
      ),
      all_matching_criteria AS (
        SELECT * FROM matching_contact_criteria
        UNION ALL
        SELECT * FROM matching_company_criteria
      )
      SELECT DISTINCT
        c.contact_id,
        c.company_id,
        c.first_name,
        c.last_name,
        c.email,
        c.title AS job_title,
        c.phone_number,
        c.linkedin_url,
        c.updated_at AS contact_updated_at,
        c.created_at AS contact_created_at,
        comp.company_name,
        comp.company_city,
        comp.company_state,
        comp.company_website,
        comp.industry,
        icc.investment_criteria_id,
        icc.criteria_source,
        icc.capital_position,
        icc.minimum_deal_size,
        icc.maximum_deal_size,
        icc.property_types,
        icc.strategies,
        -- ICC Location (Highest Priority)
        icc_region,
        icc_state,
        icc_city,
        icc_country,
        -- Contact/Company Location (Secondary Priority)
        c.contact_city,
        c.contact_state,
        c.contact_country,
        c.region as contact_region,
        comp.company_city as comp_city,
        comp.company_state as comp_state,
        comp.company_country as comp_country,
        -- Debt-specific fields
        loan_to_value_min,
        loan_to_value_max,
        loan_to_cost_min,
        loan_to_cost_max,
        min_loan_term,
        max_loan_term,
        min_loan_dscr,
        max_loan_dscr,
        loan_interest_rate,
        loan_type,
        amortization,
        recourse_loan,
        -- Equity-specific fields
        target_return,
        min_hold_period_years,
        max_hold_period_years,
        minimum_equity_multiple,
        minimum_yield_on_cost,
        ownership_requirement,
        attachment_point,
        max_leverage_tolerance,
        -- Position type for proper matching
        position_type
      FROM contacts c
      INNER JOIN all_matching_criteria icc ON (
        (icc.criteria_source = 'Contact' AND icc.entity_id = c.contact_id) OR
        (icc.criteria_source = 'Company' AND icc.entity_id = c.company_id)
      )
      LEFT JOIN companies comp ON c.company_id = comp.company_id
      WHERE c.contact_id = $1
      ORDER BY icc.criteria_source DESC, icc.investment_criteria_id
    `;

    const contactResult = await pool.query(contactQuery, [contactId]);
    
    if (contactResult.rows.length === 0) {
      return NextResponse.json({
        matches: [],
        total: 0,
        message: 'No investment criteria found for this contact'
      });
    }

    const contactCriteria = contactResult.rows;
    console.log(`📊 Found ${contactCriteria.length} investment criteria records for contact ${contactId}`);

    // Get all deals from dealsv2 table with NSF fields
    const dealsQuery = `
      SELECT 
        d.deal_id,
        d.deal_name,
        d.summary,
        d.ask_capital_position,
        d.ask_amount,
        d.strategy,
        d.deal_stage,
        d.deal_status,
        d.hold_period,
        d.yield_on_cost,
        d.common_equity_internal_rate_of_return_irr,
        d.common_equity_equity_multiple,
        d.gp_equity_multiple,
        d.gp_internal_rate_of_return_irr,
        d.lp_equity_multiple,
        d.lp_internal_rate_of_return_irr,
        d.preferred_equity_internal_rate_of_return_irr,
        d.preferred_equity_equity_multiple,
        d.total_internal_rate_of_return_irr,
        d.total_equity_multiple,
        d.loan_amount,
        d.interest_rate,
        d.loan_term,
        d.loan_to_cost_ltc,
        d.loan_to_value_ltv,
        d.loan_type,
        d.dscr,
        d.recourse,
        d.extra_fields,
        d.exit_cap_rate,
        d.created_at,
        d.updated_at,
        -- Property data
        p.address,
        p.city,
        p.state,
        p.region,
        p.country,
        p.property_type,
        p.subproperty_type,
        p.building_sqft,
        p.number_of_units,
        -- Core NSF measurements from property table
        p.gsf_gross_square_foot,
        p.zfa_zoning_floor_area,
        p.total_nsf_net_square_foot
      FROM dealsv2 d
      LEFT JOIN properties p ON d.property_id = p.property_id
      WHERE d.deal_status != 'closed'
      ORDER BY d.created_at DESC
      LIMIT $1 OFFSET $2
    `;

    const dealsResult = await pool.query(dealsQuery, [limit, offset]);
    const deals = dealsResult.rows;

    console.log(`📊 Found ${deals.length} deals to evaluate`);

    // Calculate matches for each deal
    const matches: any[] = [];

    for (const deal of deals) {
      // Get NSF fields for this deal
      const nsfQuery = `
        SELECT 
          capital_position,
          source_type,
          amount,
          use_type,
          is_required
        FROM deal_nsf_fields
        WHERE deal_id = $1
      `;
      
      const nsfResult = await pool.query(nsfQuery, [deal.deal_id]);
      const nsfFields = nsfResult.rows;
      
      // Add NSF fields to deal object
      deal.nsfFields = nsfFields;
      
      // Get capital positions from NSF fields where is_required = true
      const requiredCapitalPositions = nsfFields
        .filter(nsf => nsf.is_required === true)
        .map(nsf => nsf.capital_position || nsf.source_type)
        .filter(Boolean);

      if (requiredCapitalPositions.length === 0) {
        console.log(`⚠️ Deal ${deal.deal_id} has no required capital positions, skipping`);
        continue;
      }

      // For each capital position in the deal, find matching criteria
      for (const dealCapitalPosition of requiredCapitalPositions) {
        // Find criteria that match this capital position
        const matchingCriteria = contactCriteria.filter(criteria => 
          criteria.capital_position && 
          criteria.capital_position === dealCapitalPosition
        );

        for (const criteria of matchingCriteria) {
          console.log(`🔍 Evaluating deal ${deal.deal_id} (${deal.deal_name}) for capital position ${dealCapitalPosition}`);
          
          // Calculate match score using v2 matching with position-aware field matching
          const matchResult = await calculateV2MatchScore(
            deal,
            criteria,
            dealCapitalPosition,
            criteria.position_type // Pass position type for proper field matching
          );

          const matchScore = Math.round(matchResult.totalScore * 100);

          // Only include matches above threshold
          if (matchScore >= minScoreThreshold) {
            console.log(`✅ Match score ${matchScore}% above threshold ${minScoreThreshold}%`);
            
            matches.push({
              deal_id: deal.deal_id,
              deal_name: deal.deal_name,
              capital_position: dealCapitalPosition,
              match_score: matchScore,
              match_breakdown: matchResult.breakdown,
              match_reasons: matchResult.reasons,
              criteria_source: criteria.criteria_source,
              investment_criteria_id: criteria.investment_criteria_id,
              deal_data: {
                deal_id: deal.deal_id,
                deal_name: deal.deal_name,
                summary: deal.summary,
                ask_capital_position: deal.ask_capital_position,
                ask_amount: deal.ask_amount,
                strategy: deal.strategy,
                deal_stage: deal.deal_stage,
                deal_status: deal.deal_status,
                hold_period: deal.hold_period,
                yield_on_cost: deal.yield_on_cost,
                total_internal_rate_of_return_irr: deal.total_internal_rate_of_return_irr,
                total_equity_multiple: deal.total_equity_multiple,
                exit_cap_rate: deal.exit_cap_rate,
                loan_amount: deal.loan_amount,
                interest_rate: deal.interest_rate,
                loan_term: deal.loan_term,
                loan_to_cost_ltc: deal.loan_to_cost_ltc,
                loan_to_value_ltv: deal.loan_to_value_ltv,
                loan_type: deal.loan_type,
                dscr: deal.dscr,
                recourse: deal.recourse,
                property_address: deal.address,
                property_city: deal.city,
                property_state: deal.state,
                property_region: deal.region,
                property_country: deal.country,
                property_type: deal.property_type,
                property_subtype: deal.subproperty_type,
                building_sqft: deal.building_sqft,
                number_of_units: deal.number_of_units,
                total_nsf_net_square_foot: deal.total_nsf_net_square_foot,
                gsf_gross_square_foot: deal.gsf_gross_square_foot,
                zfa_zoning_floor_area: deal.zfa_zoning_floor_area,
                created_at: deal.created_at,
                updated_at: deal.updated_at
              },
              contact_data: {
                target_return: criteria.target_return,
                property_types: criteria.property_types,
                deal_amount_min: criteria.minimum_deal_size,
                deal_amount_max: criteria.maximum_deal_size,
                strategies: criteria.strategies,
                // Location with Priority: ICC > Contact > Company
                region: criteria.icc_region || criteria.contact_region || criteria.contact_country,
                state: criteria.icc_state || criteria.contact_state || criteria.comp_state,
                city: criteria.icc_city || criteria.contact_city || criteria.comp_city,
                country: criteria.icc_country || criteria.contact_country || criteria.comp_country,
                // Position type for proper field matching
                position_type: criteria.position_type,
                // Debt fields (only populated for debt positions)
                loan_to_value_min: criteria.loan_to_value_min,
                loan_to_value_max: criteria.loan_to_value_max,
                loan_to_cost_min: criteria.loan_to_cost_min,
                loan_to_cost_max: criteria.loan_to_cost_max,
                min_loan_term: criteria.min_loan_term,
                max_loan_term: criteria.max_loan_term,
                min_loan_dscr: criteria.min_loan_dscr,
                max_loan_dscr: criteria.max_loan_dscr,
                loan_interest_rate: criteria.loan_interest_rate,
                loan_type: criteria.loan_type,
                amortization: criteria.amortization,
                recourse_loan: criteria.recourse_loan,
                // Equity fields (only populated for equity positions)
                min_hold_period_years: criteria.min_hold_period_years,
                max_hold_period_years: criteria.max_hold_period_years,
                minimum_equity_multiple: criteria.minimum_equity_multiple,
                minimum_yield_on_cost: criteria.minimum_yield_on_cost,
                ownership_requirement: criteria.ownership_requirement,
                attachment_point: criteria.attachment_point,
                max_leverage_tolerance: criteria.max_leverage_tolerance
              },
              // V2 Detailed Scoring Breakdown
              detailed_scoring: matchResult.breakdown || [],
              total_score: matchResult.totalScore,
              // Enhanced scoring summary with proper field breakdown
              scoring_summary: {
                total_score_percentage: Math.round(matchResult.totalScore * 100),
                field_breakdown: matchResult.breakdown.map((field: any) => ({
                  field_name: field.field,
                  score_percentage: Math.round(field.score * 100),
                  weight_percentage: Math.round(field.weight * 100),
                  reason: field.reason,
                  confidence: field.confidence
                })),
                top_matches: matchResult.breakdown
                  .filter((field: any) => field.score > 0)
                  .sort((a: any, b: any) => b.score - a.score)
                  .slice(0, 5)
                  .map((field: any) => ({
                    field: field.field,
                    score: Math.round(field.score * 100),
                    reason: field.reason
                  })),
                // Show the actual weights used for this capital position
                weights_used: matchResult.breakdown.reduce((acc: any, field: any) => {
                  acc[field.field] = Math.round(field.weight * 100);
                  return acc;
                }, {}),
                // Position-aware matching info
                position_type: criteria.position_type,
                capital_position: dealCapitalPosition,
                // Add comprehensive match analysis
                match_analysis: {
                  total_fields_evaluated: matchResult.breakdown.length,
                  fields_with_matches: matchResult.breakdown.filter((field: any) => field.score > 0).length,
                  fields_without_matches: matchResult.breakdown.filter((field: any) => field.score === 0).length,
                  average_confidence: matchResult.breakdown.length > 0 ? 
                    matchResult.breakdown.reduce((sum: number, field: any) => sum + field.confidence, 0) / matchResult.breakdown.length : 0
                }
              }
            });
          } else {
            console.log(`❌ Match score ${matchScore}% below threshold ${minScoreThreshold}%`);
          }
        }
      }
    }

    // Sort matches by score (highest first)
    matches.sort((a, b) => b.match_score - a.match_score);

    console.log(`📊 V2 Matching complete: ${matches.length} matches found above threshold ${minScoreThreshold}%`);

    return NextResponse.json({
      matches: matches.slice(0, limit),
      total: matches.length,
      contact_id: contactId,
      min_score_threshold: minScoreThreshold,
      message: `Found ${matches.length} matching deals for contact ${contactId}`
    });

  } catch (error) {
    console.error('Error in deals-for-contact V2 API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
