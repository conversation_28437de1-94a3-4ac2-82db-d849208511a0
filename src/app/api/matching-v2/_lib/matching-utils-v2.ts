import { pool } from "@/lib/db";

// Types for v2 matching
export interface CapitalPositionFieldWeights {
  [fieldName: string]: number;
}

export interface V2MatchResult {
  score: number;
  reason: string;
  confidence: number;
}

export interface V2DealMatch {
  deal_id: number;
  deal_name: string;
  capital_position: string;
  match_score: number;
  match_breakdown: Array<{
    field: string;
    score: number;
    weight: number;
    reason: string;
    confidence: number;
  }>;
  deal_data: any;
  contact_data: any;
}

// V2 Weight management functions - Updated for COMMON weights + overrides
export async function fetchCapitalPositionWeights(capitalPosition: string): Promise<CapitalPositionFieldWeights> {
  try {
    console.log(`🔍 Fetching weights for capital position: ${capitalPosition}`);
    
    // First, get the COMMON weights as the base (these are the defaults)
    const commonQuery = `
      SELECT field_name, weight, table_name, description
      FROM capital_position_field_weights
      WHERE capital_position = 'COMMON' AND is_active = true AND weight > 0
      ORDER BY table_name, weight DESC
    `;
    
    const commonResult = await pool.query(commonQuery);
    let weights: CapitalPositionFieldWeights = {};
    
    if (commonResult.rows.length > 0) {
      console.log(`✅ Found ${commonResult.rows.length} COMMON weight configurations`);
      for (const row of commonResult.rows) {
        weights[row.field_name] = parseFloat(row.weight);
        console.log(`  COMMON ${row.field_name}: ${row.weight} (${(parseFloat(row.weight) * 100).toFixed(1)}%) - ${row.description}`);
      }
    } else {
      console.log(`⚠️ No COMMON weights found, trying to fetch COMMON weights directly`);
      return await getDefaultCapitalPositionWeights(capitalPosition);
    }
    
    // Then, get any specific overrides for this capital position (if not COMMON)
    if (capitalPosition !== 'COMMON') {
      const specificQuery = `
        SELECT field_name, weight, table_name, description
        FROM capital_position_field_weights
        WHERE capital_position = $1 AND is_active = true AND weight > 0
        ORDER BY table_name, weight DESC
      `;
      
      const specificResult = await pool.query(specificQuery, [capitalPosition]);
      
      if (specificResult.rows.length > 0) {
        console.log(`✅ Found ${specificResult.rows.length} specific overrides for ${capitalPosition}`);
        for (const row of specificResult.rows) {
          weights[row.field_name] = parseFloat(row.weight);
          console.log(`  OVERRIDE ${row.field_name}: ${row.weight} (${(parseFloat(row.weight) * 100).toFixed(1)}%) - ${row.description}`);
        }
      } else {
        console.log(`📝 No specific overrides for ${capitalPosition}, using COMMON weights only`);
      }
    }
    
    // Calculate total weight for validation
    let totalWeight = 0;
    for (const fieldName in weights) {
      totalWeight += weights[fieldName];
    }
    
    console.log(`📊 Total weight: ${totalWeight} (${(totalWeight * 100).toFixed(1)}%)`);
    
    if (totalWeight === 0) {
      console.log(`⚠️ No weights found, trying to fetch COMMON weights as fallback`);
      return await getDefaultCapitalPositionWeights(capitalPosition);
    }
    
    // Log final weights
    console.log(`📊 Final weights for ${capitalPosition}:`);
    for (const [field, weight] of Object.entries(weights)) {
      console.log(`  ${field}: ${weight} (${(weight * 100).toFixed(1)}%)`);
    }
    
    return weights;
  } catch (error) {
    console.error(`❌ Error fetching weights for ${capitalPosition}:`, error);
    console.log(`🔄 Falling back to COMMON weights for ${capitalPosition}`);
    return await getDefaultCapitalPositionWeights(capitalPosition);
  }
}

export async function getDefaultCapitalPositionWeights(capitalPosition: string): Promise<CapitalPositionFieldWeights> {
  // Always use COMMON weights from database - no hardcoded fallbacks
  console.log(`🔄 Fetching COMMON weights as fallback for ${capitalPosition}`);
  
  try {
    const commonQuery = `
      SELECT field_name, weight
      FROM capital_position_field_weights
      WHERE capital_position = 'COMMON' AND is_active = true AND weight > 0
      ORDER BY weight DESC
    `;
    
    const result = await pool.query(commonQuery);
    const weights: CapitalPositionFieldWeights = {};
    
    if (result.rows.length > 0) {
      console.log(`✅ Using ${result.rows.length} COMMON weights as defaults`);
      for (const row of result.rows) {
        weights[row.field_name] = parseFloat(row.weight);
        console.log(`  COMMON ${row.field_name}: ${row.weight} (${(parseFloat(row.weight) * 100).toFixed(1)}%)`);
      }
      return weights;
    } else {
      console.log(`❌ No COMMON weights found in database`);
      return {};
    }
  } catch (error) {
    console.error(`❌ Error fetching COMMON weights:`, error);
    return {};
  }
}

// Helper functions to determine field types for position-aware matching
function isDebtField(fieldName: string): boolean {
  const debtFields = [
    'loan_to_value', 'loan_to_cost', 'loan_dscr', 'interest_rate', 'loan_term',
    'amortization', 'recourse', 'debt_service_coverage', 'loan_type'
  ];
  return debtFields.includes(fieldName);
}

function isEquityField(fieldName: string): boolean {
  const equityFields = [
    'target_return', 'equity_multiple', 'hold_period', 'yield_on_cost',
    'ownership_requirement', 'attachment_point', 'max_leverage_tolerance',
    'cash_on_cash', 'preferred_return'
  ];
  return equityFields.includes(fieldName);
}

// V2 Scoring functions for deal v2 tables
export function calculateV2ArrayMatchScore(dealArray: any, contactArray: any, fieldName: string): V2MatchResult {
  if (!dealArray || !contactArray) {
    return {
      score: 0,
      reason: `No ${fieldName} data available`,
      confidence: 0.5
    };
  }

  const dealValues = Array.isArray(dealArray) ? dealArray : [dealArray];
  const contactValues = Array.isArray(contactArray) ? contactArray : [contactArray];

  if (dealValues.length === 0 || contactValues.length === 0) {
    return {
      score: 0,
      reason: `Empty ${fieldName} arrays`,
      confidence: 0.5
    };
  }

  // Ensure all values are strings and handle nested arrays
  const normalizedDealValues = dealValues
    .flat()
    .filter(value => value != null && value !== '')
    .map(value => String(value).toLowerCase().trim());
    
  const normalizedContactValues = contactValues
    .flat()
    .filter(value => value != null && value !== '')
    .map(value => String(value).toLowerCase().trim());

  if (normalizedDealValues.length === 0 || normalizedContactValues.length === 0) {
    return {
      score: 0,
      reason: `No valid ${fieldName} data after normalization`,
      confidence: 0.5
    };
  }

  const intersection = normalizedDealValues.filter(value => 
    normalizedContactValues.some(contactValue => 
      contactValue === value
    )
  );

  if (intersection.length === 0) {
    return {
      score: 0,
      reason: `No ${fieldName} matches found`,
      confidence: 0.8
    };
  }

  const score = intersection.length / Math.max(normalizedDealValues.length, normalizedContactValues.length);
  
  return {
    score,
    reason: `${fieldName} match: ${intersection.join(', ')}`,
    confidence: 0.9
  };
}

export function calculateV2DealAmountScore(
  dealAmount: number | null,
  contactMinAmount: number | null,
  contactMaxAmount: number | null
): V2MatchResult {
  if (!dealAmount || (!contactMinAmount && !contactMaxAmount)) {
    return {
      score: 0,
      reason: 'Missing deal amount or contact amount preferences',
      confidence: 0.5
    };
  }

  // Convert contact amounts from raw dollars to millions if they're too large
  let contactMin = contactMinAmount || 0;
  let contactMax = contactMaxAmount || contactMin || 0;

  // If contact amounts are in raw dollars (very large numbers), convert to millions
  if (contactMin > 1000000 || contactMax > 1000000) {
    contactMin = contactMin / 1000000;
    contactMax = contactMax / 1000000;
    console.log(`Converted contact amounts from raw dollars to millions: $${contactMin}M - $${contactMax}M`);
  }

  if (dealAmount === 0 || contactMax === 0) {
    return {
      score: 0,
      reason: 'Invalid amount values',
      confidence: 0.5
    };
  }

  // Check if deal amount falls within contact's range
  if (dealAmount >= contactMin && dealAmount <= contactMax) {
    return {
      score: 1.0,
      reason: `Deal amount $${dealAmount}M within range $${contactMin}M - $${contactMax}M`,
      confidence: 0.95
    };
  }

  // Calculate overlap percentage
  const overlap = Math.max(0, Math.min(dealAmount, contactMax) - Math.max(dealAmount, contactMin));
  const totalRange = Math.max(dealAmount, contactMax) - Math.min(dealAmount, contactMin);
  
  if (totalRange === 0) {
    return {
      score: 0,
      reason: 'No amount overlap',
      confidence: 0.8
    };
  }

  const score = overlap / totalRange;
  
  return {
    score,
    reason: `Deal amount $${dealAmount}M has ${Math.round(score * 100)}% overlap with range $${contactMin}M - $${contactMax}M`,
    confidence: 0.85
  };
}

export function calculateV2RangeMatchScore(
  dealMin: number | null,
  dealMax: number | null,
  contactMin: number | null,
  contactMax: number | null,
  fieldName: string
): V2MatchResult {
  if (!dealMin && !dealMax || !contactMin && !contactMax) {
    return {
      score: 0,
      reason: `Missing ${fieldName} range data`,
      confidence: 0.5
    };
  }

  // Use single values if ranges not provided
  const dealValue = dealMax || dealMin || 0;
  const contactMinVal = contactMin || 0;
  const contactMaxVal = contactMax || contactMinVal || 0;

  if (dealValue === 0 || contactMaxVal === 0) {
    return {
      score: 0,
      reason: `Invalid ${fieldName} values`,
      confidence: 0.5
    };
  }

  // Check if deal value falls within contact's range
  if (dealValue >= contactMinVal && dealValue <= contactMaxVal) {
    return {
      score: 1.0,
      reason: `${fieldName} ${dealValue} within range ${contactMinVal} - ${contactMaxVal}`,
      confidence: 0.95
    };
  }

  // Calculate overlap percentage
  const overlap = Math.max(0, Math.min(dealValue, contactMaxVal) - Math.max(dealValue, contactMinVal));
  const totalRange = Math.max(dealValue, contactMaxVal) - Math.min(dealValue, contactMinVal);
  
  if (totalRange === 0) {
    return {
      score: 0,
      reason: `No ${fieldName} overlap`,
      confidence: 0.8
    };
  }

  const score = overlap / totalRange;
  
  return {
    score,
    reason: `${fieldName} ${dealValue} has ${Math.round(score * 100)}% overlap with range ${contactMinVal} - ${contactMaxVal}`,
    confidence: 0.85
  };
}

export function calculateV2LocationScore(
  dealRegion: string | null,
  dealState: string | null,
  dealCity: string | null,
  contactRegion: string[] | null,
  contactState: string[] | null,
  contactCity: string[] | null
): V2MatchResult {
  console.log('🗺️ Location matching details:');
  console.log('Deal:', { region: dealRegion, state: dealState, city: dealCity });
  console.log('Contact:', { region: contactRegion, state: contactState, city: contactCity });

  // Check city match first (highest priority)
  if (dealCity && contactCity) {
    console.log('Checking city match...');
    const cityScore = calculateV2ArrayMatchScore([dealCity], contactCity, "city");
    console.log('City score:', cityScore);
    if (cityScore.score > 0) {
      return {
        score: cityScore.score,
        reason: cityScore.reason,
        confidence: 0.95
      };
    }
  }

  // Check state match
  if (dealState && contactState) {
    console.log('Checking state match...');
    const stateScore = calculateV2ArrayMatchScore([dealState], contactState, "state");
    console.log('State score:', stateScore);
    if (stateScore.score > 0) {
      return {
        score: stateScore.score * 0.8, // Slightly lower weight for state vs city
        reason: stateScore.reason,
        confidence: 0.9
      };
    }
  }

  // Check region match
  if (dealRegion && contactRegion) {
    console.log('Checking region match...');
    const regionScore = calculateV2ArrayMatchScore([dealRegion], contactRegion, "region");
    console.log('Region score:', regionScore);
    if (regionScore.score > 0) {
      return {
        score: regionScore.score * 0.6, // Lower weight for region
        reason: regionScore.reason,
        confidence: 0.85
      };
    }
  }

  console.log('No location matches found');
  return {
    score: 0,
    reason: 'No location matches found',
    confidence: 0.8
  };
}

// Comprehensive V2 field scorer - Updated to use central investment criteria tables
export function calculateV2FieldScore(
  dealData: any,
  contactData: any,
  fieldName: string,
  fieldWeight: number,
  capitalPosition?: string
): { field: string; score: number; weight: number; reason: string; confidence: number } | null {
  
  if (fieldWeight <= 0) {
    console.log(`⏭️ Skipping field '${fieldName}' with weight 0`);
    return null; // Skip fields with 0 weight
  }
  
  console.log(`🔍 Calculating score for field: ${fieldName}`);
  let result: V2MatchResult;
  
  switch (fieldName) {
    case 'location':
      console.log('🗺️ LOCATION MATCHING:');
      console.log('Deal Location (from property table):', {
        region: dealData.region,
        state: dealData.state,
        city: dealData.city,
        country: dealData.country,
        address: dealData.address
      });
      
      // Use priority order: ICC > Contact > Company
      const contactRegion = contactData.icc_region || contactData.contact_region || null;
      const contactState = contactData.icc_state || contactData.contact_state || contactData.comp_state || null;
      const contactCity = contactData.icc_city || contactData.contact_city || contactData.comp_city || null;
      const contactCountry = contactData.icc_country || contactData.contact_country || contactData.comp_country || null;
      
      console.log('Contact Location (Priority Order):', {
        region: contactRegion,
        state: contactState,
        city: contactCity,
        country: contactCountry
      });
      
      // Ensure we pass arrays for contact location data
      const contactRegionArray = contactRegion ? (Array.isArray(contactRegion) ? contactRegion : [contactRegion]) : null;
      const contactStateArray = contactState ? (Array.isArray(contactState) ? contactState : [contactState]) : null;
      const contactCityArray = contactCity ? (Array.isArray(contactCity) ? contactCity : [contactCity]) : null;
      
      result = calculateV2LocationScore(
        dealData.region,
        dealData.state,
        dealData.city,
        contactRegionArray,
        contactStateArray,
        contactCityArray
      );
      
      console.log('Location match result:', result);
      break;

    case 'deal_amount':
      console.log('💰 DEAL AMOUNT MATCHING:');
      console.log(`Looking for NSF field amount for capital position: ${capitalPosition}`);
      
      // Use NSF fields amount where source_type matches the capital position
      let dealAmount = 0;
      
      if (dealData.nsfFields && Array.isArray(dealData.nsfFields) && capitalPosition) {
        console.log('Available NSF fields:', dealData.nsfFields);
        
        // Find NSF field where source_type or capital_position matches the capital position
        const matchingNsfField = dealData.nsfFields.find((nsf: any) => 
          nsf.source_type === capitalPosition || 
          nsf.capital_position === capitalPosition
        );
        
        if (matchingNsfField && matchingNsfField.amount) {
          dealAmount = Number(matchingNsfField.amount);
          console.log(`✅ Found matching NSF field for ${capitalPosition}:`, matchingNsfField);
          console.log(`Deal amount for ${capitalPosition}: $${dealAmount}`);
        } else {
          console.log(`❌ No matching NSF field found for capital position: ${capitalPosition}`);
          console.log(`Available NSF fields:`, dealData.nsfFields.map((nsf: any) => ({
            source_type: nsf.source_type,
            capital_position: nsf.capital_position,
            amount: nsf.amount
          })));
          // Don't use fallback - if no NSF field for this capital position, score should be 0
          dealAmount = 0;
        }
      } else {
        console.log('❌ Missing NSF fields data or capital position');
        dealAmount = 0;
      }
      
      // Convert to millions if needed (assuming amount is in dollars)
      const dealAmountInMillions = dealAmount / 1000000;
      console.log(`Deal amount in millions: $${dealAmountInMillions}M`);
      console.log(`Contact deal size range (raw): $${contactData.minimum_deal_size || 0} - $${contactData.maximum_deal_size || 0}`);
      
      result = calculateV2DealAmountScore(
        dealAmountInMillions,
        contactData.minimum_deal_size,
        contactData.maximum_deal_size
      );
      
      console.log('Deal amount match result:', result);
      break;



    case 'strategies':
      result = calculateV2ArrayMatchScore(
        dealData.strategy ? [dealData.strategy] : null,
        contactData.strategies,
        "strategies"
      );
      break;

    case 'target_return':
    case 'minimum_internal_rate_of_return':
    case 'target_return_irr_on_equity':
      // Compare deal IRR with contact target return or minimum IRR expectations
      // All IRR/target return fields should be treated the same
      let dealIrr = null;
      
      if (dealData.total_internal_rate_of_return_irr) {
        dealIrr = dealData.total_internal_rate_of_return_irr;
      } else if (dealData.common_equity_internal_rate_of_return_irr) {
        dealIrr = dealData.common_equity_internal_rate_of_return_irr;
      } else if (dealData.gp_internal_rate_of_return_irr) {
        dealIrr = dealData.gp_internal_rate_of_return_irr;
      } else if (dealData.lp_internal_rate_of_return_irr) {
        dealIrr = dealData.lp_internal_rate_of_return_irr;
      } else if (dealData.preferred_equity_internal_rate_of_return_irr) {
        dealIrr = dealData.preferred_equity_internal_rate_of_return_irr;
      }
      
      // Check all possible IRR/target return fields in contact data
      const contactTargetReturn = contactData.target_return || 
                                  contactData.minimum_internal_rate_of_return || 
                                  contactData.target_return_irr_on_equity;
      
      if (dealIrr && contactTargetReturn) {
        const dealIrrNum = Number(dealIrr);
        let contactTargetNum = Number(contactTargetReturn);
        
        if (!isNaN(dealIrrNum) && !isNaN(contactTargetNum)) {
          // Handle case where target_return might be stored as percentage (e.g., 12.0) instead of decimal (0.12)
          if (contactTargetNum > 1) {
            contactTargetNum = contactTargetNum / 100; // Convert percentage to decimal
          }
          
          const diff = Math.abs(dealIrrNum - contactTargetNum);
          const score = Math.max(0, 1 - (diff / contactTargetNum));
          result = {
            score,
            reason: `IRR ${(dealIrrNum * 100).toFixed(1)}% vs target ${(contactTargetNum * 100).toFixed(1)}%`,
            confidence: 0.9
          };
        } else {
          result = {
            score: 0,
            reason: 'Invalid IRR or target return data (not numeric)',
            confidence: 0.3
          };
        }
      } else {
        result = {
          score: 0,
          reason: 'Missing IRR or target return data',
          confidence: 0.5
        };
      }
      break;

    case 'loan_to_value':
      result = calculateV2RangeMatchScore(
        dealData.loan_to_value_ltv,
        dealData.loan_to_value_ltv,
        contactData.loan_to_value_min,
        contactData.loan_to_value_max,
        "LTV"
      );
      break;

    case 'loan_to_cost':
      result = calculateV2RangeMatchScore(
        dealData.loan_to_cost_ltc,
        dealData.loan_to_cost_ltc,
        contactData.loan_to_cost_min,
        contactData.loan_to_cost_max,
        "LTC"
      );
      break;

    case 'interest_rate':
      result = calculateV2RangeMatchScore(
        dealData.interest_rate,
        dealData.interest_rate,
        contactData.loan_interest_rate,
        contactData.loan_interest_rate,
        "Interest Rate"
      );
      break;

    case 'loan_term':
      result = calculateV2RangeMatchScore(
        dealData.loan_term,
        dealData.loan_term,
        contactData.min_loan_term,
        contactData.max_loan_term,
        "Loan Term"
      );
      break;

    case 'loan_dscr':
      result = calculateV2RangeMatchScore(
        dealData.dscr,
        dealData.dscr,
        contactData.min_loan_dscr,
        contactData.max_loan_dscr,
        "DSCR"
      );
      break;

    case 'hold_period':
      result = calculateV2RangeMatchScore(
        dealData.hold_period,
        dealData.hold_period,
        contactData.min_hold_period_years,
        contactData.max_hold_period_years,
        "Hold Period"
      );
      break;

    case 'yield_on_cost':
      // Compare deal yield on cost with contact target return
      if (dealData.yield_on_cost && contactData.target_return) {
        const dealYieldNum = Number(dealData.yield_on_cost);
        let contactTargetNum = Number(contactData.target_return);
        
        if (!isNaN(dealYieldNum) && !isNaN(contactTargetNum)) {
          // Handle case where target_return might be stored as percentage (e.g., 12.0) instead of decimal (0.12)
          if (contactTargetNum > 1) {
            contactTargetNum = contactTargetNum / 100; // Convert percentage to decimal
          }
          
          const diff = Math.abs(dealYieldNum - contactTargetNum);
          const score = Math.max(0, 1 - (diff / contactTargetNum));
          result = {
            score,
            reason: `Yield on cost ${(dealYieldNum * 100).toFixed(1)}% vs target ${(contactTargetNum * 100).toFixed(1)}%`,
            confidence: 0.9
          };
        } else {
          result = {
            score: 0,
            reason: 'Invalid yield on cost or target return data (not numeric)',
            confidence: 0.3
          };
        }
      } else {
        result = {
          score: 0,
          reason: 'Missing yield on cost or target return data',
          confidence: 0.5
        };
      }
      break;

    case 'equity_multiple':
    case 'minimum_equity_multiple':
      // Compare deal equity multiple with contact minimum equity multiple expectations
      // Both equity_multiple and minimum_equity_multiple fields should be treated the same
      let dealEquityMultiple: number | null = null;
      
      if (dealData.total_equity_multiple) {
        dealEquityMultiple = dealData.total_equity_multiple;
      } else if (dealData.common_equity_equity_multiple) {
        dealEquityMultiple = dealData.common_equity_equity_multiple;
      } else if (dealData.gp_equity_multiple) {
        dealEquityMultiple = dealData.gp_equity_multiple;
      } else if (dealData.lp_equity_multiple) {
        dealEquityMultiple = dealData.lp_equity_multiple;
      } else if (dealData.preferred_equity_equity_multiple) {
        dealEquityMultiple = dealData.preferred_equity_equity_multiple;
      }
      
      // Check both equity_multiple and minimum_equity_multiple in contact data
      const contactMinMultiple = contactData.minimum_equity_multiple || contactData.equity_multiple;
      
      if (dealEquityMultiple !== null && contactMinMultiple) {
        // Convert to numbers to ensure we can perform calculations
        const dealMultiple = Number(dealEquityMultiple);
        const contactMinMultipleNum = Number(contactMinMultiple);
        
        if (!isNaN(dealMultiple) && !isNaN(contactMinMultipleNum)) {
          // Simple scoring based on whether multiple meets minimum requirement
          const score = dealMultiple >= contactMinMultipleNum ? 1.0 : 0.5;
          result = {
            score,
            reason: `Equity multiple ${dealMultiple.toFixed(2)}x vs minimum ${contactMinMultipleNum.toFixed(2)}x`,
            confidence: 0.8
          };
        } else {
          result = {
            score: 0,
            reason: 'Invalid equity multiple or minimum requirement data (not numeric)',
            confidence: 0.3
          };
        }
      } else {
        result = {
          score: 0,
          reason: 'Missing equity multiple or minimum requirement data',
          confidence: 0.5
        };
      }
      break;

    case 'exit_cap_rate':
      // Compare deal exit cap rate with contact expectations
      if (dealData.exit_cap_rate && contactData.target_return) {
        // Lower cap rates typically mean higher property values and potentially better returns
        const expectedCapRate = 0.06; // 6% is a reasonable benchmark
        const diff = Math.abs(dealData.exit_cap_rate - expectedCapRate);
        const score = Math.max(0, 1 - (diff / expectedCapRate));
        result = {
          score,
          reason: `Exit cap rate ${(dealData.exit_cap_rate * 100).toFixed(1)}% vs expected ${(expectedCapRate * 100).toFixed(1)}%`,
          confidence: 0.8
        };
      } else {
        result = {
          score: 0,
          reason: 'Missing exit cap rate data',
          confidence: 0.5
        };
      }
      break;

    case 'deal_stage':
      // Compare deal stage with contact investment preferences
      if (dealData.deal_stage && contactData.deal_stages) {
        result = calculateV2ArrayMatchScore(
          [dealData.deal_stage],
          contactData.deal_stages,
          "deal_stage"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing deal stage or deal stage preferences',
          confidence: 0.5
        };
      }
      break;

    case 'property_size':
      // Compare property size (NSF) with contact preferences
      if (dealData.property?.total_nsf_net_square_foot && contactData.property_size_min && contactData.property_size_max) {
        const propertySizeInKSF = dealData.property.total_nsf_net_square_foot / 1000; // Convert to thousands of square feet
        result = calculateV2RangeMatchScore(
          propertySizeInKSF,
          propertySizeInKSF,
          contactData.property_size_min,
          contactData.property_size_max,
          "Property Size (KSF)"
        );
      } else {
        result = {
          score: 0,
          reason: 'Missing property size data or preferences',
          confidence: 0.5
        };
      }
      break;

    default:
      // Dynamic field matching for ALL other fields from weights table
      console.log(`🔄 DYNAMIC FIELD MATCHING for: ${fieldName}`);
      
      // Get the field value from deal data
      const dealFieldValue = dealData[fieldName];
      console.log(`Deal ${fieldName}:`, dealFieldValue);
      
      // Get the corresponding contact field value
      let contactFieldValue = null;
      let contactFieldName = fieldName;
      
      // Map field names to their contact equivalents based on the weights table structure
      const fieldMapping: { [key: string]: string } = {
        // Deal fields
        'property_type': 'property_types',
        'subproperty_type': 'property_types',
        'num_apartment_units': 'number_of_units',
        'occupancy_rate': 'occupancy_rate',
        'hold_period': 'min_hold_period_years',
        'strategy': 'strategies',
        
        // Property fields
        'floor_area_ratio': 'floor_area_ratio',
        
        // Debt fields - map to contact field names
        'loan_interest_rate': 'loan_interest_rate',
        'loan_to_cost_min': 'loan_to_cost_min',
        'loan_to_cost_max': 'loan_to_cost_max',
        'loan_to_value_min': 'loan_to_value_min',
        'loan_to_value_max': 'loan_to_value_max',
        'min_loan_term': 'min_loan_term',
        'max_loan_term': 'max_loan_term',
        'loan_type': 'loan_type',
        'amortization': 'amortization',
        'min_loan_dscr': 'min_loan_dscr',
        'max_loan_dscr': 'max_loan_dscr',
        'recourse_loan': 'recourse_loan',
        'lien_position': 'lien_position',
        'rate_type': 'rate_type',
        
        // Equity fields - map to contact field names
        'target_return': 'target_return',
        'equity_multiple': 'equity_multiple',
        'minimum_equity_multiple': 'minimum_equity_multiple',
        'minimum_internal_rate_of_return': 'minimum_internal_rate_of_return',
        'target_return_irr_on_equity': 'target_return_irr_on_equity',
        'min_hold_period_years': 'min_hold_period_years',
        'max_hold_period_years': 'max_hold_period_years',
        'yield_on_cost': 'yield_on_cost',
        'minimum_yield_on_cost': 'minimum_yield_on_cost',
        'target_cash_on_cash_min': 'target_cash_on_cash_min',
        'position_specific_equity_multiple': 'position_specific_equity_multiple',
        'position_specific_irr': 'position_specific_irr',
        'attachment_point': 'attachment_point',
        'max_leverage_tolerance': 'max_leverage_tolerance',
        'ownership_requirement': 'ownership_requirement',
        'proof_of_funds_requirement': 'proof_of_funds_requirement'
      };
      
      if (fieldMapping[fieldName]) {
        contactFieldName = fieldMapping[fieldName];
        contactFieldValue = contactData[contactFieldName];
        console.log(`Mapped contact field: ${fieldName} → ${contactFieldName}`);
      } else {
        contactFieldValue = contactData[fieldName];
        console.log(`Direct contact field: ${fieldName}`);
      }
      
      console.log(`Contact ${contactFieldName}:`, contactFieldValue);
      
      // Determine the type of field and calculate score accordingly
      if (Array.isArray(contactFieldValue)) {
        // Array field (like property_types, strategies)
        result = calculateV2ArrayMatchScore(
          dealFieldValue ? [dealFieldValue] : null,
          contactFieldValue,
          fieldName
        );
      } else if (typeof contactFieldValue === 'object' && contactFieldValue !== null && 
                 (contactFieldValue.min !== undefined || contactFieldValue.max !== undefined)) {
        // Range field (like min/max values)
        const rangeValue = contactFieldValue as { min?: number; max?: number; minimum?: number; maximum?: number };
        const min = rangeValue.min || rangeValue.minimum || null;
        const max = rangeValue.max || rangeValue.maximum || null;
        result = calculateV2RangeMatchScore(
          dealFieldValue,
          dealFieldValue,
          min,
          max,
          fieldName
        );
      } else if ((dealFieldValue !== null && dealFieldValue !== undefined) && 
                 (contactFieldValue !== null && contactFieldValue !== undefined)) {
        const dealNum = Number(dealFieldValue);
        const contactNum = Number(contactFieldValue);
        
        if (!isNaN(dealNum) && !isNaN(contactNum)) {
          // Numeric comparison
          const diff = Math.abs(dealNum - contactNum);
          const maxValue = Math.max(Math.abs(dealNum), Math.abs(contactNum), 1);
          const score = Math.max(0, 1 - (diff / maxValue));
          result = {
            score,
            reason: `${fieldName}: ${dealNum} vs ${contactNum}`,
            confidence: 0.8
          };
        } else if (typeof dealFieldValue === 'boolean' && typeof contactFieldValue === 'boolean') {
          // Boolean comparison
          const score = dealFieldValue === contactFieldValue ? 1.0 : 0.0;
          result = {
            score,
            reason: `${fieldName}: ${dealFieldValue} vs ${contactFieldValue}`,
            confidence: 0.9
          };
        } else {
          // String comparison
          const normalizedDeal = String(dealFieldValue).toLowerCase().trim();
          const normalizedContact = String(contactFieldValue).toLowerCase().trim();
          const score = normalizedDeal === normalizedContact ? 1.0 : 0.5;
          result = {
            score,
            reason: `${fieldName}: ${dealFieldValue} vs ${contactFieldValue}`,
            confidence: 0.7
          };
        }
      } else {
        // Missing data
        result = {
          score: 0,
          reason: `Missing ${fieldName} data`,
          confidence: 0.3
        };
      }
      
      console.log(`Dynamic field match result for ${fieldName}:`, result);
      break;
  }

  return {
    field: fieldName,
    score: result.score,
    weight: fieldWeight,
    reason: result.reason,
    confidence: result.confidence
  };
}

// Main V2 matching function
export async function calculateV2MatchScore(
  dealData: any,
  contactData: any,
  capitalPosition: string,
  positionType?: string // 'debt', 'equity', or 'unknown'
): Promise<{
  totalScore: number;
  breakdown: Array<{
    field: string;
    score: number;
    weight: number;
    reason: string;
    confidence: number;
  }>;
  reasons: string[];
}> {
  console.log('🚀 STARTING V2 MATCH SCORE CALCULATION');
  console.log('📊 DEAL DATA:', {
    deal_id: dealData.deal_id,
    deal_name: dealData.deal_name,
    ask_capital_position: dealData.ask_capital_position,
    strategy: dealData.strategy,
    property_type: dealData.property_type,
    nsfFields_count: dealData.nsfFields ? dealData.nsfFields.length : 0
  });
  console.log('👤 CONTACT DATA:', {
    contact_id: contactData.contact_id,
    name: `${contactData.first_name || ''} ${contactData.last_name || ''}`.trim(),
    company: contactData.company_name,
    capital_position: contactData.capital_position,
    criteria_source: contactData.criteria_source,
    investment_criteria_id: contactData.investment_criteria_id
  });
  console.log('🎯 CAPITAL POSITION:', capitalPosition);
  
  // Get weights for this capital position
  const weights = await fetchCapitalPositionWeights(capitalPosition);
  console.log('⚖️ CAPITAL POSITION WEIGHTS:', weights);
  
  const breakdown: Array<{
    field: string;
    score: number;
    weight: number;
    reason: string;
    confidence: number;
  }> = [];
  
  const reasons: string[] = [];
  let totalScore = 0;
  let totalWeight = 0;

  // Calculate scores for each field with position-aware matching
  // Only use fields with non-zero weights (as requested by user)
  for (const [fieldName, weight] of Object.entries(weights)) {
    if (weight <= 0) {
      console.log(`⏭️ Skipping field '${fieldName}' with zero weight: ${weight}`);
      continue;
    }
    
    console.log(`🔍 Evaluating field: ${fieldName} with weight: ${weight} (${(weight * 100).toFixed(1)}%)`);
    
    // Determine if this is a debt or equity field based on table_name in database
    const isDebtField = ['loan_interest_rate', 'loan_to_cost_min', 'loan_to_cost_max', 'loan_to_value_min', 'loan_to_value_max', 
                         'min_loan_term', 'max_loan_term', 'loan_type', 'amortization', 'min_loan_dscr', 'max_loan_dscr', 
                         'recourse_loan', 'lien_position', 'rate_type'].includes(fieldName);
    
    const isEquityField = ['target_return', 'equity_multiple', 'minimum_equity_multiple', 'minimum_internal_rate_of_return',
                          'target_return_irr_on_equity', 'min_hold_period_years', 'max_hold_period_years', 'yield_on_cost',
                          'minimum_yield_on_cost', 'target_cash_on_cash_min', 'position_specific_equity_multiple',
                          'position_specific_irr', 'attachment_point', 'max_leverage_tolerance', 'ownership_requirement',
                          'proof_of_funds_requirement'].includes(fieldName);
    
    // Skip fields that don't match the position type for better accuracy
    if (positionType && positionType !== 'unknown') {
      if (positionType === 'debt' && isEquityField) {
        console.log(`⏭️ Skipping equity field '${fieldName}' for debt position`);
        continue;
      }
      if (positionType === 'equity' && isDebtField) {
        console.log(`⏭️ Skipping debt field '${fieldName}' for equity position`);
        continue;
      }
    }
    
    const fieldResult = calculateV2FieldScore(dealData, contactData, fieldName, weight, capitalPosition);
    
    if (fieldResult) {
      console.log(`✅ Field ${fieldName}: Score=${fieldResult.score} (${(fieldResult.score * 100).toFixed(1)}%), Weight=${fieldResult.weight} (${(fieldResult.weight * 100).toFixed(1)}%), Reason="${fieldResult.reason}"`);
      breakdown.push(fieldResult);
      totalScore += fieldResult.score * fieldResult.weight;
      totalWeight += fieldResult.weight;
      
      if (fieldResult.score > 0) {
        reasons.push(fieldResult.reason);
      }
    } else {
      console.log(`❌ Field ${fieldName}: No result returned (likely zero weight or field not found)`);
    }
  }

  // Normalize score if we have weights
  if (totalWeight > 0) {
    totalScore = totalScore / totalWeight;
  }

  console.log(`📊 FINAL SCORING SUMMARY:`);
  console.log(`Total Score: ${totalScore} (${Math.round(totalScore * 100)}%)`);
  console.log(`Total Weight: ${totalWeight}`);
  console.log(`Fields Evaluated: ${breakdown.length}`);
  console.log(`Fields with Matches: ${breakdown.filter(f => f.score > 0).length}`);
  console.log(`Reasons: ${reasons.length}`);
  
  // Debug: Show each field's contribution to total score
  console.log(`📊 Field Contributions to Total Score:`);
  breakdown.forEach(field => {
    const contribution = field.score * field.weight;
    console.log(`  ${field.field}: ${field.score * 100}% × ${field.weight * 100}% = ${(contribution * 100).toFixed(1)}%`);
  });

  return {
    totalScore,
    breakdown,
    reasons
  };
}
