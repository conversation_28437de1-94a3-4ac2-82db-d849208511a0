import { NextRequest, NextResponse } from 'next/server'
import { processorScheduler } from '../../../../lib/scheduler/ProcessorScheduler'
import { ProcessingStage } from '../../../../types/processing'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, stage, entityType, options = {} } = body

    switch (action) {
      case 'execute_manual': {
        const { 
          limit, 
          singleId, 
          multiIds, 
          filters, 
          campaignId,
          searchTerm,
        } = options
        const batchSize = options.batchSize || 100 // Use provided batchSize or default to 100
        
        if (!stage) {
          return NextResponse.json(
            { success: false, error: 'Stage is required for manual execution' },
            { status: 400 }
          )
        }

        // Just pass all the filter params as they are (same as UI components)
        const processorFilters = {
          ...filters, // Include any legacy filters
          // Pass search term and any other unified filter params
          ...(searchTerm && { searchTerm }),
        }

        // Log what we will pass through for verification
        console.log('[Trigger] execute_manual called with:', {
          stage,
          limit,
          singleId,
          multiIds,
          entityType,
          processorFilters
        })

        const result = await processorScheduler.executeManualJob(stage as ProcessingStage, {
          limit,
          singleId,
          multiIds,
          filters: processorFilters,
          batchSize,
          entityType: entityType as 'contact' | 'company' | 'both' | undefined,
          campaignId
        })

        if (!result) {
          return NextResponse.json(
            { success: false, error: 'Failed to execute job' },
            { status: 500 }
          )
        }

        return NextResponse.json({
          success: true,
          data: {
            stage,
            result,
            appliedFilters: processorFilters,
            batchSize, // Actual batch size used for processing
            appliedLimit: limit || 'no limit', // User-specified limit or no limit
            processedIds: multiIds || (singleId ? [singleId] : undefined),
            entityType
          }
        })
      }

      case 'toggle_scheduled_job': {
        const { jobId, enabled } = options
        
        if (!jobId || typeof enabled !== 'boolean') {
          return NextResponse.json(
            { success: false, error: 'jobId and enabled status are required' },
            { status: 400 }
          )
        }

        processorScheduler.toggleJob(jobId, enabled)

        return NextResponse.json({
          success: true,
          data: {
            jobId,
            enabled
          }
        })
      }

      case 'execute_scheduled_job': {
        const { jobId, stage } = options
        
        if (!jobId || !stage) {
          return NextResponse.json(
            { success: false, error: 'jobId and stage are required' },
            { status: 400 }
          )
        }

        // Execute the scheduled job immediately
        const result = await processorScheduler.executeJob(jobId)

        if (!result) {
          return NextResponse.json(
            { success: false, error: 'Failed to execute scheduled job' },
            { status: 500 }
          )
        }

        return NextResponse.json({
          success: true,
          data: {
            jobId,
            stage,
            result,
            message: 'Scheduled job executed successfully'
          }
        })
      }

      case 'start_scheduler': {
        processorScheduler.start()
        
        return NextResponse.json({
          success: true,
          data: { message: 'Scheduler started' }
        })
      }

      case 'stop_scheduler': {
        processorScheduler.stop()
        
        return NextResponse.json({
          success: true,
          data: { message: 'Scheduler stopped' }
        })
      }

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Processing trigger error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'execute_manual_get': {
        // Support manual execution via GET with search params (same as UI components)
        const stage = searchParams.get('stage')
        const entityType = searchParams.get('entityType')
        const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined
        const singleId = searchParams.get('singleId') ? parseInt(searchParams.get('singleId')!) : undefined
        
        // Parse multiIds if provided as comma-separated values
        const multiIdsParam = searchParams.get('multiIds')
        const multiIds = multiIdsParam ? multiIdsParam.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id)) : undefined

        if (!stage) {
          return NextResponse.json(
            { success: false, error: 'Stage is required for manual execution' },
            { status: 400 }
          )
        }

        // Create filters object with all search params (same as UI components pass)
        const processorFilters = Object.fromEntries(searchParams.entries())

        const result = await processorScheduler.executeManualJob(stage as ProcessingStage, {
          limit,
          singleId,
          multiIds,
          filters: processorFilters,
          batchSize: parseInt(searchParams.get('batchSize') || '100'),
          entityType: entityType as 'contact' | 'company' | 'both' | undefined,
          campaignId: undefined
        })

        if (!result) {
          return NextResponse.json(
            { success: false, error: 'Failed to execute job' },
            { status: 500 }
          )
        }

        return NextResponse.json({
          success: true,
          data: {
            stage,
            result,
            appliedFilters: processorFilters,
            message: 'Manual job executed successfully via GET request'
          }
        })
      }

      case 'jobs': {
        const jobs = processorScheduler.getJobs()
        
        return NextResponse.json({
          success: true,
          data: { jobs }
        })
      }

      case 'running_jobs': {
        const runningJobs = processorScheduler.getRunningJobs()
        
        return NextResponse.json({
          success: true,
          data: { runningJobs }
        })
      }

      case 'job_status': {
        const jobId = searchParams.get('jobId')
        
        if (!jobId) {
          return NextResponse.json(
            { success: false, error: 'jobId is required' },
            { status: 400 }
          )
        }

        const job = processorScheduler.getJobStatus(jobId)
        
        return NextResponse.json({
          success: true,
          data: { job }
        })
      }

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action parameter' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('Processing trigger GET error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
} 