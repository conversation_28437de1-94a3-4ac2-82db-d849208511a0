import { NextRequest, NextResponse } from "next/server";
import { pool } from "@/lib/db";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get("email");

    console.log('🔍 Search-by-email API called with email:', email);

    if (!email || email.length < 2) {
      console.log('❌ Email too short or empty');
      return NextResponse.json({ contacts: [] });
    }

    // Search for exact email match (case-insensitive)
    const query = `
      SELECT 
        contact_id,
        first_name,
        last_name,
        full_name,
        email,
        personal_email,
        linkedin_url,
        title,
        company_id
      FROM public.contacts 
      WHERE 
        email ILIKE $1 OR 
        personal_email ILIKE $1 OR
        additional_email ILIKE $1
      ORDER BY 
        CASE 
          WHEN email ILIKE $2 THEN 1 
          ELSE 2 
        END,
        first_name,
        last_name
      LIMIT 5
    `;

    const result = await pool.query(query, [`%${email}%`, email]);
    
    // console.log('📋 Database query result:', result.rows);

    return NextResponse.json({ contacts: result.rows });
  } catch (error) {
    console.error("Error searching contacts by email:", error);
    return NextResponse.json(
      { error: "Failed to search contacts" },
      { status: 500 }
    );
  }
} 