import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ contactId: string }> }
) {
  try {
    const { contactId } = await params
    const { selectedCriteria } = await request.json()

    if (!contactId || !selectedCriteria || !Array.isArray(selectedCriteria)) {
      return NextResponse.json(
        { error: 'Contact ID and selected criteria are required' },
        { status: 400 }
      )
    }

    const results: Array<{
      original_criteria_id: any;
      new_criteria_id: any;
      capital_position: any;
    }> = []

    for (const criteria of selectedCriteria) {
      // FIXED: Always start with the original debt/equity IDs from the company
      console.log(' Value of criteria', criteria)
      let debtId = criteria.investment_criteria_debt_id;
      let equityId = criteria.investment_criteria_equity_id;

      // Only create new debt record if it was explicitly modified
      if (criteria.debtModified === true && criteria.investment_criteria_debt_id) {
        // Create new debt record if modified
        const debtInsertQuery = `
          INSERT INTO investment_criteria_debt (
            loan_type, loan_program, structured_loan_tranche, min_loan_term, max_loan_term,
            loan_interest_rate, loan_to_value_min, loan_to_value_max, loan_to_cost_min, loan_to_cost_max,
            loan_origination_min_fee, loan_origination_max_fee, loan_exit_min_fee, loan_exit_max_fee,
            min_loan_dscr, max_loan_dscr, recourse_loan, closing_time, future_facilities,
            eligible_borrower, occupancy_requirements, lien_position, loan_min_debt_yield,
            prepayment, yield_maintenance, amortization, application_deposit, good_faith_deposit,
            rate_lock, rate_type, debt_program_overview, notes
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25, $26, $27, $28, $29, $30, $31, $32)
          RETURNING investment_criteria_debt_id
        `;

        const debtValues = [
          criteria.loan_type, criteria.loan_program, criteria.structured_loan_tranche,
          criteria.debt_min_loan_term, criteria.debt_max_loan_term, criteria.debt_loan_interest_rate,
          criteria.debt_loan_to_value_min, criteria.debt_loan_to_value_max, criteria.debt_loan_to_cost_min, criteria.debt_loan_to_cost_max,
          criteria.debt_loan_origination_min_fee, criteria.debt_loan_origination_max_fee, criteria.debt_loan_exit_min_fee, criteria.debt_loan_exit_max_fee,
          criteria.debt_min_loan_dscr, criteria.debt_max_loan_dscr, criteria.debt_recourse_loan, criteria.debt_closing_time, criteria.debt_future_facilities,
          criteria.debt_eligible_borrower, criteria.debt_occupancy_requirements, criteria.debt_lien_position, criteria.debt_loan_min_debt_yield,
          criteria.debt_prepayment, criteria.debt_yield_maintenance, criteria.debt_amortization, criteria.debt_application_deposit, criteria.debt_good_faith_deposit,
          criteria.debt_rate_lock, criteria.debt_rate_type, criteria.debt_program_overview, criteria.debt_notes
        ];

        const debtResult = await pool.query(debtInsertQuery, debtValues);
        debtId = debtResult.rows[0].investment_criteria_debt_id;
      }

      // Only create new equity record if it was explicitly modified
      if (criteria.equityModified === true && criteria.investment_criteria_equity_id) {
        // Create new equity record if modified
        const equityInsertQuery = `
          INSERT INTO investment_criteria_equity (
            target_return, minimum_internal_rate_of_return, minimum_yield_on_cost, minimum_equity_multiple,
            target_cash_on_cash_min, min_hold_period_years, max_hold_period_years, ownership_requirement,
            attachment_point, max_leverage_tolerance, typical_closing_timeline_days, proof_of_funds_requirement,
            notes, equity_program_overview, occupancy_requirements
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
          RETURNING investment_criteria_equity_id
        `;

        const equityValues = [
          criteria.equity_target_return, criteria.equity_minimum_internal_rate_of_return, criteria.equity_minimum_yield_on_cost, criteria.equity_minimum_equity_multiple,
          criteria.equity_target_cash_on_cash_min, criteria.equity_min_hold_period_years, criteria.equity_max_hold_period_years, criteria.equity_ownership_requirement,
          criteria.equity_attachment_point, criteria.equity_max_leverage_tolerance, criteria.equity_typical_closing_timeline_days, criteria.equity_proof_of_funds_requirement,
          criteria.equity_notes, criteria.equity_program_overview, criteria.equity_occupancy_requirements
        ];

        const equityResult = await pool.query(equityInsertQuery, equityValues);
        equityId = equityResult.rows[0].investment_criteria_equity_id;
      }

      // Always create new central record with the appropriate debt/equity IDs
      const insertQuery = `
        INSERT INTO investment_criteria_central (
          entity_id,
          entity_type,
          capital_position,
          minimum_deal_size,
          maximum_deal_size,
          country,
          region,
          state,
          city,
          property_types,
          property_subcategories,
          strategies,
          decision_making_process,
          notes,
          investment_criteria_debt_id,
          investment_criteria_equity_id,
          created_at,
          updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, NOW(), NOW())
        RETURNING investment_criteria_id
      `

      const values = [
        contactId,
        'contact',
        criteria.capital_position,
        criteria.minimum_deal_size,
        criteria.maximum_deal_size,
        criteria.country,
        criteria.region,
        criteria.state,
        criteria.city,
        criteria.property_types,
        criteria.property_subcategories,
        criteria.strategies,
        criteria.decision_making_process,
        criteria.notes,
        debtId,  // Use original company debt ID unless modified
        equityId, // Use original company equity ID unless modified
      ]

      const result = await pool.query(insertQuery, values)
      
      results.push({
        original_criteria_id: criteria.investment_criteria_id,
        new_criteria_id: result.rows[0].investment_criteria_id,
        capital_position: criteria.capital_position
      })
    }

    return NextResponse.json({
      success: true,
      message: `Successfully copied ${results.length} investment criteria`,
      results
    })

  } catch (error) {
    console.error('Error copying investment criteria:', error)
    return NextResponse.json(
      { error: 'Failed to copy investment criteria', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
