import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

// GET /api/articles/[id]/entities - Get entities for an article
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const articleId = parseInt(params.id)
    if (isNaN(articleId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid article ID' },
        { status: 400 }
      )
    }

    const sql = `
      SELECT 
        article_entity_id,
        entity_type,
        entity_name,
        entity_role,
        created_at,
        updated_at
      FROM articles_entities
      WHERE article_id = $1
      ORDER BY created_at DESC
    `

    const result = await pool.query(sql, [articleId])

    return NextResponse.json({
      success: true,
      entities: result.rows
    })
  } catch (error) {
    console.error('Error fetching article entities:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch entities' },
      { status: 500 }
    )
  }
}

// PUT /api/articles/[id]/entities - Update entity matching
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const articleId = parseInt(params.id)
    if (isNaN(articleId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid article ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { entityId, matchedId, matchedType, matchConfidence } = body

    if (!entityId) {
      return NextResponse.json(
        { success: false, error: 'Entity ID is required' },
        { status: 400 }
      )
    }

    // Update the entity with matching information
    const sql = `
      UPDATE articles_entities
      SET 
        matched_id = $1,
        matched_type = $2,
        match_confidence = $3,
        updated_at = NOW()
      WHERE article_entity_id = $4 AND article_id = $5
    `

    await pool.query(sql, [matchedId, matchedType, matchConfidence, entityId, articleId])

    return NextResponse.json({
      success: true,
      message: 'Entity updated successfully'
    })
  } catch (error) {
    console.error('Error updating entity:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update entity' },
      { status: 500 }
    )
  }
}

// POST /api/articles/[id]/entities - Add new entity
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const articleId = parseInt(params.id)
    if (isNaN(articleId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid article ID' },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { entityType, entityName, entityRole } = body

    if (!entityType || !entityName || !entityRole) {
      return NextResponse.json(
        { success: false, error: 'Entity type, name, and role are required' },
        { status: 400 }
      )
    }

    const sql = `
      INSERT INTO articles_entities (
        article_id, entity_type, entity_name, entity_role, created_at, updated_at
      ) VALUES ($1, $2, $3, $4, NOW(), NOW())
      RETURNING article_entity_id
    `

    const result = await pool.query(sql, [articleId, entityType, entityName, entityRole])

    return NextResponse.json({
      success: true,
      entityId: result.rows[0].article_entity_id,
      message: 'Entity added successfully'
    })
  } catch (error) {
    console.error('Error adding entity:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to add entity' },
      { status: 500 }
    )
  }
}

// DELETE /api/articles/[id]/entities - Delete entity
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const articleId = parseInt(params.id)
    if (isNaN(articleId)) {
      return NextResponse.json(
        { success: false, error: 'Invalid article ID' },
        { status: 400 }
      )
    }

    const { searchParams } = new URL(request.url)
    const entityId = searchParams.get('entityId')

    if (!entityId) {
      return NextResponse.json(
        { success: false, error: 'Entity ID is required' },
        { status: 400 }
      )
    }

    const sql = `
      DELETE FROM articles_entities
      WHERE article_entity_id = $1 AND article_id = $2
    `

    await pool.query(sql, [entityId, articleId])

    return NextResponse.json({
      success: true,
      message: 'Entity deleted successfully'
    })
  } catch (error) {
    console.error('Error deleting entity:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete entity' },
      { status: 500 }
    )
  }
}
