import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  const articleId = parseInt(id)

  if (isNaN(articleId)) {
    return NextResponse.json(
      { error: 'Invalid article ID' },
      { status: 400 }
    )
  }

  try {
    // Get main article data
    const articleQuery = `
      SELECT 
        article_id,
        publication_name,
        article_url,
        scraping_source_type,
        headline,
        publication_date,
        author,
        summary,
        article_body_text,
        topic,
        market_trend_tags,
        is_distressed,
        sentiment,
        sentiment_summary,
        key_takeaways,
        llm_tags,
        quotes_llm_tags,
        source_confidence,
        extraction_notes,
        fetch_status,
        fetch_error,
        fetch_date,
        extraction_status,
        extraction_error,
        extraction_date,
        is_bad_url,
        is_relevant,
        created_at,
        updated_at
      FROM article 
      WHERE article_id = $1
    `
    
    const articleResult = await pool.query(articleQuery, [articleId])
    
    if (articleResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Article not found' },
        { status: 404 }
      )
    }

    const article = articleResult.rows[0]

    // Get article properties
    const propertiesQuery = `
      SELECT * FROM article_properties 
      WHERE article_id = $1 
      ORDER BY article_property_id
    `
    const propertiesResult = await pool.query(propertiesQuery, [articleId])

    // Get market metrics
    const metricsQuery = `
      SELECT * FROM article_market_metrics 
      WHERE article_id = $1 
      ORDER BY article_market_metric_id
    `
    const metricsResult = await pool.query(metricsQuery, [articleId])

    // Get transactions
    const transactionsQuery = `
      SELECT * FROM article_transactions 
      WHERE article_id = $1 
      ORDER BY article_transaction_id
    `
    const transactionsResult = await pool.query(transactionsQuery, [articleId])

    // Get entities
    const entitiesQuery = `
      SELECT * FROM articles_entities 
      WHERE article_id = $1 
      ORDER BY article_entity_id
    `
    const entitiesResult = await pool.query(entitiesQuery, [articleId])

    return NextResponse.json({
      article,
      properties: propertiesResult.rows,
      market_metrics: metricsResult.rows,
      transactions: transactionsResult.rows,
      entities: entitiesResult.rows
    })

  } catch (error) {
    console.error('Error fetching article details:', error)
    return NextResponse.json(
      { error: 'Failed to fetch article details' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  const articleId = parseInt(id)

  if (isNaN(articleId)) {
    return NextResponse.json(
      { error: 'Invalid article ID' },
      { status: 400 }
    )
  }

  try {
    const body = await request.json()
    
    // Handle specific actions
    if (body.action) {
      if (body.action === 'mark_bad_url') {
        const updateQuery = `
          UPDATE article 
          SET is_bad_url = true, updated_at = NOW() 
          WHERE article_id = $1
        `
        await pool.query(updateQuery, [articleId])
        
        return NextResponse.json({
          success: true,
          message: 'Article marked as bad URL'
        })
      }
      
      if (body.action === 'mark_relevant') {
        const updateQuery = `
          UPDATE article 
          SET is_relevant = $2, updated_at = NOW() 
          WHERE article_id = $1
        `
        await pool.query(updateQuery, [articleId, body.is_relevant])
        
        return NextResponse.json({
          success: true,
          message: `Article marked as ${body.is_relevant ? 'relevant' : 'not relevant'}`
        })
      }

      if (body.action === 'update_status') {
        const updateQuery = `
          UPDATE article 
          SET extraction_status = $2, updated_at = NOW() 
          WHERE article_id = $1
        `
        await pool.query(updateQuery, [articleId, body.status])
        
        return NextResponse.json({
          success: true,
          message: 'Article status updated'
        })
      }
    }

    // General update
    const updateFields: string[] = []
    const queryParams: any[] = []
    let paramCount = 0

    // List of updatable fields
    const updatableFields = [
      'headline', 'publication_date', 'author', 'summary', 'article_body_text',
      'topic', 'market_trend_tags', 'is_distressed', 'sentiment', 
      'sentiment_summary', 'key_takeaways', 'llm_tags', 'quotes_llm_tags',
      'source_confidence', 'extraction_notes', 'is_relevant'
    ]

    updatableFields.forEach(field => {
      if (body.hasOwnProperty(field)) {
        paramCount++
        updateFields.push(`${field} = $${paramCount}`)
        
        // Handle JSON fields
        if (['market_trend_tags', 'llm_tags', 'quotes_llm_tags'].includes(field)) {
          queryParams.push(JSON.stringify(body[field]))
        } else {
          queryParams.push(body[field])
        }
      }
    })

    if (updateFields.length === 0) {
      return NextResponse.json(
        { error: 'No valid fields to update' },
        { status: 400 }
      )
    }

    paramCount++
    queryParams.push(articleId)

    const updateQuery = `
      UPDATE article 
      SET ${updateFields.join(', ')}, updated_at = NOW() 
      WHERE article_id = $${paramCount}
    `

    await pool.query(updateQuery, queryParams)

    return NextResponse.json({
      success: true,
      message: 'Article updated successfully'
    })

  } catch (error) {
    console.error('Error updating article:', error)
    return NextResponse.json(
      { error: 'Failed to update article' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params
  const articleId = parseInt(id)

  if (isNaN(articleId)) {
    return NextResponse.json(
      { error: 'Invalid article ID' },
      { status: 400 }
    )
  }

  try {
    const deleteQuery = `DELETE FROM article WHERE article_id = $1`
    const result = await pool.query(deleteQuery, [articleId])

    if (result.rowCount === 0) {
      return NextResponse.json(
        { error: 'Article not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Article deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting article:', error)
    return NextResponse.json(
      { error: 'Failed to delete article' },
      { status: 500 }
    )
  }
}
