import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const page = parseInt(searchParams.get('page') || '1')
  const pageSize = Math.min(parseInt(searchParams.get('pageSize') || '20'), 100)
  const search = searchParams.get('search') || ''
  const source = searchParams.get('source') || ''
  const status = searchParams.get('status') || ''
  const sortBy = searchParams.get('sort') || 'created_desc'
  
  const offset = (page - 1) * pageSize

  try {
    // Build WHERE clause
    let whereConditions = ['(is_bad_url IS NULL OR is_bad_url != true)']
    const queryParams: any[] = []
    let paramCount = 0

    if (search) {
      paramCount++
      whereConditions.push(`(headline ILIKE $${paramCount} OR article_body_text ILIKE $${paramCount} OR publication_name ILIKE $${paramCount})`)
      queryParams.push(`%${search}%`)
    }

    if (source) {
      paramCount++
      whereConditions.push(`publication_name = $${paramCount}`)
      queryParams.push(source)
    }

    if (status) {
      if (status === 'processed') {
        whereConditions.push(`extraction_status = 'completed'`)
      } else if (status === 'pending') {
        whereConditions.push(`extraction_status IN ('pending', 'processing')`)
      } else if (status === 'failed') {
        whereConditions.push(`extraction_status = 'failed'`)
      }
    }

    const whereClause = whereConditions.join(' AND ')

    // Build ORDER BY clause
    let orderBy = 'created_at DESC'
    switch (sortBy) {
      case 'created_asc':
        orderBy = 'created_at ASC'
        break
      case 'updated_desc':
        orderBy = 'updated_at DESC'
        break
      case 'updated_asc':
        orderBy = 'updated_at ASC'
        break
      case 'publication_desc':
        orderBy = 'publication_date DESC, created_at DESC'
        break
      case 'publication_asc':
        orderBy = 'publication_date ASC, created_at ASC'
        break
      default:
        orderBy = 'created_at DESC'
    }

    // Get articles with pagination
    paramCount++
    const articlesQuery = `
      SELECT 
        article_id,
        publication_name,
        article_url,
        headline,
        publication_date,
        author,
        summary,
        topic,
        market_trend_tags,
        is_distressed,
        sentiment,
        sentiment_summary,
        key_takeaways,
        llm_tags,
        source_confidence,
        fetch_status,
        extraction_status,
        is_bad_url,
        is_relevant,
        created_at,
        updated_at,
        -- Count related data
        (SELECT COUNT(*) FROM article_properties WHERE article_id = article.article_id) as properties_count,
        (SELECT COUNT(*) FROM article_market_metrics WHERE article_id = article.article_id) as metrics_count,
        (SELECT COUNT(*) FROM article_transactions WHERE article_id = article.article_id) as transactions_count,
        (SELECT COUNT(*) FROM articles_entities WHERE article_id = article.article_id) as entities_count
      FROM article 
      WHERE ${whereClause}
      ORDER BY ${orderBy}
      LIMIT $${paramCount} OFFSET $${paramCount + 1}
    `
    
    queryParams.push(pageSize, offset)

    const articlesResult = await pool.query(articlesQuery, queryParams)

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM article WHERE ${whereClause}`
    const countResult = await pool.query(countQuery, queryParams.slice(0, -2)) // Remove limit/offset params
    const totalCount = parseInt(countResult.rows[0].count)

    // Get source statistics
    const sourceStatsQuery = `
      SELECT 
        publication_name,
        COUNT(*) as article_count,
        COUNT(CASE WHEN DATE(created_at) = CURRENT_DATE THEN 1 END) as today_count,
        COUNT(CASE WHEN extraction_status = 'completed' THEN 1 END) as processed_count,
        COUNT(CASE WHEN extraction_status IN ('pending', 'processing') THEN 1 END) as pending_count,
        COUNT(CASE WHEN extraction_status = 'failed' THEN 1 END) as failed_count
      FROM article 
      WHERE publication_name IS NOT NULL
        AND (is_bad_url IS NULL OR is_bad_url != true)
      GROUP BY publication_name 
      ORDER BY article_count DESC
    `
    const sourceStatsResult = await pool.query(sourceStatsQuery)

    // Get daily stats for the last 7 days
    const dailyStatsQuery = `
      SELECT 
        DATE(created_at) as date,
        publication_name,
        COUNT(*) as count
      FROM article 
      WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
        AND publication_name IS NOT NULL
        AND (is_bad_url IS NULL OR is_bad_url != true)
      GROUP BY DATE(created_at), publication_name 
      ORDER BY date DESC, publication_name
    `
    const dailyStatsResult = await pool.query(dailyStatsQuery)

    return NextResponse.json({
      articles: articlesResult.rows,
      total: totalCount,
      page,
      totalPages: Math.ceil(totalCount / pageSize),
      pageSize,
      sources: sourceStatsResult.rows,
      dailyStats: dailyStatsResult.rows
    })

  } catch (error) {
    console.error('Error fetching articles:', error)
    return NextResponse.json(
      { error: 'Failed to fetch articles' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      publication_name,
      article_url,
      headline,
      publication_date,
      author,
      summary,
      article_body_text,
      topic,
      market_trend_tags = [],
      is_distressed = false,
      sentiment,
      sentiment_summary,
      key_takeaways,
      llm_tags = [],
      source_confidence,
      scraping_source_type = 'Manual'
    } = body

    // Validate required fields
    if (!article_url) {
      return NextResponse.json(
        { error: 'Article URL is required' },
        { status: 400 }
      )
    }

    const insertQuery = `
      INSERT INTO article (
        publication_name, article_url, headline, publication_date, author,
        summary, article_body_text, topic, market_trend_tags, is_distressed,
        sentiment, sentiment_summary, key_takeaways, llm_tags, source_confidence,
        scraping_source_type
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16
      ) RETURNING article_id
    `

    const result = await pool.query(insertQuery, [
      publication_name,
      article_url,
      headline,
      publication_date,
      author,
      summary,
      article_body_text,
      topic,
      JSON.stringify(market_trend_tags),
      is_distressed,
      sentiment,
      sentiment_summary,
      key_takeaways,
      JSON.stringify(llm_tags),
      source_confidence,
      scraping_source_type
    ])

    return NextResponse.json({
      success: true,
      article_id: result.rows[0].article_id,
      message: 'Article created successfully'
    })

  } catch (error: any) {
    console.error('Error creating article:', error)
    
    // Handle unique constraint violation for article_url
    if (error.code === '23505' && error.constraint === 'unique_article_url') {
      return NextResponse.json(
        { error: 'Article URL already exists' },
        { status: 409 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to create article' },
      { status: 500 }
    )
  }
}
