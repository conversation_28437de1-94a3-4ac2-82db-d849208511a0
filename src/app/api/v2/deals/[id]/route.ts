import { NextRequest, NextResponse } from "next/server";
import { typeORMService } from "@/lib/typeorm/service";
import { withTypeOR<PERSON><PERSON>and<PERSON> } from "@/lib/typeorm/middleware";

/**
 * Determine if a capital position is required based on ask_capital_position
 */
function isCapitalPositionRequired(capitalPosition: string | null, askCapitalPosition: string[] | null): boolean {
  if (!capitalPosition || !askCapitalPosition || askCapitalPosition.length === 0) {
    return false;
  }
  
  return askCapitalPosition.includes(capitalPosition);
}

async function getDealDetailHandler(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params;
    const dealId = parseInt(id);
    console.log("Fetching deal v2 with ID:", dealId);

    if (isNaN(dealId)) {
      console.log("Invalid deal ID:", id);
      return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 });
    }

    // Get repositories from the service
    const dealsRepository = typeORMService.getDealsRepository();
    const nsfRepository = typeORMService.getNsfRepository();
    const investmentCriteriaDebtRepository = typeORMService.getInvestmentCriteriaDebtRepository();
    const investmentCriteriaEquityRepository = typeORMService.getInvestmentCriteriaEquityRepository();

    // Fetch the deal with all relations
    const deal = await dealsRepository.findOne({
      where: { dealId },
      relations: ['property', 'property.owner']
    });

    if (!deal) {
      console.log("No deal found with ID:", dealId);
      return NextResponse.json({ error: "Deal not found" }, { status: 404 });
    }

    // Fetch NSF fields data with proper capital position linking
    // Note: deal_type classification is based on pattern matching, not hardcoded values
    // The actual capital_position values come from the central mapping table
    const nsfFields = await typeORMService.getDataSource().query(`
      SELECT 
        nsf.*,
        CASE 
          WHEN nsf.source_type IS NOT NULL THEN 'sources'
          WHEN nsf.use_type IS NOT NULL THEN 'uses_total'
          ELSE 'sources'
        END as nsf_context,
        CASE 
          WHEN nsf.source_type IS NOT NULL THEN 
            CASE 
              WHEN LOWER(nsf.source_type) LIKE '%debt%' OR LOWER(nsf.source_type) LIKE '%loan%' THEN 'debt'
              WHEN LOWER(nsf.source_type) LIKE '%equity%' OR LOWER(nsf.source_type) LIKE '%partner%' THEN 'equity'
              ELSE 'other'
            END
          WHEN nsf.use_type = 'Acquisition' THEN 'acquisition'
          WHEN nsf.use_type = 'Hard Cost' THEN 'hard_cost'
          WHEN nsf.use_type = 'Soft Cost' THEN 'soft_cost'
          WHEN nsf.use_type = 'Financing Cost' THEN 'finance'
          ELSE 'other'
        END as deal_type,
        nsf.capital_position,
        nsf.source_type,
        nsf.use_type,
        nsf.amount,
        nsf.amount_per_gsf,
        nsf.amount_per_nsf,
        nsf.amount_per_zfa,
        nsf.percentage_of_total,
        -- Core NSF measurements moved to properties table
        -- nsf.gsf_gross_square_foot,
        -- nsf.zfa_zoning_floor_area,
        -- nsf.total_nsf_net_square_foot,
        nsf.additional_info,
        nsf.is_required
      FROM deal_nsf_fields nsf
      WHERE nsf.deal_id = $1
      ORDER BY nsf.deal_type ASC, nsf.nsf_context ASC, nsf.capital_position ASC
    `, [dealId]);

    // Core NSF measurements are now stored in the property table
    // NSF fields only contain amount calculations, not the base measurements
    const synchronizedNsfFields = nsfFields;

    // Fetch investment criteria data using raw SQL
    // Use 'deal_v2' entity type for V2 deals to distinguish from regular deals
    const investmentCriteriaResult = await typeORMService.getDataSource().query(`
      SELECT investment_criteria_id FROM investment_criteria_central 
      WHERE entity_type = 'deal_v2' AND entity_id = $1
      ORDER BY investment_criteria_id ASC
    `, [dealId.toString()]);

    let investmentCriteriaDebt: any[] = [];
    let investmentCriteriaEquity: any[] = [];

    if (investmentCriteriaResult.length > 0) {
      // Get ALL criteria IDs, not just the first one
      const criteriaIds = investmentCriteriaResult.map(row => row.investment_criteria_id);
      
      // Fetch debt criteria with capital position linking for ALL criteria
      investmentCriteriaDebt = await typeORMService.getDataSource().query(`
        SELECT 
          icd.investment_criteria_debt_id,
          icd.investment_criteria_id,
          icd.notes,
          icd.closing_time,
          icd.future_facilities,
          icd.eligible_borrower,
          icd.occupancy_requirements,
          icd.lien_position,
          icd.min_loan_dscr,
          icd.max_loan_dscr,
          icd.recourse_loan,
          icd.loan_min_debt_yield,
          icd.prepayment,
          icd.yield_maintenance,
          icd.application_deposit,
          icd.good_faith_deposit,
          icd.loan_origination_max_fee,
          icd.loan_origination_min_fee,
          icd.loan_exit_min_fee,
          icd.loan_exit_max_fee,
          icd.loan_interest_rate,
          icd.loan_interest_rate_based_off_sofr,
          icd.loan_interest_rate_based_off_wsj,
          icd.loan_interest_rate_based_off_prime,
          icd.loan_interest_rate_based_off_3yt,
          icd.loan_interest_rate_based_off_5yt,
          icd.loan_interest_rate_based_off_10yt,
          icd.loan_interest_rate_based_off_30yt,
          icd.rate_lock,
          icd.rate_type,
          icd.loan_to_value_max,
          icd.loan_to_value_min,
          icd.loan_to_cost_min,
          icd.loan_to_cost_max,
          icd.debt_program_overview,
          icd.loan_type,
          icd.structured_loan_tranche,
          icd.loan_program,
          icd.min_loan_term,
          icd.max_loan_term,
          icd.amortization,
          icd.created_at,
          icd.updated_at,
          icc.entity_id as deal_id,
          icc.entity_type,
          icc.capital_position
        FROM investment_criteria_debt icd
        JOIN investment_criteria_central icc ON icd.investment_criteria_id = icc.investment_criteria_id
        WHERE icc.investment_criteria_id = ANY($1)
        ORDER BY icd.created_at ASC
      `, [criteriaIds]);

      // Fetch equity criteria with capital position linking for ALL criteria
      investmentCriteriaEquity = await typeORMService.getDataSource().query(`
        SELECT 
          ice.investment_criteria_equity_id,
          ice.investment_criteria_id,
          ice.target_return,
          ice.minimum_internal_rate_of_return,
          ice.minimum_yield_on_cost,
          ice.minimum_equity_multiple,
          ice.target_cash_on_cash_min,
          ice.min_hold_period_years,
          ice.max_hold_period_years,
          ice.ownership_requirement,
          ice.attachment_point,
          ice.max_leverage_tolerance,
          ice.typical_closing_timeline_days,
          ice.proof_of_funds_requirement,
          ice.notes,
          ice.equity_program_overview,
          ice.occupancy_requirements,
          ice.yield_on_cost,
          ice.target_return_irr_on_equity,
          ice.equity_multiple,
          ice.position_specific_irr,
          ice.position_specific_equity_multiple,
          ice.created_at,
          ice.updated_at,
          icc.entity_id as deal_id,
          icc.entity_type,
          icc.capital_position
        FROM investment_criteria_equity ice
        JOIN investment_criteria_central icc ON ice.investment_criteria_id = icc.investment_criteria_id
        WHERE icc.investment_criteria_id = ANY($1)
        ORDER BY ice.created_at ASC
      `, [criteriaIds]);

      // Add capital position linking if not already present
      investmentCriteriaDebt = investmentCriteriaDebt.map(criteria => ({
        // Only include the essential ID fields and transformed camelCase fields
        investmentCriteriaDebtId: criteria.investment_criteria_debt_id,
        investmentCriteriaId: criteria.investment_criteria_id,
        capitalPosition: criteria.capital_position || null,
        loanType: criteria.loan_type || null,
        loanInterestRate: criteria.loan_interest_rate || null,
        loanInterestRateBasedOffSofr: criteria.loan_interest_rate_based_off_sofr || null,
        loanInterestRateBasedOffWsj: criteria.loan_interest_rate_based_off_wsj || null,
        loanInterestRateBasedOffPrime: criteria.loan_interest_rate_based_off_prime || null,
        loanInterestRateBasedOff3yt: criteria.loan_interest_rate_based_off_3yt || null,
        loanInterestRateBasedOff5yt: criteria.loan_interest_rate_based_off_5yt || null,
        loanInterestRateBasedOff10yt: criteria.loan_interest_rate_based_off_10yt || null,
        loanInterestRateBasedOff30yt: criteria.loan_interest_rate_based_off_30yt || null,
        rateLock: criteria.rate_lock || null,
        rateType: criteria.rate_type || null,
        minLoanTerm: criteria.min_loan_term || null,
        maxLoanTerm: criteria.max_loan_term || null,
        loanToValueMin: criteria.loan_to_value_min || null,
        loanToValueMax: criteria.loan_to_value_max || null,
        loanToCostMin: criteria.loan_to_cost_min || null,
        loanToCostMax: criteria.loan_to_cost_max || null,
        minLoanDscr: criteria.min_loan_dscr || null,
        maxLoanDscr: criteria.max_loan_dscr || null,
        loanMinDebtYield: criteria.loan_min_debt_yield || null,
        recourseLoan: criteria.recourse_loan || null,
        lienPosition: criteria.lien_position || null,
        amortization: criteria.amortization || null,
        prepayment: criteria.prepayment || null,
        yieldMaintenance: criteria.yield_maintenance || null,
        applicationDeposit: criteria.application_deposit || null,
        goodFaithDeposit: criteria.good_faith_deposit || null,
        loanOriginationMinFee: criteria.loan_origination_min_fee || null,
        loanOriginationMaxFee: criteria.loan_origination_max_fee || null,
        loanExitMinFee: criteria.loan_exit_min_fee || null,
        loanExitMaxFee: criteria.loan_exit_max_fee || null,
        closingTime: criteria.closing_time || null,
        futureFacilities: criteria.future_facilities || null,
        eligibleBorrower: criteria.eligible_borrower || null,
        occupancyRequirements: criteria.occupancy_requirements || null,
        debtProgramOverview: criteria.debt_program_overview || null,
        structuredLoanTranche: criteria.structured_loan_tranche || null,
        loanProgram: criteria.loan_program || null,
        notes: criteria.notes || null
      }));

      investmentCriteriaEquity = investmentCriteriaEquity.map(criteria => ({
        // Only include the essential ID fields and transformed camelCase fields
        investmentCriteriaEquityId: criteria.investment_criteria_equity_id,
        investmentCriteriaId: criteria.investment_criteria_id,
        capitalPosition: criteria.capital_position || null,
        equityType: criteria.equity_type || null,
        // Ensure all fields from the dynamic schema are included
        targetReturn: criteria.target_return || null,
        minimumInternalRateOfReturn: criteria.minimum_internal_rate_of_return || null,
        minimumYieldOnCost: criteria.minimum_yield_on_cost || null,
        minimumEquityMultiple: criteria.minimum_equity_multiple || null,
        targetCashOnCashMin: criteria.target_cash_on_cash_min || null,
        minHoldPeriodYears: criteria.min_hold_period_years || null,
        maxHoldPeriodYears: criteria.max_hold_period_years || null,
        ownershipRequirement: criteria.ownership_requirement || null,
        attachmentPoint: criteria.attachment_point || null,
        maxLeverageTolerance: criteria.max_leverage_tolerance || null,
        typicalClosingTimelineDays: criteria.typical_closing_timeline_days || null,
        proofOfFundsRequirement: criteria.proof_of_funds_requirement || null,
        equityProgramOverview: criteria.equity_program_overview || null,
        occupancyRequirements: criteria.occupancy_requirements || null,
        notes: criteria.notes || null,
        // NEW FIELDS - Moved from deal
        yieldOnCost: criteria.yield_on_cost || null,
        targetReturnIrrOnEquity: criteria.target_return_irr_on_equity || null,
        equityMultiple: criteria.equity_multiple || null,
        // NEW FIELDS - Position specific metrics
        positionSpecificIrr: criteria.position_specific_irr || null,
        positionSpecificEquityMultiple: criteria.position_specific_equity_multiple || null
      }));

      // Add logging to debug what's being returned
      console.log(`[API] Fetched ${investmentCriteriaDebt.length} debt criteria records`);
      console.log(`[API] Fetched ${investmentCriteriaEquity.length} equity criteria records`);
      console.log(`[API] Fetched ${nsfFields.length} NSF fields`);
      
      if (investmentCriteriaDebt.length > 0) {
        console.log(`[API] Debt criteria capital positions:`, investmentCriteriaDebt.map(d => d.capitalPosition));
      }
      if (investmentCriteriaEquity.length > 0) {
        console.log(`[API] Equity criteria capital positions:`, investmentCriteriaEquity.map(e => e.capitalPosition));
      }
      
      // Log NSF fields details
      console.log(`[API] NSF fields details:`, nsfFields.map(nsf => ({
        id: nsf.id,
        dealType: nsf.deal_type,
        nsfContext: nsf.nsf_context,
        capitalPosition: nsf.capital_position,
        amount: nsf.amount
      })));
    } else {
      console.log(`[API] No investment criteria found for deal ${dealId}`);
    }

    // Add computed fields for better UX
    const dealWithComputed = {
      ...deal,
      nsf_fields: synchronizedNsfFields,
      // Add camelCase version for frontend compatibility
      nsfFields: synchronizedNsfFields.map(nsf => {
        // Core NSF measurements are now stored in the property table
        // NSF fields only contain amount calculations, not the base measurements
        const synchronizedTotalNsf = 0; // Not applicable anymore
        
        return {
          id: nsf.id,
          dealId: nsf.deal_id,
          capitalPosition: nsf.capital_position,
          sourceType: nsf.source_type,
          useType: nsf.use_type,
          amount: nsf.amount,
          amountPerGsf: nsf.amount_per_gsf,
          amountPerNsf: nsf.amount_per_nsf,
          amountPerZfa: nsf.amount_per_zfa,
          percentageOfTotal: nsf.percentage_of_total,
          // Core NSF measurements moved to properties table
          // gsfGrossSquareFoot: nsf.gsf_gross_square_foot,
          // zfaZoningFloorArea: nsf.zfa_zoning_floor_area,
          // totalNsfNetSquareFoot: synchronizedTotalNsf,
          additionalInfo: nsf.additional_info,
          isRequired: nsf.is_required,
          dealType: nsf.deal_type,
          nsfContext: nsf.nsf_context
        };
      }),
      investmentCriteriaDebt: investmentCriteriaDebt,
      investmentCriteriaEquity: investmentCriteriaEquity,
      // Add computed percentages if we have financial data
      computed_fields: {
        total_nsf: (deal.property as any)?.totalNsfNetSquareFoot || 0,
        total_gsf: (deal.property as any)?.gsfGrossSquareFoot || 0,
        total_zfa: (deal.property as any)?.zfaZoningFloorArea || 0
      }
    };

    // Calculate data quality metrics using comprehensive calculator
    const { calculateComprehensiveQuality } = await import('@/lib/utils/comprehensiveQualityCalculator');
    const qualityMetrics = calculateComprehensiveQuality(dealWithComputed as any);
    (dealWithComputed as any).data_quality_metrics = qualityMetrics;

    return NextResponse.json({
      success: true,
      deal: dealWithComputed,
      message: "Deal v2 data retrieved successfully"
    });

  } catch (error) {
    console.error("Error fetching deal v2:", error);
    return NextResponse.json(
      { 
        error: "Failed to fetch deal v2",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// Note: Data quality metrics are now calculated using the comprehensive quality calculator
// which provides detailed breakdowns for all sections (overview, debt, equity, nsf, property, financial, units, campaign)

// Update deal handler with comprehensive investment criteria and property support
async function updateDealHandler(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params;
    const dealId = parseInt(id);
    const body = await request.json();
    
    console.log("Updating deal v2 with ID:", dealId, "Body:", JSON.stringify(body, null, 2));

    if (isNaN(dealId)) {
      return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 });
    }

    // Validate payload structure
    if (!body || typeof body !== 'object') {
      return NextResponse.json({ error: "Invalid request body" }, { status: 400 });
    }

    // Log what sections are being updated
    const updateSections: string[] = [];
    if (body.property) updateSections.push('property');
    if (body.nsfFields) updateSections.push('nsfFields');
    if (body.investmentCriteriaDebt) updateSections.push('investmentCriteriaDebt');
    if (body.investmentCriteriaEquity) updateSections.push('investmentCriteriaEquity');
    if (body.investmentCriteria) updateSections.push('investmentCriteria');
    
    console.log(`[API] Update sections detected:`, updateSections);
    
    // Log unit count fields that are being updated
    const unitCountFields = [
      'numAffordableHousing1bedroomUnits', 'numAffordableHousing2bedroomUnits',
      'numAffordableHousing3bedroomUnits', 'numAffordableHousingStudiosUnits',
      'numMarketRate1bedroomUnits', 'numMarketRate2bedroomUnits',
      'numMarketRate3bedroomUnits', 'numMarketRateStudiosUnits',
      'totalNumAffordableHousingUnits', 'totalNumMarketRateUnits'
    ];
    
    const providedUnitCountFields = unitCountFields.filter(field => body[field] !== undefined);
    if (providedUnitCountFields.length > 0) {
      console.log(`[API] Unit count fields to update:`, providedUnitCountFields);
      providedUnitCountFields.forEach(field => {
        console.log(`  - ${field}: ${body[field]} (type: ${typeof body[field]})`);
      });
    } else {
      console.log(`[API] No unit count fields found in payload`);
    }
    
    // Log all fields in the payload for debugging
    console.log(`[API] All fields in payload:`, Object.keys(body));
    console.log(`[API] Payload size:`, JSON.stringify(body).length, 'characters');

    // Get repositories
    const dealsRepository = typeORMService.getDealsRepository();
    const nsfRepository = typeORMService.getNsfRepository();
    const propertyRepository = typeORMService.getPropertyRepository();
    const ownerRepository = typeORMService.getOwnerRepository();
    const investmentCriteriaDebtRepository = typeORMService.getInvestmentCriteriaDebtRepository();
    const investmentCriteriaEquityRepository = typeORMService.getInvestmentCriteriaEquityRepository();

    // Start transaction
    const queryRunner = typeORMService.getDataSource().createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Update main deal fields
      const dealUpdateData: any = {};
              const dealFields = [
          'dealName', 'summary', 'dealType', 'askCapitalPosition', 'askAmount', 'capitalRaiseTimeline',
        'dateReceived', 'dealStage', 'dateClosed', 'dateUnderContract', 'strategy', 'holdPeriod',
        'dealStatus', 'totalInternalRateOfReturnIrr', 'totalEquityMultiple', 'totalNsfNetSquareFoot',
        'purchasePrice', 'totalProjectCost',
        'residentialNsfNetSquareFoot', 'retailNsfNetSquareFoot', 'occupancyRate',
        'communityFacilityNsfNetSquareFoot', 'officeNsfNetSquareFoot', 'numApartmentUnits',
        'closingTime', 'hotelKeys', 'parkingSf', 'parkingSpots', 'totalNumAffordableHousingUnits',
        'totalNumMarketRateUnits', 'lienPosition', 'structuredLienTranche', 'loanAmount',
        'interestRate', 'loanInterestRateBasedOffSofr', 'loanInterestRateBasedOffWsj',
        'loanInterestRateBasedOffPrime', 'loanInterestRateBasedOff3yt', 'loanInterestRateBasedOff5yt',
        'loanInterestRateBasedOff10yt', 'loanInterestRateBasedOff30yt', 'loanTerm', 'loanToCostLtc',
        'loanToValueLtv', 'loanType', 'dscr', 'exitCapRate', 'recourse',
        // Unit count fields for affordable housing
        'numAffordableHousing1bedroomUnits', 'numAffordableHousing2bedroomUnits', 
        'numAffordableHousing3bedroomUnits', 'numAffordableHousingStudiosUnits',
        // Unit count fields for market rate
        'numMarketRate1bedroomUnits', 'numMarketRate2bedroomUnits', 
        'numMarketRate3bedroomUnits', 'numMarketRateStudiosUnits',
        // Additional unit count fields
        'totalNumAffordableHousingUnits', 'totalNumMarketRateUnits',
        // Additional fields from the payload
        'affordableHousingRent1bedroomUnit', 'affordableHousingRent2bedroomUnit',
        'affordableHousingRent3bedroomUnit', 'affordableHousingRentStudioUnit',
        'affordableHousingSale1bedroomUnit', 'affordableHousingSale2bedroomUnit',
        'affordableHousingSale3bedroomUnit', 'affordableHousingSaleStudioUnit',
        'marketRateRent1bedroomUnit', 'marketRateRent2bedroomUnit',
        'marketRateRent3bedroomUnit', 'marketRateSale1bedroomUnit',
        'marketRateSale2bedroomUnit', 'marketRateSale3bedroomUnit',
        'marketRateSaleStudioUnit', 'communityFacilityRent',
        'communityFacilityRentAdditional', 'communityFacilitySalePrice',
        'communityFacilitySalePriceAdditional', 'costPerTotalNsfNetSquareFoot',
        'costPerZoningFloorArea',         'seniorDebtCostGsfGrossSquareFoot',
        'seniorDebtCostHotelKeys', 'seniorDebtCostPerApartmentUnits',
        'seniorDebtNsfNetSquareFoot', 'seniorDebtZoningFloorArea',
        // Additional fields that might be present
        'yieldOnCost', 'commonEquityInternalRateOfReturnIrr', 'commonEquityEquityMultiple',
        'gpEquityMultiple', 'gpInternalRateOfReturnIrr', 'lpEquityMultiple', 'lpInternalRateOfReturnIrr',
        'preferredEquityInternalRateOfReturnIrr', 'preferredEquityEquityMultiple',
        'takeoutLoanAmount', 'takeoutLoanInterestRate', 'takeoutLoanTerm', 'takeoutLoanDscr',
        'takeoutLoanLoanToCostLtc', 'takeoutLoanLoanToValueLtv', 'takeoutLoanDebtYield',
        'takeoutLoanIoPeriod', 'takeoutLoanPerNsf', 'takeoutLoanPosition', 'takeoutLoanType',
        'takeoutLoanAmortization', 'takeoutLoanInterestRateBasedOffSofr', 'takeoutLoanInterestRateBasedOffWsj',
        'takeoutLoanInterestRateBasedOffPrime', 'takeoutLoanInterestRateBasedOff3yt', 'takeoutLoanInterestRateBasedOff5yt',
        'takeoutLoanInterestRateBasedOff10yt', 'takeoutLoanInterestRateBasedOff30yt'
      ];

      dealFields.forEach(field => {
        if (body[field] !== undefined) {
          let value = body[field];
          
          // Handle special data type conversions for numeric fields
          if (['numAffordableHousing1bedroomUnits', 'numAffordableHousing2bedroomUnits', 
               'numAffordableHousing3bedroomUnits', 'numAffordableHousingStudiosUnits',
               'numMarketRate1bedroomUnits', 'numMarketRate2bedroomUnits', 
               'numMarketRate3bedroomUnits', 'numMarketRateStudiosUnits',
               'totalNumAffordableHousingUnits', 'totalNumMarketRateUnits',
               'numApartmentUnits', 'hotelKeys', 'parkingSf', 'parkingSpots',
               'holdPeriod', 'loanTerm', 'yearBuilt', 'yearRenovated',
               'numberOfUnits', 'takeoutLoanTerm', 'takeoutLoanIoPeriod'].includes(field)) {
            if (typeof value === 'string' && value.trim() !== '') {
              value = parseInt(value);
              if (isNaN(value)) value = null;
            } else if (value === '') {
              value = null;
            }
          }
          
          // Handle numeric fields that should be floats
          if (['totalNsfNetSquareFoot', 'residentialNsfNetSquareFoot', 'retailNsfNetSquareFoot',
               'officeNsfNetSquareFoot', 'communityFacilityNsfNetSquareFoot', 'parkingSf',
               'totalInternalRateOfReturnIrr', 'totalEquityMultiple', 'yieldOnCost',
               'purchasePrice', 'totalProjectCost',
               'commonEquityInternalRateOfReturnIrr', 'commonEquityEquityMultiple',
               'gpEquityMultiple', 'gpInternalRateOfReturnIrr', 'lpEquityMultiple', 'lpInternalRateOfReturnIrr',
               'preferredEquityInternalRateOfReturnIrr', 'preferredEquityEquityMultiple',
               'interestRate', 'loanToCostLtc', 'loanToValueLtv', 'dscr', 'exitCapRate',
               'occupancyRate', 'affordableHousingRent1bedroomUnit', 'affordableHousingRent2bedroomUnit',
               'affordableHousingRent3bedroomUnit', 'affordableHousingRentStudioUnit',
               'affordableHousingSale1bedroomUnit', 'affordableHousingSale2bedroomUnit',
               'affordableHousingSale3bedroomUnit', 'affordableHousingSaleStudioUnit',
               'marketRateRent1bedroomUnit', 'marketRateRent2bedroomUnit',
               'marketRateRent3bedroomUnit', 'marketRateSale1bedroomUnit',
               'marketRateSale2bedroomUnit', 'marketRateSale3bedroomUnit',
               'marketRateSaleStudioUnit', 'communityFacilityRent', 'communityFacilityRentAdditional',
               'communityFacilitySalePrice', 'communityFacilitySalePriceAdditional',
               'costPerTotalNsfNetSquareFoot', 'costPerZoningFloorArea',
               'seniorDebtCostGsfGrossSquareFoot', 'seniorDebtCostHotelKeys',
               'seniorDebtCostPerApartmentUnits', 'seniorDebtNsfNetSquareFoot',
               'seniorDebtZoningFloorArea', 'takeoutLoanAmount', 'takeoutLoanInterestRate',
               'takeoutLoanDscr', 'takeoutLoanLoanToCostLtc', 'takeoutLoanLoanToValueLtv',
               'takeoutLoanDebtYield', 'takeoutLoanPerNsf'].includes(field)) {
            if (typeof value === 'string' && value.trim() !== '') {
              value = parseFloat(value);
              if (isNaN(value)) value = null;
            } else if (value === '') {
              value = null;
            }
          }
          
          dealUpdateData[field] = value;
        }
      });
      
      // Validate that unit count fields are properly converted
      const unitCountValidationFields = [
        'numAffordableHousing1bedroomUnits', 'numAffordableHousing2bedroomUnits',
        'numAffordableHousing3bedroomUnits', 'numAffordableHousingStudiosUnits',
        'numMarketRate1bedroomUnits', 'numMarketRate2bedroomUnits',
        'numMarketRate3bedroomUnits', 'numMarketRateStudiosUnits'
      ];
      
      unitCountValidationFields.forEach(field => {
        if (dealUpdateData[field] !== undefined) {
          console.log(`[API] Unit count validation - ${field}:`, {
            original: body[field],
            converted: dealUpdateData[field],
            type: typeof dealUpdateData[field],
            isInteger: Number.isInteger(dealUpdateData[field]),
            isNull: dealUpdateData[field] === null,
            isUndefined: dealUpdateData[field] === undefined
          });
          
          // Warn if conversion failed
          if (typeof dealUpdateData[field] === 'string' && dealUpdateData[field] !== null) {
            console.warn(`[API] Warning: ${field} is still a string after conversion: "${dealUpdateData[field]}"`);
          }
        }
      });

      // Handle askCapitalPosition array properly
      if (body.askCapitalPosition) {
        dealUpdateData.askCapitalPosition = Array.isArray(body.askCapitalPosition) 
          ? body.askCapitalPosition 
          : [body.askCapitalPosition];
      }

      // Update the deal
      try {
        console.log(`[API] Attempting to update deal ${dealId} with ${Object.keys(dealUpdateData).length} fields`);
        const updateResult = await dealsRepository.update(dealId, dealUpdateData);
        console.log(`[API] Deal update result:`, updateResult);
        
        // Verify the update was successful
        if (updateResult.affected === 0) {
          console.warn(`[API] Warning: Deal update affected 0 rows. Deal ID ${dealId} may not exist.`);
        } else {
          console.log(`[API] Successfully updated deal ${dealId}. Rows affected: ${updateResult.affected}`);
        }
      } catch (error) {
        console.error(`[API] Error updating deal ${dealId}:`, error);
        throw new Error(`Failed to update deal: ${error instanceof Error ? error.message : String(error)}`);
      }
      
      // Log what was actually updated
      console.log(`[API] Deal update summary for deal ${dealId}:`);
      console.log(`  - Fields updated:`, Object.keys(dealUpdateData));
      console.log(`  - Total fields:`, Object.keys(dealUpdateData).length);
      
      // Log specific unit count updates if any
      const updatedUnitCountFields = Object.keys(dealUpdateData).filter(field => 
        field.includes('numAffordableHousing') || field.includes('numMarketRate') || field.includes('totalNum')
      );
      if (updatedUnitCountFields.length > 0) {
        console.log(`  - Unit count fields updated:`, updatedUnitCountFields);
        updatedUnitCountFields.forEach(field => {
          console.log(`    - ${field}: ${dealUpdateData[field]} (type: ${typeof dealUpdateData[field]})`);
        });
      }
      
      // Log the raw update data for debugging
      console.log(`[API] Raw deal update data:`, JSON.stringify(dealUpdateData, null, 2));

      // Update property fields if provided
      if (body.property && typeof body.property === 'object') {
        console.log(`[API] Processing property update for deal ${dealId}`);
        
        // Validate property data structure
        const validPropertyFields = [
          'address', 'city', 'state', 'zipcode', 'region', 'country', 'market', 'submarket', 'neighborhood',
          'propertyType', 'subpropertyType', 'buildingSqft', 'landAcres', 'lotArea', 'yearBuilt', 'yearRenovated',
          'latitude', 'longitude', 'propertyStatus', 'numberOfUnits', 'appraisalValue', 'appraisalValueDate',
          'gsfGrossSquareFoot', 'zfaZoningFloorArea', 'totalNsfNetSquareFoot', 'far', 'historicalOccupancyTrend', 'environmentalRiskScore',
          'propertyDescription', 'floorAreaRatio', 'zoningSquareFootage', 'owner'
        ];
        
        const providedPropertyFields = Object.keys(body.property);
        const invalidPropertyFields = providedPropertyFields.filter(field => !validPropertyFields.includes(field));
        
        if (invalidPropertyFields.length > 0) {
          console.warn(`[API] Warning: Invalid property fields provided:`, invalidPropertyFields);
        }
        
        console.log(`[API] Property fields to process:`, providedPropertyFields.filter(field => validPropertyFields.includes(field)));
        
        // Get the current deal to find the property ID
        const currentDeal = await dealsRepository.findOne({
          where: { dealId },
          relations: ['property']
        });

        if (currentDeal?.property && (currentDeal.property as any)?.propertyId) {
          try {
            // Update existing property
            const propertyUpdateData: any = {};
            const propertyFields = [
              'address', 'city', 'state', 'zipcode', 'region', 'country', 'market', 'submarket', 'neighborhood',
              'propertyType', 'subpropertyType', 'buildingSqft', 'landAcres', 'lotArea', 'yearBuilt', 'yearRenovated',
              'latitude', 'longitude', 'propertyStatus', 'numberOfUnits', 'appraisalValue', 'appraisalValueDate',
              'gsfGrossSquareFoot', 'zfaZoningFloorArea', 'totalNsfNetSquareFoot', 'far', 'historicalOccupancyTrend', 'environmentalRiskScore',
              'propertyDescription', 'floorAreaRatio', 'zoningSquareFootage'
            ];

            propertyFields.forEach(field => {
              if (body.property[field] !== undefined) {
                // Handle special data type conversions
                let value = body.property[field];
                
                // Convert numeric fields
                if (['buildingSqft', 'landAcres', 'lotArea', 'yearBuilt', 'yearRenovated', 'latitude', 'longitude', 
                     'numberOfUnits', 'appraisalValue', 'gsfGrossSquareFoot', 'zfaZoningFloorArea', 'totalNsfNetSquareFoot', 'far', 
                     'environmentalRiskScore', 'floorAreaRatio', 'zoningSquareFootage'].includes(field)) {
                  if (typeof value === 'string' && value.trim() !== '') {
                    value = parseFloat(value);
                    if (isNaN(value)) value = null;
                  } else if (value === '') {
                    value = null;
                  }
                }
                
                // Convert integer fields
                if (['yearBuilt', 'yearRenovated', 'numberOfUnits'].includes(field)) {
                  if (typeof value === 'string' && value.trim() !== '') {
                    value = parseInt(value);
                    if (isNaN(value)) value = null;
                  } else if (value === '') {
                    value = null;
                  }
                }
                
                propertyUpdateData[field] = value;
              }
            });

            // Handle special date field
            if (body.property.appraisalValueDate) {
              propertyUpdateData.appraisalValueDate = new Date(body.property.appraisalValueDate);
            }

            // Update the property - cast to any to avoid TypeScript issues
            const propertyId = (currentDeal.property as any).propertyId;
            await propertyRepository.update(propertyId, propertyUpdateData);
            console.log(`[API] Updated existing property ${propertyId} with fields:`, Object.keys(propertyUpdateData));
          } catch (error) {
            console.error(`[API] Error updating property:`, error);
            throw new Error(`Failed to update property: ${error instanceof Error ? error.message : String(error)}`);
          }
        } else {
          try {
            // Create new property if it doesn't exist
            console.log(`[API] Creating new property for deal ${dealId}`);
            
            const propertyData: any = {};
            
            // Process each property field with proper data type conversion
            const propertyFields = [
              'address', 'city', 'state', 'zipcode', 'region', 'country', 'market', 'submarket', 'neighborhood',
              'propertyType', 'subpropertyType', 'buildingSqft', 'landAcres', 'lotArea', 'yearBuilt', 'yearRenovated',
              'latitude', 'longitude', 'propertyStatus', 'numberOfUnits', 'appraisalValue', 'appraisalValueDate',
              'gsfGrossSquareFoot', 'zfaZoningFloorArea', 'totalNsfNetSquareFoot', 'far', 'historicalOccupancyTrend', 'environmentalRiskScore',
              'propertyDescription', 'floorAreaRatio', 'zoningSquareFootage'
            ];
            
            propertyFields.forEach(field => {
              if (body.property[field] !== undefined) {
                let value = body.property[field];
                
                // Handle special date field
                if (field === 'appraisalValueDate' && value) {
                  propertyData[field] = new Date(value);
                }
                // Convert numeric fields
                else if (['buildingSqft', 'landAcres', 'lotArea', 'yearBuilt', 'yearRenovated', 'latitude', 'longitude', 
                         'numberOfUnits', 'appraisalValue', 'gsfGrossSquareFoot', 'zfaZoningFloorArea', 'totalNsfNetSquareFoot', 'far', 
                         'environmentalRiskScore', 'floorAreaRatio', 'zoningSquareFootage'].includes(field)) {
                  if (typeof value === 'string' && value.trim() !== '') {
                    value = parseFloat(value);
                    if (isNaN(value)) value = null;
                  } else if (value === '') {
                    value = null;
                  }
                  propertyData[field] = value;
                }
                // Convert integer fields
                else if (['yearBuilt', 'yearRenovated', 'numberOfUnits'].includes(field)) {
                  if (typeof value === 'string' && value.trim() !== '') {
                    value = parseInt(value);
                    if (isNaN(value)) value = null;
                  } else if (value === '') {
                    value = null;
                  }
                  propertyData[field] = value;
                }
                // Handle other fields
                else {
                  propertyData[field] = value;
                }
              }
            });
            
            // Remove owner data from property creation (will be handled separately)
            delete propertyData.owner;
            
            const newProperty = propertyRepository.create(propertyData);
            const savedProperty = await propertyRepository.save(newProperty);
            
            // Update the deal to reference the new property - cast to any to avoid TypeScript issues
            const propertyId = (savedProperty as any).propertyId;
            await dealsRepository.update(dealId, { propertyId });
            console.log(`[API] Created new property ${propertyId} and linked to deal ${dealId}`);
            
            // If owner data was provided, create/update the owner and link it to the new property
            if (body.property.owner && typeof body.property.owner === 'object') {
              console.log(`[API] Creating new owner for new property ${propertyId}`);
              const newOwner = ownerRepository.create(body.property.owner);
              const savedOwner = await ownerRepository.save(newOwner);
              
              // Link the new owner to the new property
              await propertyRepository.update(propertyId, { ownerId: (savedOwner as any).ownerId });
              console.log(`[API] Created new owner ${(savedOwner as any).ownerId} and linked to new property ${propertyId}`);
            }
          } catch (error) {
            console.error(`[API] Error creating property:`, error);
            throw new Error(`Failed to create property: ${error instanceof Error ? error.message : String(error)}`);
          }
        }
      }

      // Update owner fields if provided
      if (body.property?.owner && typeof body.property.owner === 'object') {
        console.log(`[API] Processing owner update for deal ${dealId}`);
        
        // Validate owner data structure
        const validOwnerFields = [
          'firstName', 'lastName', 'ownerName', 'entityType', 'contactPerson', 'phoneNumber', 
          'additionalPhoneNumber', 'emailAddress', 'additionalEmailAddress', 'mailingAddress', 
          'mailingCity', 'mailingState', 'mailingZipcode'
        ];
        
        const providedOwnerFields = Object.keys(body.property.owner);
        const invalidOwnerFields = providedOwnerFields.filter(field => !validOwnerFields.includes(field));
        
        if (invalidOwnerFields.length > 0) {
          console.warn(`[API] Warning: Invalid owner fields provided:`, invalidOwnerFields);
        }
        
        console.log(`[API] Owner fields to process:`, providedOwnerFields.filter(field => validOwnerFields.includes(field)));
        
        // Get the current deal to find the property and owner ID
        const currentDeal = await dealsRepository.findOne({
          where: { dealId },
          relations: ['property', 'property.owner']
        });

        if (currentDeal?.property && (currentDeal.property as any)?.owner?.ownerId) {
          try {
            // Update existing owner
            console.log(`[API] Updating existing owner for property ${(currentDeal.property as any).propertyId}`);
            
            const ownerUpdateData: any = {};
            const ownerFields = [
              'firstName', 'lastName', 'ownerName', 'entityType', 'contactPerson', 'phoneNumber', 
              'additionalPhoneNumber', 'emailAddress', 'additionalEmailAddress', 'mailingAddress', 
              'mailingCity', 'mailingState', 'mailingZipcode'
            ];

            ownerFields.forEach(field => {
              if (body.property.owner[field] !== undefined) {
                let value = body.property.owner[field];
                
                // Convert empty strings to null for better database consistency
                if (typeof value === 'string' && value.trim() === '') {
                  value = null;
                }
                
                ownerUpdateData[field] = value;
              }
            });

            // Update the owner - cast to any to avoid TypeScript issues
            const ownerId = (currentDeal.property as any).owner.ownerId;
            await ownerRepository.update(ownerId, ownerUpdateData);
            console.log(`[API] Updated existing owner ${ownerId} with fields:`, Object.keys(ownerUpdateData));
          } catch (error) {
            console.error(`[API] Error updating owner:`, error);
            throw new Error(`Failed to update owner: ${error instanceof Error ? error.message : String(error)}`);
          }
        } else if (currentDeal?.property && (currentDeal.property as any)?.propertyId) {
          try {
            // Property exists but no owner - create new owner and link it
            console.log(`[API] Creating new owner for existing property ${(currentDeal.property as any).propertyId}`);
            
            const newOwner = ownerRepository.create(body.property.owner);
            const savedOwner = await ownerRepository.save(newOwner);
            
            // Update the property to reference the new owner
            const propertyId = (currentDeal.property as any).propertyId;
            await propertyRepository.update(propertyId, { ownerId: (savedOwner as any).ownerId });
            console.log(`[API] Created new owner ${(savedOwner as any).ownerId} and linked to existing property ${propertyId}`);
          } catch (error) {
            console.error(`[API] Error creating owner for existing property:`, error);
            throw new Error(`Failed to create owner for existing property: ${error instanceof Error ? error.message : String(error)}`);
          }
        } else {
          console.log(`[API] No property found for deal ${dealId}, owner creation will be handled in property creation section`);
        }
        // Note: If no property exists, owner creation is handled in the property creation section above
      }

      // Log summary of property and owner updates
      if (body.property) {
        console.log(`[API] Property update summary for deal ${dealId}:`);
        if (body.property.address) console.log(`  - Address: ${body.property.address}`);
        if (body.property.city) console.log(`  - City: ${body.property.city}`);
        if (body.property.state) console.log(`  - State: ${body.property.state}`);
        if (body.property.propertyType) console.log(`  - Property Type: ${body.property.propertyType}`);
        if (body.property.owner) console.log(`  - Owner data included`);
      }
      
      // Log final summary of all updates
      console.log(`[API] Final update summary for deal ${dealId}:`);
      console.log(`  - Deal fields updated: ${Object.keys(dealUpdateData).length}`);
      console.log(`  - Property updated: ${!!body.property}`);
      console.log(`  - Owner updated: ${!!(body.property?.owner)}`);
      console.log(`  - NSF fields updated: ${body.nsfFields ? body.nsfFields.length : 0}`);
      console.log(`  - Investment criteria debt updated: ${body.investmentCriteriaDebt ? body.investmentCriteriaDebt.length : 0}`);
      console.log(`  - Investment criteria equity updated: ${body.investmentCriteriaEquity ? body.investmentCriteriaEquity.length : 0}`);

      // Update NSF fields if provided
      if (body.nsfFields && Array.isArray(body.nsfFields)) {
        for (const nsfField of body.nsfFields) {
          if (nsfField.id) {
            await nsfRepository.update(nsfField.id, {
              nsfContext: nsfField.nsfContext,
              capitalPosition: nsfField.capitalPosition,
              sourceType: nsfField.sourceType,
              useType: nsfField.useType,
              isRequired: nsfField.isRequired,
              // Core NSF measurements moved to properties table
              // gsfGrossSquareFoot: nsfField.gsfGrossSquareFoot,
              // zfaZoningFloorArea: nsfField.zfaZoningFloorArea,
              // totalNsfNetSquareFoot: nsfField.totalNsfNetSquareFoot,
              amount: nsfField.amount,
              amountPerGsf: nsfField.amountPerGsf,
              amountPerNsf: nsfField.amountPerNsf,
              amountPerZfa: nsfField.amountPerZfa,
              percentageOfTotal: nsfField.percentageOfTotal,
              additionalInfo: nsfField.additionalInfo
            });
          }
        }
      }

      // Update investment criteria debt if provided
      if (body.investmentCriteriaDebt && Array.isArray(body.investmentCriteriaDebt)) {
        for (const debtCriteria of body.investmentCriteriaDebt) {
          if (debtCriteria.investmentCriteriaDebtId) {
            await investmentCriteriaDebtRepository.update(debtCriteria.investmentCriteriaDebtId, {
              notes: debtCriteria.notes,
              closingTime: debtCriteria.closingTime,
              futureFacilities: debtCriteria.futureFacilities,
              eligibleBorrower: debtCriteria.eligibleBorrower,
              occupancyRequirements: debtCriteria.occupancyRequirements,
              lienPosition: debtCriteria.lienPosition,
              minLoanDscr: debtCriteria.minLoanDscr,
              maxLoanDscr: debtCriteria.maxLoanDscr,
              recourseLoan: debtCriteria.recourseLoan,
              loanMinDebtYield: debtCriteria.loanMinDebtYield,
              prepayment: debtCriteria.prepayment,
              yieldMaintenance: debtCriteria.yieldMaintenance,
              applicationDeposit: debtCriteria.applicationDeposit,
              goodFaithDeposit: debtCriteria.goodFaithDeposit,
              loanOriginationMaxFee: debtCriteria.loanOriginationMaxFee,
              loanOriginationMinFee: debtCriteria.loanOriginationMinFee,
              loanExitMinFee: debtCriteria.loanExitMinFee,
              loanExitMaxFee: debtCriteria.loanExitMaxFee,
              loanInterestRate: debtCriteria.loanInterestRate,
              loanInterestRateBasedOffSofr: debtCriteria.loanInterestRateBasedOffSofr,
              loanInterestRateBasedOffWsj: debtCriteria.loanInterestRateBasedOffWsj,
              loanInterestRateBasedOffPrime: debtCriteria.loanInterestRateBasedOffPrime,
              loanInterestRateBasedOff3yt: debtCriteria.loanInterestRateBasedOff3yt,
              loanInterestRateBasedOff5yt: debtCriteria.loanInterestRateBasedOff5yt,
              loanInterestRateBasedOff10yt: debtCriteria.loanInterestRateBasedOff10yt,
              loanInterestRateBasedOff30yt: debtCriteria.loanInterestRateBasedOff30yt,
              rateLock: debtCriteria.rateLock,
              rateType: debtCriteria.rateType,
              loanToValueMax: debtCriteria.loanToValueMax,
              loanToValueMin: debtCriteria.loanToValueMin,
              loanToCostMin: debtCriteria.loanToCostMin,
              loanToCostMax: debtCriteria.loanToCostMax,
              debtProgramOverview: debtCriteria.debtProgramOverview,
              loanType: debtCriteria.loanType,
              structuredLoanTranche: debtCriteria.structuredLoanTranche,
              loanProgram: debtCriteria.loanProgram,
              minLoanTerm: debtCriteria.minLoanTerm,
              maxLoanTerm: debtCriteria.maxLoanTerm,
              amortization: debtCriteria.amortization,
              // Update timestamp
              updatedAt: new Date()
            });
          }
        }
      }

      // Update investment criteria equity if provided
      if (body.investmentCriteriaEquity && Array.isArray(body.investmentCriteriaEquity)) {
        for (const equityCriteria of body.investmentCriteriaEquity) {
          if (equityCriteria.investmentCriteriaEquityId) {
            await investmentCriteriaEquityRepository.update(equityCriteria.investmentCriteriaEquityId, {
              targetReturn: equityCriteria.targetReturn,
              minimumInternalRateOfReturn: equityCriteria.minimumInternalRateOfReturn,
              minimumYieldOnCost: equityCriteria.minimumYieldOnCost,
              minimumEquityMultiple: equityCriteria.minimumEquityMultiple,
              targetCashOnCashMin: equityCriteria.targetCashOnCashMin,
              minHoldPeriodYears: equityCriteria.minHoldPeriodYears,
              maxHoldPeriodYears: equityCriteria.maxHoldPeriodYears,
              ownershipRequirement: equityCriteria.ownershipRequirement,
              attachmentPoint: equityCriteria.attachmentPoint,
              maxLeverageTolerance: equityCriteria.maxLeverageTolerance,
              typicalClosingTimelineDays: equityCriteria.typicalClosingTimelineDays,
              proofOfFundsRequirement: equityCriteria.proofOfFundsRequirement,
              notes: equityCriteria.notes,
              equityProgramOverview: equityCriteria.equityProgramOverview,
              occupancyRequirements: equityCriteria.occupancyRequirements,
              // NEW FIELDS - Moved from DealsV2
              yieldOnCost: equityCriteria.yieldOnCost,
              targetReturnIrrOnEquity: equityCriteria.targetReturnIrrOnEquity,
              equityMultiple: equityCriteria.equityMultiple,
              positionSpecificIrr: equityCriteria.positionSpecificIrr,
              positionSpecificEquityMultiple: equityCriteria.positionSpecificEquityMultiple,
              // Update timestamp
              updatedAt: new Date()
            });
          }
        }
      }

      // Update investment criteria capital positions if provided using raw SQL
      if (body.investmentCriteria && Array.isArray(body.investmentCriteria)) {
        for (const criteria of body.investmentCriteria) {
          if (criteria.investment_criteria_id) {
            await queryRunner.query(`
              UPDATE investment_criteria_central 
              SET capital_position = $1, updated_at = NOW()
              WHERE investment_criteria_id = $2
            `, [
              criteria.capitalPosition ? `{${criteria.capitalPosition}}` : null,
              criteria.investment_criteria_id
            ]);
          }
        }
      }

      await queryRunner.commitTransaction();

      // Fetch updated deal to return
      const updatedDeal = await dealsRepository.findOne({
        where: { dealId },
        relations: ['property', 'property.owner']
      });

      return NextResponse.json({
        success: true,
        deal: updatedDeal,
        message: "Deal updated successfully"
      });

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }

  } catch (error) {
    console.error("Error updating deal v2:", error);
    return NextResponse.json(
      { 
        error: "Failed to update deal v2",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

async function deleteDealHandler(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse> {
  try {
    const { id } = await params;
    const dealId = parseInt(id);

    console.log("Deleting deal v2 with ID:", dealId);

    if (isNaN(dealId)) {
      return NextResponse.json({ error: "Invalid deal ID" }, { status: 400 });
    }

    // Get repositories from the service
    const dealsRepository = typeORMService.getDealsRepository();
    const propertyRepository = typeORMService.getPropertyRepository();
    const nsfRepository = typeORMService.getNsfRepository();

    // Check if deal exists
    const existingDeal = await dealsRepository.findOne({
      where: { dealId },
      relations: ['property', 'nsfFields']
    });

    if (!existingDeal) {
      return NextResponse.json({ error: "Deal not found" }, { status: 404 });
    }

    // Note: We're keeping the property intact - not deleting it

    // Delete related NSF fields
    const nsfFields = await nsfRepository.find({
      where: { deal: { dealId } }
    });
    
    if (nsfFields.length > 0) {
      await nsfRepository.remove(nsfFields);
    }

    // Delete related investment criteria using raw SQL
    // Use 'deal_v2' entity type for V2 deals to distinguish from regular deals
    const investmentCriteriaResult = await typeORMService.getDataSource().query(`
      SELECT investment_criteria_id FROM investment_criteria_central 
      WHERE entity_type = 'deal_v2' AND entity_id = $1
    `, [dealId.toString()]);

    if (investmentCriteriaResult.length > 0) {
      const criteriaId = investmentCriteriaResult[0].investment_criteria_id;
      
      // Delete debt criteria
      await typeORMService.getDataSource().query(`
        DELETE FROM investment_criteria_debt WHERE investment_criteria_id = $1
      `, [criteriaId]);
      
      // Delete equity criteria
      await typeORMService.getDataSource().query(`
        DELETE FROM investment_criteria_equity WHERE investment_criteria_id = $1
      `, [criteriaId]);
      
      // Delete the investment criteria record
      await typeORMService.getDataSource().query(`
        DELETE FROM investment_criteria_central WHERE investment_criteria_id = $1
      `, [criteriaId]);
    }

    // Delete related deal contacts (V2 deals)
    await typeORMService.getDataSource().query(`
      DELETE FROM deal_contacts WHERE deal_v2_id = $1
    `, [dealId]);

    // Delete related file relationships for V2 deals
    await typeORMService.getDataSource().query(`
      DELETE FROM file_relationships 
      WHERE target_table_name = 'dealsv2' 
      AND target_column_name = 'deal_id' 
      AND target_row_id = $1
    `, [dealId.toString()]);

    // Delete the deal itself
    await dealsRepository.delete(dealId);

    return NextResponse.json({
      success: true,
      message: "Deal v2 deleted successfully"
    });

  } catch (error) {
    console.error("Error deleting deal v2:", error);
    return NextResponse.json(
      { 
        error: "Failed to delete deal v2",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// Export the handlers wrapped with TypeORM middleware
export const GET = withTypeORMHandler((request: NextRequest) => 
  getDealDetailHandler(request, { params: Promise.resolve({ id: request.nextUrl.pathname.split('/').pop() || '' }) })
);

export const PUT = withTypeORMHandler((request: NextRequest) => 
  updateDealHandler(request, { params: Promise.resolve({ id: request.nextUrl.pathname.split('/').pop() || '' }) })
);

export const DELETE = withTypeORMHandler((request: NextRequest) => 
  deleteDealHandler(request, { params: Promise.resolve({ id: request.nextUrl.pathname.split('/').pop() || '' }) })
); 