import { NextRequest, NextResponse } from "next/server";
import { typeORMService } from '@/lib/typeorm/service';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id: dealIdStr } = params;
    const dealId = parseInt(dealIdStr);
    if (isNaN(dealId)) {
      return NextResponse.json({ error: 'Invalid deal ID' }, { status: 400 });
    }

    const { items } = await request.json();

    if (!items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { error: "Items array is required" },
        { status: 400 }
      );
    }

    // Initialize TypeORM service if not already initialized
    if (!typeORMService.getInitialized()) {
      await typeORMService.initialize();
    }
    
    const nsfRepository = typeORMService.getNsfRepository();

    // Validate all items first
    const validationErrors: string[] = [];
    const sourceTypes = new Set<string>();
    const useTypes = new Set<string>();

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      
      // Validate required fields
      if (!item.nsfContext) {
        validationErrors.push(`Item ${i + 1}: nsfContext is required`);
        continue;
      }

      if (item.nsfContext === 'sources' && !item.sourceType) {
        validationErrors.push(`Item ${i + 1}: sourceType is required for sources`);
        continue;
      }

      if (item.nsfContext === 'uses_total' && !item.useType) {
        validationErrors.push(`Item ${i + 1}: useType is required for uses`);
        continue;
      }

      // Check for duplicates within the batch
      if (item.nsfContext === 'sources') {
        if (sourceTypes.has(item.sourceType)) {
          validationErrors.push(`Item ${i + 1}: Duplicate source type "${item.sourceType}" in batch`);
        } else {
          sourceTypes.add(item.sourceType);
        }
      } else if (item.nsfContext === 'uses_total') {
        if (useTypes.has(item.useType)) {
          validationErrors.push(`Item ${i + 1}: Duplicate use type "${item.useType}" in batch`);
        } else {
          useTypes.add(item.useType);
        }
      }
    }

    // Check for existing duplicates in database
    for (const sourceType of sourceTypes) {
      const existingSource = await nsfRepository.findOne({
        where: { dealId, nsfContext: 'sources', sourceType: sourceType }
      });
      
      if (existingSource) {
        validationErrors.push(`Source type "${sourceType}" already exists for this deal`);
      }
    }

    for (const useType of useTypes) {
      const existingUse = await nsfRepository.findOne({
        where: { dealId, nsfContext: 'uses_total', useType: useType }
      });
      
      if (existingUse) {
        validationErrors.push(`Use type "${useType}" already exists for this deal`);
      }
    }

    if (validationErrors.length > 0) {
      return NextResponse.json(
        { 
          error: "Validation failed",
          details: validationErrors
        },
        { status: 400 }
      );
    }

    // Start transaction for batch creation
    const queryRunner = typeORMService.getDataSource().createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const createdNsfFields: any[] = [];
      const investmentCriteriaToCreate: Array<{sourceType: string, isDebt: boolean}> = [];

      // Calculate totals for percentage calculation
      const totalSources = items
        .filter(item => item.nsfContext === 'sources')
        .reduce((sum, item) => sum + (item.amount || 0), 0);
      
      const totalUses = items
        .filter(item => item.nsfContext === 'uses_total')
        .reduce((sum, item) => sum + (item.amount || 0), 0);

      // Get existing NSF fields to include in percentage calculation
      const existingNsfFields = await nsfRepository.find({
        where: { dealId }
      });
      
      const existingSourcesTotal = existingNsfFields
        .filter(nsf => nsf.nsfContext === 'sources')
        .reduce((sum, nsf) => sum + (nsf.amount || 0), 0);
      
      const existingUsesTotal = existingNsfFields
        .filter(nsf => nsf.nsfContext === 'uses_total')
        .reduce((sum, nsf) => sum + (nsf.amount || 0), 0);

      const finalSourcesTotal = existingSourcesTotal + totalSources;
      const finalUsesTotal = existingUsesTotal + totalUses;

      // Calculate totals with Common Equity exclusion logic for sources
      let adjustedSourcesTotal = finalSourcesTotal;
      if (totalSources > 0) {
        // Get all source items (existing + new)
        const allSourceItems = [
          ...existingNsfFields.filter(nsf => nsf.nsfContext === 'sources'),
          ...items.filter(item => item.nsfContext === 'sources')
        ];
        
        // Check if LP or GP are present
        const hasLP = allSourceItems.some(nsf => 
          (nsf.sourceType || '').includes('Limited Partner (LP)') || 
          (nsf.sourceType || '').includes('LP')
        );
        const hasGP = allSourceItems.some(nsf => 
          (nsf.sourceType || '').includes('General Partner (GP)') || 
          (nsf.sourceType || '').includes('GP')
        );
        
        // If LP/GP present, recalculate total excluding Common Equity
        if (hasLP || hasGP) {
          adjustedSourcesTotal = allSourceItems.reduce((sum, nsf) => {
            const amount = parseFloat(nsf.amount?.toString() || '0') || 0;
            const isCommonEquity = (nsf.sourceType || '') === 'Common Equity';
            
            // Exclude Common Equity from total if LP/GP present
            if (isCommonEquity) {
              return sum; // Don't add Common Equity to total
            }
            
            return sum + amount;
          }, 0);
        }
      }

      // Create all NSF fields with auto-calculated percentages
      for (const item of items) {
        // Auto-calculate percentage based on context
        let autoPercentage = 0;
        if (item.nsfContext === 'sources' && adjustedSourcesTotal > 0) {
          const isCommonEquity = (item.sourceType || '') === 'Common Equity';
          // For Common Equity, percentage is 0% when LP/GP present
          if (isCommonEquity && adjustedSourcesTotal !== finalSourcesTotal) {
            autoPercentage = 0;
          } else {
            autoPercentage = (item.amount || 0) / adjustedSourcesTotal;
          }
        } else if (item.nsfContext === 'uses_total' && finalUsesTotal > 0) {
          autoPercentage = (item.amount || 0) / finalUsesTotal;
        }

        const nsfField = nsfRepository.create({
          dealId,
          nsfContext: item.nsfContext,
          capitalPosition: item.sourceType || item.useType || null,
          sourceType: item.sourceType || null,
          useType: item.useType || null,
          dealType: item.dealType || (item.sourceType ? 'debt' : 'equity'),
          amount: item.amount || 0,
          amountPerGsf: item.amountPerGsf || 0,
          amountPerNsf: item.amountPerNsf || 0,
          amountPerZfa: item.amountPerZfa || 0,
          percentageOfTotal: autoPercentage, // Use auto-calculated percentage
          isRequired: false,
          additionalInfo: item.additionalInfo || {}
        });

        const savedNsfField = await queryRunner.manager.save(nsfField);
        createdNsfFields.push(savedNsfField);

        // Prepare investment criteria for sources
        if (item.nsfContext === 'sources' && item.sourceType) {
          const isDebt = item.sourceType.toLowerCase().includes('debt') || 
                        item.sourceType.toLowerCase().includes('loan') || 
                        item.sourceType.toLowerCase().includes('mezzanine');
          
          investmentCriteriaToCreate.push({
            sourceType: item.sourceType,
            isDebt
          });
        }
      }

      // Create investment criteria for all sources
      for (const criteria of investmentCriteriaToCreate) {
        try {
          // Create investment criteria central entry
          const centralCriteriaResult = await queryRunner.query(`
            INSERT INTO investment_criteria_central 
            (entity_type, entity_id, capital_position, created_at, updated_at)
            VALUES ($1, $2, $3, NOW(), NOW())
            RETURNING investment_criteria_id
          `, ['deal_v2', dealId, criteria.sourceType]);
          
          const investmentCriteriaId = centralCriteriaResult[0].investment_criteria_id;
          
          // Create the appropriate criteria type
          if (criteria.isDebt) {
            await queryRunner.query(`
              INSERT INTO investment_criteria_debt 
              (investment_criteria_id, created_at, updated_at)
              VALUES ($1, NOW(), NOW())
            `, [investmentCriteriaId]);
          } else {
            await queryRunner.query(`
              INSERT INTO investment_criteria_equity 
              (investment_criteria_id, created_at, updated_at)
              VALUES ($1, NOW(), NOW())
            `, [investmentCriteriaId]);
          }
          
          console.log(`Created investment criteria for ${criteria.sourceType} (${criteria.isDebt ? 'debt' : 'equity'})`);
        } catch (icError) {
          console.warn(`Failed to create investment criteria for ${criteria.sourceType}:`, icError);
          // Continue with other criteria creation
        }
      }

      // Update percentages for existing NSF fields
      if (totalSources > 0 || totalUses > 0) {
        // Update existing source percentages (with Common Equity exclusion logic)
        if (adjustedSourcesTotal > 0) {
          const existingSources = existingNsfFields.filter(nsf => nsf.nsfContext === 'sources');
          for (const existingSource of existingSources) {
            const isCommonEquity = (existingSource.sourceType || '') === 'Common Equity';
            let newPercentage = 0;
            
            // For Common Equity, percentage is 0% when LP/GP present
            if (isCommonEquity && adjustedSourcesTotal !== finalSourcesTotal) {
              newPercentage = 0;
            } else {
              newPercentage = (existingSource.amount || 0) / adjustedSourcesTotal;
            }
            
            await queryRunner.manager.update('DealNsfField', existingSource.id, {
              percentageOfTotal: newPercentage
            });
          }
        }

        // Update existing use percentages
        if (finalUsesTotal > 0) {
          const existingUses = existingNsfFields.filter(nsf => nsf.nsfContext === 'uses_total');
          for (const existingUse of existingUses) {
            const newPercentage = (existingUse.amount || 0) / finalUsesTotal;
            await queryRunner.manager.update('DealNsfField', existingUse.id, {
              percentageOfTotal: newPercentage
            });
          }
        }
      }

      await queryRunner.commitTransaction();

      return NextResponse.json({
        success: true,
        message: `Successfully created ${createdNsfFields.length} NSF fields with investment criteria`,
        data: createdNsfFields,
        investmentCriteriaCreated: investmentCriteriaToCreate.length
      });

    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }

  } catch (error) {
    console.error("Error in batch NSF fields creation:", error);
    return NextResponse.json(
      { 
        error: "Failed to create NSF fields",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
