import { NextRequest, NextResponse } from 'next/server'
import { pool } from '@/lib/db'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: companyId } = await params

    if (!companyId) {
      return NextResponse.json(
        { error: 'Company ID is required' },
        { status: 400 }
      )
    }

    const query = `
      SELECT 
        ic.investment_criteria_id,
        ic.capital_position,
        ic.minimum_deal_size,
        ic.maximum_deal_size,
        ic.country,
        ic.region,
        ic.state,
        ic.city,
        ic.property_types,
        ic.property_subcategories,
        ic.strategies,
        ic.decision_making_process,
        ic.notes,
        ic.created_at,
        ic.updated_at,
        ic.investment_criteria_debt_id,
        ic.investment_criteria_equity_id,
        
        -- Debt-specific fields
        id.loan_type,
        id.loan_program,
        id.structured_loan_tranche,
        id.loan_interest_rate,
        id.loan_interest_rate_based_off_sofr,
        id.loan_interest_rate_based_off_wsj,
        id.loan_interest_rate_based_off_prime,
        id.loan_interest_rate_based_off_3yt,
        id.loan_interest_rate_based_off_5yt,
        id.loan_interest_rate_based_off_10yt,
        id.loan_interest_rate_based_off_30yt,
        id.rate_lock,
        id.rate_type,
        id.loan_to_value_max,
        id.loan_to_value_min,
        id.loan_to_cost_min,
        id.loan_to_cost_max,
        id.min_loan_term,
        id.max_loan_term,
        id.recourse_loan,
        id.min_loan_dscr,
        id.max_loan_dscr,
        id.loan_min_debt_yield,
        id.prepayment,
        id.yield_maintenance,
        id.application_deposit,
        id.good_faith_deposit,
        id.loan_origination_max_fee,
        id.loan_origination_min_fee,
        id.loan_exit_min_fee,
        id.loan_exit_max_fee,
        id.amortization,
        id.closing_time,
        id.future_facilities,
        id.eligible_borrower,
        id.occupancy_requirements,
        id.lien_position,
        id.debt_program_overview,
        id.loan_type_normalized,
        id.notes as debt_notes,
        
        -- Equity-specific fields
        ie.target_return,
        ie.minimum_internal_rate_of_return,
        ie.minimum_yield_on_cost,
        ie.minimum_equity_multiple,
        ie.target_cash_on_cash_min,
        ie.min_hold_period_years,
        ie.max_hold_period_years,
        ie.ownership_requirement,
        ie.attachment_point,
        ie.max_leverage_tolerance,
        ie.typical_closing_timeline_days,
        ie.proof_of_funds_requirement,
        ie.equity_program_overview,
        ie.occupancy_requirements as equity_occupancy_requirements,
        ie.notes as equity_notes,
        ie.yield_on_cost,
        ie.target_return_irr_on_equity,
        ie.equity_multiple,
        ie.position_specific_irr,
        ie.position_specific_equity_multiple
      FROM investment_criteria_central ic
      LEFT JOIN investment_criteria_debt id ON ic.investment_criteria_debt_id = id.investment_criteria_debt_id
      LEFT JOIN investment_criteria_equity ie ON ic.investment_criteria_equity_id = ie.investment_criteria_equity_id
      WHERE ic.entity_id = $1 
        AND ic.entity_type = 'company'
      ORDER BY ic.created_at DESC
    `

    const result = await pool.query(query, [companyId])
    
    // console.log('Query result:', result.rows)
    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Error fetching company investment criteria:', error)
    return NextResponse.json(
      { error: 'Failed to fetch investment criteria', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    )
  }
}
