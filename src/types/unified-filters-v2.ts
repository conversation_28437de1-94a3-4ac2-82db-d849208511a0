export interface ContactUnifiedFiltersV2 {
  // Base pagination and sorting
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  searchTerm?: string

  // Core Contact Table Filters
  source?: string[]
  emailStatus?: string[]
  jobTier?: string[]
  contactCountries?: string[]
  contactStates?: string[]
  contactCities?: string[]
  
  // Processing Status Filters
  emailVerificationStatus?: string[]
  contactEnrichmentV2Status?: string[]
  contactInvestmentCriteriaStatus?: string[]
  emailGenerationStatus?: string[]
  emailSendingStatus?: string[]
  
  // Boolean Processing Flags
  extracted?: boolean
  searched?: boolean
  emailGenerated?: boolean
  enriched?: boolean
  hasSmartleadId?: boolean
  
  // Contact Enrichment V2 Fields
  contactType?: string[]
  relationshipOwner?: string[]
  roleInDecisionMaking?: string[]
  sourceOfIntroduction?: string[]
  accreditedInvestorStatus?: boolean
  hasExecutiveSummary?: boolean
  hasCareerTimeline?: boolean
  hasAdditionalEmail?: boolean
  hasSecondaryPhone?: boolean
  
  // Social Media Filters
  hasTwitter?: boolean
  hasFacebook?: boolean
  hasInstagram?: boolean
  hasYoutube?: boolean
  
  // Education Filters (High School removed)
  educationCollege?: string[]
  educationCollegeYearGraduated?: string[]
  
  // Personal Details
  hasHonorableAchievements?: boolean
  hasHobbies?: boolean
  ageRange?: string[]
  hasContactAddress?: boolean
  hasContactZipCode?: boolean
  
  // Company Processor Flags (from company table)
  companyWebsiteScrapingStatus?: string[]
  companyOverviewV2Status?: string[]
  companyInvestmentCriteriaStatus?: string[]
  companyHasInvestmentCriteria?: boolean
  notCompanyWebsiteScrapingStatus?: string[]
  notCompanyOverviewV2Status?: string[]
  notCompanyInvestmentCriteriaStatus?: string[]
  
  // Investment Criteria Central - Deal Scope
  capitalPosition?: string[]
  dealSizeMin?: number
  dealSizeMax?: number
  
  // Investment Criteria Central - Geography
  regions?: string[]
  states?: string[]
  cities?: string[]
  countries?: string[]
  
  // Investment Criteria Central - Asset Strategy
  propertyTypes?: string[]
  propertySubcategories?: string[]
  strategies?: string[]
  decisionMakingProcess?: string[]
  
  // Investment Criteria Central - Additional Info
  investmentCriteriaNotes?: string[]
  
  // Investment Criteria Debt - Borrower & Closing
  closingTimeMin?: number
  closingTimeMax?: number
  eligibleBorrower?: string[]
  futureFacilities?: string[]
  occupancyRequirements?: string[]
  
  // Investment Criteria Debt - Covenants & Terms
  lienPosition?: string[]
  minLoanDscrMin?: number
  minLoanDscrMax?: number
  maxLoanDscrMin?: number
  maxLoanDscrMax?: number
  recourseLoan?: string[]
  loanMinDebtYield?: string[]
  prepayment?: string[]
  yieldMaintenance?: string[]
  
  // Investment Criteria Debt - Fees
  loanOriginationMaxFeeMin?: number
  loanOriginationMaxFeeMax?: number
  loanOriginationMinFeeMin?: number
  loanOriginationMinFeeMax?: number
  loanExitMinFeeMin?: number
  loanExitMinFeeMax?: number
  loanExitMaxFeeMin?: number
  loanExitMaxFeeMax?: number
  
  // Investment Criteria Debt - Interest Rate
  loanInterestRateSofrMin?: number
  loanInterestRateSofrMax?: number
  loanInterestRateWsjMin?: number
  loanInterestRateWsjMax?: number
  loanInterestRatePrimeMin?: number
  loanInterestRatePrimeMax?: number
  loanInterestRate3ytMin?: number
  loanInterestRate3ytMax?: number
  loanInterestRate5ytMin?: number
  loanInterestRate5ytMax?: number
  loanInterestRate10ytMin?: number
  loanInterestRate10ytMax?: number
  loanInterestRate30ytMin?: number
  loanInterestRate30ytMax?: number
  rateLock?: string[]
  rateType?: string[]
  
  // Investment Criteria Debt - Loan Sizing
  loanToValueMinMin?: number
  loanToValueMinMax?: number
  loanToValueMaxMin?: number
  loanToValueMaxMax?: number
  loanToCostMinMin?: number
  loanToCostMinMax?: number
  loanToCostMaxMin?: number
  loanToCostMaxMax?: number
  
  // Investment Criteria Debt - Program Detail
  loanTypes?: string[]
  loanTypeNormalized?: string[]
  structuredLoanTranche?: string[]
  loanProgram?: string[]
  
  // Investment Criteria Debt - Term & Amortization
  minLoanTermMin?: number
  minLoanTermMax?: number
  maxLoanTermMin?: number
  maxLoanTermMax?: number
  amortization?: string[]
  
  // Investment Criteria Equity - Target Returns
  targetReturnMin?: number
  targetReturnMax?: number
  minimumIrrMin?: number
  minimumIrrMax?: number
  minimumYieldOnCostMin?: number
  minimumYieldOnCostMax?: number
  targetCashOnCashMin?: number
  targetCashOnCashMax?: number
  
  // Investment Criteria Equity - Hold Period
  minHoldPeriodYearsMin?: number
  minHoldPeriodYearsMax?: number
  maxHoldPeriodYearsMin?: number
  maxHoldPeriodYearsMax?: number
  
  // Investment Criteria Equity - Ownership Control
  ownershipRequirement?: string[]
  maxLeverageToleranceMin?: number
  maxLeverageToleranceMax?: number
  
  // Gmail Outreach Filters
  hasBeenReachedOut?: boolean
  
  // NOT Filters for key fields
  notSource?: string[]
  notEmailStatus?: string[]
  notEmailVerificationStatus?: string[]
  notContactEnrichmentV2Status?: string[]
  notContactInvestmentCriteriaStatus?: string[]
  notEmailGenerationStatus?: string[]
  notEmailSendingStatus?: string[]
  notCapitalPosition?: string[]
  notPropertyTypes?: string[]
  notStrategies?: string[]
  notContactType?: string[]
  notLoanTypes?: string[]
  notStructuredLoanTranche?: string[]
  notLoanProgram?: string[]
  notRecourseLoan?: string[]
  notEligibleBorrower?: string[]
  notLienPosition?: string[]
  notOwnershipRequirement?: string[]
  notRateType?: string[]
  notAmortization?: string[]
  
  // New Filters
  notEmptyEmail?: boolean
  contactIds?: string[]
  contactEmails?: string[]
}

export interface CompanyUnifiedFiltersV2 {
  // Base pagination and sorting
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  searchTerm?: string

  // Core Company Table Filters
  companyAddress?: string[]
  companyCity?: string[]
  companyState?: string[]
  companyWebsite?: string[]
  industry?: string[]
  companyCountry?: string[]
  source?: string[]
  
  // Company Processing Status Filters
  websiteScrapingStatus?: string[]
  companyOverviewStatus?: string[]
  overviewV2Status?: string[]
  investmentCriteriaStatus?: string[]
  
  // Company Overview V2 Fields
  companyType?: string[]
  foundedYearMin?: number
  foundedYearMax?: number
  
  // Investment & Strategy V2 Filters
  investmentFocus?: string[]
  
  // Contact Information V2 Filters
  hasMainPhone?: boolean
  hasSecondaryPhone?: boolean
  hasMainEmail?: boolean
  hasSecondaryEmail?: boolean
  hasCompanyLinkedin?: boolean
  hasTwitter?: boolean
  hasFacebook?: boolean
  hasInstagram?: boolean
  hasYoutube?: boolean
  
  // Location V2 Filters
  headquartersAddress?: string[]
  headquartersCity?: string[]
  headquartersState?: string[]
  headquartersZipcode?: string[]
  headquartersCountry?: string[]
  additionalAddress?: string[]
  additionalCity?: string[]
  additionalState?: string[]
  additionalZipcode?: string[]
  additionalCountry?: string[]
  officeLocations?: string[]
  
  // Financial Metrics V2 Filters
  fundSizeMin?: number
  fundSizeMax?: number
  aumMin?: number
  aumMax?: number
  numberOfPropertiesMin?: number
  numberOfPropertiesMax?: number
  numberOfOfficesMin?: number
  numberOfOfficesMax?: number
  numberOfEmployeesMin?: number
  numberOfEmployeesMax?: number
  annualRevenueMin?: number
  annualRevenueMax?: number
  netIncomeMin?: number
  netIncomeMax?: number
  ebitdaMin?: number
  ebitdaMax?: number
  profitMarginMin?: number
  profitMarginMax?: number
  marketCapitalizationMin?: number
  marketCapitalizationMax?: number
  marketSharePercentageMin?: number
  marketSharePercentageMax?: number
  
  // Financial Information V2 Filters
  balanceSheetStrength?: string[]
  fundingSources?: string[]
  recentCapitalRaises?: string[]
  typicalDebtToEquityRatioMin?: number
  typicalDebtToEquityRatioMax?: number
  developmentFeeStructure?: string[]
  creditRating?: string[]
  dryPowderMin?: number
  dryPowderMax?: number
  annualDeploymentTargetMin?: number
  annualDeploymentTargetMax?: number
  
  // Investment & Fund Information V2 Filters
  investmentVehicleType?: string[]
  activeFundNameSeries?: string[]
  fundSizeActiveFundMin?: number
  fundSizeActiveFundMax?: number
  fundraisingStatus?: string[]
  lenderType?: string[]
  annualLoanVolumeMin?: number
  annualLoanVolumeMax?: number
  lendingOrigin?: string[]
  portfolioHealth?: string[]
  
  // Partnership & Leadership V2 Filters
  partnerships?: string[]
  keyEquityPartners?: string[]
  keyDebtPartners?: string[]
  boardOfDirectors?: string[]
  keyExecutives?: string[]
  founderBackground?: string[]
  
  // Market Positioning & Strategy V2 Filters
  sustainabilityEsgFocus?: boolean
  technologyProptechAdoption?: boolean
  adaptiveReuseExperience?: boolean
  regulatoryZoningExpertise?: boolean
  
  // Corporate Structure V2 Filters
  corporateStructure?: string[]
  parentCompany?: string[]
  subsidiaries?: string[]
  stockTickerSymbol?: string[]
  stockExchange?: string[]
  
  // Business Information V2 Filters
  productsServicesDescription?: string[]
  targetCustomerProfile?: string[]
  majorCompetitors?: string[]
  uniqueSellingProposition?: string[]
  industryAwardsRecognitions?: string[]
  hasCompanyHistory?: boolean
  
  // Transaction & Portfolio Data V2 Filters
  transactionsCompletedLast12mMin?: number
  transactionsCompletedLast12mMax?: number
  totalTransactionVolumeYtdMin?: number
  totalTransactionVolumeYtdMax?: number
  dealCountYtdMin?: number
  dealCountYtdMax?: number
  averageDealSizeMin?: number
  averageDealSizeMax?: number
  portfolioSizeSqftMin?: number
  portfolioSizeSqftMax?: number
  portfolioAssetCountMin?: number
  portfolioAssetCountMax?: number
  roleInPreviousDeal?: string[]
  
  // Relationship & Pipeline Data V2 Filters
  internalRelationshipManager?: string[]
  lastContactDate?: string
  pipelineStatus?: string[]
  recentNewsSentiment?: string[]
  
  // Data Quality & Processing V2 Filters
  dataSource?: string[]
  dataConfidenceScoreMin?: number
  dataConfidenceScoreMax?: number
  hasQuarterlyEarningsLink?: boolean
  
  // Investment Criteria Central - Deal Scope
  capitalPosition?: string[]
  dealSizeMin?: number
  dealSizeMax?: number
  
  // Investment Criteria Central - Geography
  regions?: string[]
  states?: string[]
  cities?: string[]
  countries?: string[]
  
  // Investment Criteria Central - Asset Strategy
  propertyTypes?: string[]
  propertySubcategories?: string[]
  strategies?: string[]
  decisionMakingProcess?: string[]
  
  // Investment Criteria Central - Additional Info
  investmentCriteriaNotes?: string[]
  
  // Investment Criteria Debt - Borrower & Closing
  closingTimeMin?: number
  closingTimeMax?: number
  eligibleBorrower?: string[]
  futureFacilities?: string[]
  occupancyRequirements?: string[]
  
  // Investment Criteria Debt - Covenants & Terms
  lienPosition?: string[]
  minLoanDscrMin?: number
  minLoanDscrMax?: number
  maxLoanDscrMin?: number
  maxLoanDscrMax?: number
  recourseLoan?: string[]
  loanMinDebtYield?: string[]
  prepayment?: string[]
  yieldMaintenance?: string[]
  
  // Investment Criteria Debt - Fees
  loanOriginationMaxFeeMin?: number
  loanOriginationMaxFeeMax?: number
  loanOriginationMinFeeMin?: number
  loanOriginationMinFeeMax?: number
  loanExitMinFeeMin?: number
  loanExitMinFeeMax?: number
  loanExitMaxFeeMin?: number
  loanExitMaxFeeMax?: number
  
  // Investment Criteria Debt - Interest Rate
  loanInterestRateSofrMin?: number
  loanInterestRateSofrMax?: number
  loanInterestRateWsjMin?: number
  loanInterestRateWsjMax?: number
  loanInterestRatePrimeMin?: number
  loanInterestRatePrimeMax?: number
  loanInterestRate3ytMin?: number
  loanInterestRate3ytMax?: number
  loanInterestRate5ytMin?: number
  loanInterestRate5ytMax?: number
  loanInterestRate10ytMin?: number
  loanInterestRate10ytMax?: number
  loanInterestRate30ytMin?: number
  loanInterestRate30ytMax?: number
  rateLock?: string[]
  rateType?: string[]
  
  // Investment Criteria Debt - Loan Sizing
  loanToValueMinMin?: number
  loanToValueMinMax?: number
  loanToValueMaxMin?: number
  loanToValueMaxMax?: number
  loanToCostMinMin?: number
  loanToCostMinMax?: number
  loanToCostMaxMin?: number
  loanToCostMaxMax?: number
  
  // Investment Criteria Debt - Program Detail
  loanTypes?: string[]
  loanTypeNormalized?: string[]
  structuredLoanTranche?: string[]
  loanProgram?: string[]
  
  // Investment Criteria Debt - Term & Amortization
  minLoanTermMin?: number
  minLoanTermMax?: number
  maxLoanTermMin?: number
  maxLoanTermMax?: number
  amortization?: string[]
  
  // Investment Criteria Equity - Target Returns
  targetReturnMin?: number
  targetReturnMax?: number
  minimumIrrMin?: number
  minimumIrrMax?: number
  minimumYieldOnCostMin?: number
  minimumYieldOnCostMax?: number
  targetCashOnCashMin?: number
  targetCashOnCashMax?: number
  
  // Investment Criteria Equity - Hold Period
  minHoldPeriodYearsMin?: number
  minHoldPeriodYearsMax?: number
  maxHoldPeriodYearsMin?: number
  maxHoldPeriodYearsMax?: number
  
  // Investment Criteria Equity - Ownership Control
  ownershipRequirement?: string[]
  maxLeverageToleranceMin?: number
  maxLeverageToleranceMax?: number
  
  // Contact Processor Flags (from related contacts)
  hasContacts?: boolean
  contactsEmailVerificationStatus?: string[]
  contactsEnrichmentStatus?: string[]
  contactsEnrichmentV2Status?: string[]
  contactsEmailGenerationStatus?: string[]
  contactsEmailSendingStatus?: string[]
  
  // NOT Filters for key fields
  notSource?: string[]
  notIndustry?: string[]
  notCompanyType?: string[]
  notWebsiteScrapingStatus?: string[]
  notCompanyOverviewStatus?: string[]
  notOverviewV2Status?: string[]
  notInvestmentCriteriaStatus?: string[]
  notContactsEmailVerificationStatus?: string[]
  notContactsEnrichmentStatus?: string[]
  notContactsEnrichmentV2Status?: string[]
  notContactsEmailGenerationStatus?: string[]
  notContactsEmailSendingStatus?: string[]
  notInvestmentFocus?: string[]
  notCapitalPosition?: string[]
  notPropertyTypes?: string[]
  notStrategies?: string[]
  notPartnerships?: string[]
  notFundraisingStatus?: string[]
  notLenderType?: string[]
  notLoanTypes?: string[]
  notStructuredLoanTranche?: string[]
  notLoanProgram?: string[]
  notRecourseLoan?: string[]
  notEligibleBorrower?: string[]
  notLienPosition?: string[]
  notOwnershipRequirement?: string[]
  notRateType?: string[]
  notAmortization?: string[]
  
  // New Filters
  notEmptyCompanyWebsite?: boolean
  companyIds?: string[]
  companyWebsites?: string[]
}
